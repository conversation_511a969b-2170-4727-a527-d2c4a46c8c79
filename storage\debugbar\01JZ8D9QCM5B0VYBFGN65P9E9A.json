{"__meta": {"id": "01JZ8D9QCM5B0VYBFGN65P9E9A", "datetime": "2025-07-03 15:08:20", "utime": **********.757475, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.081643, "end": **********.757487, "duration": 0.6758439540863037, "duration_str": "676ms", "measures": [{"label": "Booting", "start": **********.081643, "relative_start": 0, "end": **********.328925, "relative_end": **********.328925, "duration": 0.*****************, "duration_str": "247ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.328952, "relative_start": 0.*****************, "end": **********.757489, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "429ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.339367, "relative_start": 0.****************, "end": **********.343251, "relative_end": **********.343251, "duration": 0.0038840770721435547, "duration_str": "3.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend.home", "start": **********.577809, "relative_start": 0.****************, "end": **********.577809, "relative_end": **********.577809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.599282, "relative_start": 0.****************, "end": **********.599282, "relative_end": **********.599282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.604992, "relative_start": 0.****************, "end": **********.604992, "relative_end": **********.604992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.611473, "relative_start": 0.5298299789428711, "end": **********.611473, "relative_end": **********.611473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.618299, "relative_start": 0.5366559028625488, "end": **********.618299, "relative_end": **********.618299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.623466, "relative_start": 0.5418229103088379, "end": **********.623466, "relative_end": **********.623466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.631777, "relative_start": 0.5501339435577393, "end": **********.631777, "relative_end": **********.631777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.637853, "relative_start": 0.5562098026275635, "end": **********.637853, "relative_end": **********.637853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.644014, "relative_start": 0.562370777130127, "end": **********.644014, "relative_end": **********.644014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.651209, "relative_start": 0.569566011428833, "end": **********.651209, "relative_end": **********.651209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.656954, "relative_start": 0.5753109455108643, "end": **********.656954, "relative_end": **********.656954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.663926, "relative_start": 0.5822827816009521, "end": **********.663926, "relative_end": **********.663926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.669728, "relative_start": 0.5880849361419678, "end": **********.669728, "relative_end": **********.669728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.674546, "relative_start": 0.5929028987884521, "end": **********.674546, "relative_end": **********.674546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.677233, "relative_start": 0.5955898761749268, "end": **********.677233, "relative_end": **********.677233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.680987, "relative_start": 0.5993437767028809, "end": **********.680987, "relative_end": **********.680987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.layouts.master", "start": **********.703016, "relative_start": 0.6213729381561279, "end": **********.703016, "relative_end": **********.703016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.include.header", "start": **********.70524, "relative_start": 0.6235969066619873, "end": **********.70524, "relative_end": **********.70524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.property-category", "start": **********.71805, "relative_start": 0.6364068984985352, "end": **********.71805, "relative_end": **********.71805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.include.footer", "start": **********.735468, "relative_start": 0.6538248062133789, "end": **********.735468, "relative_end": **********.735468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.script.landloardScript", "start": **********.752272, "relative_start": 0.6706287860870361, "end": **********.752272, "relative_end": **********.752272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 25239768, "peak_usage_str": "24MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Exception", "message": "Cannot add SymfonyMailCollector on Laravel Debugbar: Mailer [] is not defined.", "code": 0, "file": "vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php", "line": 740, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1633093152 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>534</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">addCollectorException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">Cannot add SymfonyMailCollector</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object InvalidArgumentException]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">boot</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">C:\\xampp\\htdocs\\landlord\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633093152\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["    protected function addCollectorException(string $message, Exception $exception)\n", "    {\n", "        $this->addThrowable(\n", "            new Exception(\n", "                $message . ' on Laravel Debugbar: ' . $exception->getMessage(),\n", "                $exception->getCode(),\n", "                $exception\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=740", "ajax": false, "filename": "LaravelDebugbar.php", "line": "740"}}, {"type": "InvalidArgumentException", "message": "Mailer [] is not defined.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Mail/MailManager.php", "line": 113, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-298479506 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>97</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"68 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>33</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>885</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Mail\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Mail\\MailServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>770</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">build</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>881</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>706</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>866</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1431</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>504</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">offsetGet</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">boot</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">C:\\xampp\\htdocs\\landlord\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298479506\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["        $config = $this->getConfig($name);\n", "\n", "        if (is_null($config)) {\n", "            throw new InvalidArgumentException(\"Mailer [{$name}] is not defined.\");\n", "        }\n", "\n", "        // Once we have created the mailer instance we will set a container instance\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FMail%2FMailManager.php&line=113", "ajax": false, "filename": "MailManager.php", "line": "113"}}]}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "landlord.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 21, "nb_templates": 21, "templates": [{"name": "frontend.home", "param_count": null, "params": [], "start": **********.577725, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/home.blade.phpfrontend.home", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.599197, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.604939, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.61141, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.618246, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.623413, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.631723, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.637801, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.643926, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.651144, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.656903, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.663874, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.669676, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.674493, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.677174, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.680931, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.layouts.master", "param_count": null, "params": [], "start": **********.702962, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/layouts/master.blade.phpfrontend.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "frontend.include.header", "param_count": null, "params": [], "start": **********.705187, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/include/header.blade.phpfrontend.include.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Finclude%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "components.property-category", "param_count": null, "params": [], "start": **********.717994, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/components/property-category.blade.phpcomponents.property-category", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fcomponents%2Fproperty-category.blade.php&line=1", "ajax": false, "filename": "property-category.blade.php", "line": "?"}}, {"name": "frontend.include.footer", "param_count": null, "params": [], "start": **********.735411, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/include/footer.blade.phpfrontend.include.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Finclude%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "frontend.script.landloardScript", "param_count": null, "params": [], "start": **********.752214, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/script/landloardScript.blade.phpfrontend.script.landloardScript", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fscript%2FlandloardScript.blade.php&line=1", "ajax": false, "filename": "landloardScript.blade.php", "line": "?"}}]}, "queries": {"count": 146, "nb_statements": 146, "nb_visible_statements": 146, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08306000000000006, "accumulated_duration_str": "83.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 46 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select `id`, `title`, `highlight_title_one`, `btn_one`, `image_id`, `status` from `hero_sections` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.35483, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:61", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=61", "ajax": false, "filename": "HomeController.php", "line": "61"}, "connection": "landlord", "explain": null, "start_percent": 0, "width_percent": 0.542}, {"sql": "select `id`, `path` from `images` where `images`.`id` in (224, 225, 226)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3597949, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:61", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=61", "ajax": false, "filename": "HomeController.php", "line": "61"}, "connection": "landlord", "explain": null, "start_percent": 0.542, "width_percent": 1.096}, {"sql": "select * from `properties` where `status` = 1 order by RAND() limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 63}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.364322, "duration": 0.01107, "duration_str": "11.07ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:63", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=63", "ajax": false, "filename": "HomeController.php", "line": "63"}, "connection": "landlord", "explain": null, "start_percent": 1.637, "width_percent": 13.328}, {"sql": "select * from `images` where `images`.`id` = 95 limit 1", "type": "query", "params": [], "bindings": [95], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.379595, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 14.965, "width_percent": 0.65}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.382104, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 15.615, "width_percent": 0.482}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 11 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3855689, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 16.097, "width_percent": 0.674}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.387994, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 16.771, "width_percent": 0.614}, {"sql": "select * from `images` where `images`.`id` = 203 limit 1", "type": "query", "params": [], "bindings": [203], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3901942, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 17.385, "width_percent": 0.554}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3923361, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 17.939, "width_percent": 0.602}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 29 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.39577, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 18.541, "width_percent": 0.746}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.398074, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 19.287, "width_percent": 0.674}, {"sql": "select * from `images` where `images`.`id` = 137 limit 1", "type": "query", "params": [], "bindings": [137], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.40029, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 19.961, "width_percent": 0.566}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.402431, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 20.527, "width_percent": 0.566}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 18 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.404486, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 21.093, "width_percent": 0.638}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.406594, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 21.731, "width_percent": 0.638}, {"sql": "select * from `images` where `images`.`id` = 143 limit 1", "type": "query", "params": [], "bindings": [143], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.40903, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 22.369, "width_percent": 0.626}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.412877, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 22.995, "width_percent": 0.855}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 19 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.415383, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 23.85, "width_percent": 0.638}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.417537, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 24.488, "width_percent": 0.674}, {"sql": "select * from `images` where `images`.`id` = 77 limit 1", "type": "query", "params": [], "bindings": [77], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4197562, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 25.163, "width_percent": 0.53}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.421842, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 25.692, "width_percent": 0.578}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 8 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.423918, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 26.27, "width_percent": 0.626}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.426569, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 26.896, "width_percent": 0.879}, {"sql": "select * from `properties` where `status` = 1 order by RAND() limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 66}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.43013, "duration": 0.00449, "duration_str": "4.49ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:66", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=66", "ajax": false, "filename": "HomeController.php", "line": "66"}, "connection": "landlord", "explain": null, "start_percent": 27.775, "width_percent": 5.406}, {"sql": "select * from `images` where `images`.`id` = 47 limit 1", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.437575, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 33.181, "width_percent": 0.831}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.440753, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 34.012, "width_percent": 0.602}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 3 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.442981, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 34.614, "width_percent": 0.662}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.445863, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 35.276, "width_percent": 0.783}, {"sql": "select * from `images` where `images`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.448281, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 36.058, "width_percent": 0.337}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.450326, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 36.395, "width_percent": 0.482}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 22 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4523342, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 36.877, "width_percent": 0.59}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4544091, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 37.467, "width_percent": 0.566}, {"sql": "select * from `images` where `images`.`id` = 89 limit 1", "type": "query", "params": [], "bindings": [89], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.45648, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 38.033, "width_percent": 0.458}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.458539, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 38.49, "width_percent": 0.494}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 10 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4615788, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 38.984, "width_percent": 0.71}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.463785, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 39.694, "width_percent": 0.602}, {"sql": "select * from `images` where `images`.`id` = 131 limit 1", "type": "query", "params": [], "bindings": [131], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.466006, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 40.296, "width_percent": 0.47}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.468046, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 40.766, "width_percent": 0.518}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 17 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.470092, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 41.283, "width_percent": 0.53}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.472102, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 41.813, "width_percent": 0.602}, {"sql": "select * from `images` where `images`.`id` = 203 limit 1", "type": "query", "params": [], "bindings": [203], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.474221, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 42.415, "width_percent": 0.482}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.477082, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 42.897, "width_percent": 0.843}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 29 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.480418, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 43.739, "width_percent": 0.734}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.482718, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 44.474, "width_percent": 0.578}, {"sql": "select * from `properties` where `status` = 1 order by RAND() limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 69}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.484969, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:69", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=69", "ajax": false, "filename": "HomeController.php", "line": "69"}, "connection": "landlord", "explain": null, "start_percent": 45.052, "width_percent": 4.443}, {"sql": "select * from `images` where `images`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.491027, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 49.494, "width_percent": 0.59}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.494665, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 50.084, "width_percent": 1.072}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 22 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.498128, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 51.156, "width_percent": 0.698}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.500454, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 51.854, "width_percent": 0.698}, {"sql": "select * from `images` where `images`.`id` = 137 limit 1", "type": "query", "params": [], "bindings": [137], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5027652, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 52.552, "width_percent": 0.542}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.504974, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 53.094, "width_percent": 0.638}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 18 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.507174, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 53.732, "width_percent": 0.65}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5093849, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 54.382, "width_percent": 0.867}, {"sql": "select * from `images` where `images`.`id` = 71 limit 1", "type": "query", "params": [], "bindings": [71], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.512785, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 55.249, "width_percent": 0.566}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5154421, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 55.815, "width_percent": 0.758}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 7 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.519084, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 56.574, "width_percent": 1.336}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.522066, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 57.91, "width_percent": 0.59}, {"sql": "select * from `images` where `images`.`id` = 131 limit 1", "type": "query", "params": [], "bindings": [131], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.524247, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 58.5, "width_percent": 0.458}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.526306, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 58.957, "width_percent": 0.47}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 17 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.529277, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 59.427, "width_percent": 0.795}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5315711, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 60.222, "width_percent": 0.566}, {"sql": "select * from `images` where `images`.`id` = 101 limit 1", "type": "query", "params": [], "bindings": [101], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.53375, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 60.787, "width_percent": 0.445}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5358078, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 61.233, "width_percent": 0.482}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 12 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.53794, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 61.714, "width_percent": 0.686}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5402632, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 62.401, "width_percent": 0.566}, {"sql": "select * from `partners` where `status` = 1 limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.542638, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:72", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=72", "ajax": false, "filename": "HomeController.php", "line": "72"}, "connection": "landlord", "explain": null, "start_percent": 62.967, "width_percent": 0.542}, {"sql": "select * from `how_it_works` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.54754, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:75", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=75", "ajax": false, "filename": "HomeController.php", "line": "75"}, "connection": "landlord", "explain": null, "start_percent": 63.508, "width_percent": 0.831}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.550987, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 64.339, "width_percent": 0.65}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.553662, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 64.989, "width_percent": 0.458}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5559602, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 65.447, "width_percent": 0.337}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.558002, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 65.784, "width_percent": 0.349}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.561626, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 66.133, "width_percent": 1.096}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.565106, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 67.229, "width_percent": 0.686}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.5715768, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 67.915, "width_percent": 0.518}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.574557, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 68.432, "width_percent": 0.373}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.592447, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 68.806, "width_percent": 0.65}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.596633, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 69.456, "width_percent": 0.674}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.599877, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 70.13, "width_percent": 0.807}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.602761, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 70.937, "width_percent": 0.421}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.605334, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 71.358, "width_percent": 0.325}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.608393, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 71.683, "width_percent": 0.482}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.61221, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 72.165, "width_percent": 0.963}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.615483, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 73.128, "width_percent": 0.65}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.61874, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 73.778, "width_percent": 0.59}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.621292, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 74.368, "width_percent": 0.361}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6245382, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 74.729, "width_percent": 0.337}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6286361, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 75.066, "width_percent": 1.047}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6330762, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 76.114, "width_percent": 0.554}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6356459, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 76.667, "width_percent": 0.494}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.638991, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 77.161, "width_percent": 0.409}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6413789, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 77.57, "width_percent": 0.47}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6459, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 78.04, "width_percent": 0.807}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.648917, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 78.847, "width_percent": 0.518}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6523468, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 79.364, "width_percent": 0.458}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.654752, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 79.822, "width_percent": 0.506}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.658053, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 80.327, "width_percent": 0.409}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6615171, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 80.737, "width_percent": 0.59}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6650178, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 81.327, "width_percent": 0.482}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.667424, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 81.808, "width_percent": 0.506}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.671263, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 82.314, "width_percent": 0.578}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.673849, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 82.892, "width_percent": 0.554}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.675607, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 83.446, "width_percent": 0.482}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676544, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 83.927, "width_percent": 0.433}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.678852, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 84.361, "width_percent": 0.626}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.680167, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 84.987, "width_percent": 0.626}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.696903, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 85.613, "width_percent": 0.831}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.697854, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 86.444, "width_percent": 0.337}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6983829, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 86.781, "width_percent": 0.277}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.698832, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 87.058, "width_percent": 0.277}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699277, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 87.334, "width_percent": 0.277}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699713, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 87.611, "width_percent": 0.289}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.700141, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 87.9, "width_percent": 0.241}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.700535, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 88.141, "width_percent": 0.253}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7009208, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 88.394, "width_percent": 0.289}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.70134, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 88.683, "width_percent": 0.253}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7018318, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 88.936, "width_percent": 0.277}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.702587, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 89.213, "width_percent": 0.289}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.70366, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 89.502, "width_percent": 0.277}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.704143, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 89.778, "width_percent": 0.265}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.704828, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 90.043, "width_percent": 0.277}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.716235, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 90.32, "width_percent": 0.602}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.717307, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 90.922, "width_percent": 0.59}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.719048, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 91.512, "width_percent": 0.506}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.719705, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 92.018, "width_percent": 0.373}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.720227, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 92.391, "width_percent": 0.373}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.720859, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 92.764, "width_percent": 0.337}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721335, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 93.101, "width_percent": 0.421}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721872, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 93.523, "width_percent": 0.337}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.722429, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 93.86, "width_percent": 0.325}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.72286, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 94.185, "width_percent": 0.325}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7232878, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 94.51, "width_percent": 0.313}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.723815, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 94.823, "width_percent": 0.289}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7242138, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 95.112, "width_percent": 0.289}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.724613, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 95.401, "width_percent": 0.217}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7250519, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 95.618, "width_percent": 0.217}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7255251, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 95.834, "width_percent": 0.361}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7260041, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 96.196, "width_percent": 0.337}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7266161, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 96.533, "width_percent": 0.361}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7271461, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 96.894, "width_percent": 0.373}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7276099, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 97.267, "width_percent": 0.229}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7339551, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 97.496, "width_percent": 0.542}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.734991, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.038, "width_percent": 0.325}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7495592, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.363, "width_percent": 0.542}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.750217, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.904, "width_percent": 0.421}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7510462, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.326, "width_percent": 0.325}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.751781, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.651, "width_percent": 0.349}]}, "models": {"data": {"App\\Models\\Language": {"value": 63, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Image": {"value": 34, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FImage.php&line=1", "ajax": false, "filename": "Image.php", "line": "?"}}, "App\\Models\\Setting": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Property\\Property": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FProperty%2FProperty.php&line=1", "ajax": false, "filename": "Property.php", "line": "?"}}, "App\\Models\\Property\\PropertyCategory": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FProperty%2FPropertyCategory.php&line=1", "ajax": false, "filename": "PropertyCategory.php", "line": "?"}}, "App\\Models\\Property\\PropertyLocation": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FProperty%2FPropertyLocation.php&line=1", "ajax": false, "filename": "PropertyLocation.php", "line": "?"}}, "App\\Models\\Locations\\Country": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FLocations%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\Partner": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FPartner.php&line=1", "ajax": false, "filename": "Partner.php", "line": "?"}}, "App\\Models\\HowItWork": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FHowItWork.php&line=1", "ajax": false, "filename": "HowItWork.php", "line": "?"}}, "App\\Models\\HeroSection": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FHeroSection.php&line=1", "ajax": false, "filename": "HeroSection.php", "line": "?"}}}, "count": 195, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=58\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=58\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:58-92</a>", "middleware": "web", "duration": "700ms", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2046052272 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2046052272\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-787903581 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-787903581\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2135670420 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IkpxcDdVV2lkUEYwdGJqVXp2Mys5Vmc9PSIsInZhbHVlIjoiNEExeUNRQmdnQWpKQ3B5UlR6N29jYUFQUlhXYmJpaTFaUnRNT250MWdUS2laMnFBZFpqR0NlTURHcVVZeU5NVm0zSTdzN2p6OUxlMHlZYjE1Zjl2RnU3dldLWVJkWG15cVRuc05JMkE2NW1maHFOVXNqYVNzZTk5V3p5MjdZUnAiLCJtYWMiOiI5ZThlOWIxYTVjMmM4MWY1ZDRjM2Q5MWM4MGI5ZmEwNTIzZDgxZGIwMDcyZTE0NGY5N2YwZDE1NmRhMWFmOTcxIiwidGFnIjoiIn0%3D; landlord_session=eyJpdiI6IkZ6Tk05Z2FrU3oxVnV0NW5OMzRONFE9PSIsInZhbHVlIjoiSDAxTFk4a3FqOWV2NHdONmhvTW4vZ1l0UXd3QzBla0V0Nm00WnUyZlNsRW1LNEhVQXZ4ZEhrU2pSYnQrUisvZDdKVTc3aEVmYnNGTGVaUk1JMm5sSXk3V2hhaG9jVWtUUTVHSmZyeTFkSnhWM1V6c2h1bzJuVktOdDZFelN1WlAiLCJtYWMiOiJkNGFkMTVhZTg2ZmU0ZGU0NWQxYWYxZDI5NjQ3MGYxOTJlYTQ3ZDNkYjc2MTJhZmNhYjhkZWIxYWY5NzQzMGY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135670420\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-400534360 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zd8n8oVrnqq7VrOib2a0aR6Mg0x9iKyNiIzK3AUb</span>\"\n  \"<span class=sf-dump-key>landlord_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wjN97PJEcLLEIbZmnCzEPYaoZw3NxJJzOOvYVDV4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400534360\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1812222069 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 15:08:20 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812222069\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-197757975 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zd8n8oVrnqq7VrOib2a0aR6Mg0x9iKyNiIzK3AUb</span>\"\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JZ8D9PPV3Z41XMCFQDJ9NZCZ</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-197757975\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index"}, "badge": null}}