<?php

namespace App\Http\Controllers\Installer;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use App\Repositories\Installer\InstallRepository;

class InstallController extends Controller
{
    protected $installRepository, $request, $init, $path;

    public function __construct(InstallRepository $installRepository, Request $request)
    {
        $this->installRepository = $installRepository;
        $this->request = $request;
        $this->path = asset('/frontend/installer');
    }

    public function CheckEnvironment()
    {

        // WelcomeNote
        // CheckEnvironment
        // LicenseVerification
        // DatabaseSetup
        // AdminSetup
        // Complete
        $data['title'] = _trans('installer.Check Your Environment For Landlord Installation');
        $data['Server-Requirements'] = _trans('installer.Server Requirements');
        $data['Folder-Requirements'] = _trans('installer.Folder Requirements');
        $data['notify'] = _trans('installer.Please make sure that all the requirements are met before proceeding to the next step.');
        $data['success'] = _trans('installer.It looks like everything meets the requirements, Please click the button below to continue.');
        $data['asset_path'] = $this->path;
        $data['button_text'] = _trans('installer.Continue');

        // Set a session value
        $this->installRepository->checkStage('CheckEnvironment');

        $checks = $this->installRepository->getPreRequisite();
        $server_checks = $checks['server'];
        $folder_checks = $checks['folder'];
        $verifier = $checks['verifier'];
        $has_false = in_array(false, $checks);

        envu(['APP_ENV' => 'production']);
        $name = env('APP_NAME');

        return view('installer.install.preRequisite', compact('server_checks', 'folder_checks', 'name', 'verifier', 'has_false', 'data'));
    }

    public function license()
    {

        // WelcomeNote
        // CheckEnvironment
        // LicenseVerification
        // DatabaseSetup
        // AdminSetup
        // Complete

        $data['title'] = _trans('installer.License Verification');
        $data['Access-Code'] = _trans('installer.Access Code');
        $data['info'] = _trans('installer.Please enter your access code to verify your license.');
        $data['Envato-Email'] = _trans('installer.Envato Email');
        $data['Installed-Domain'] = _trans('installer.Installed Domain');
        $data['button_text'] = _trans('installer.Continue');

        $data['asset_path'] = $this->path;
        // Set a session value
        $this->installRepository->checkStage('LicenseVerification');

        $checks = $this->installRepository->getPreRequisite();
        if (in_array(false, $checks)) {
            return redirect()->route('service.checkEnvironment')->with(['message' => _trans('installer.requirement_failed'), 'status' => 'error']);
        }

        $reinstall = $this->installRepository->checkReinstall();
        return view('installer.install.license', compact('reinstall', 'data'));
    }

    public function post_license(Request $request)
    {
        // return $request;
        try {
            $response = $this->installRepository->validateLicense($request->all());
            if ($response && gv($response, 'goto')) {
                $message = __('We can not verify your credentials, Please wait');
                $goto = $response['goto'];
            } else {
                session()->flash('license', 'verified');
                $goto = route('service.database');
                $message = _trans('installer.valid_license');
                if (request('re_install') && $this->installRepository->checkReinstall()) {
                    Storage::disk('local')->put('.app_installed', Storage::disk('local')->get('.temp_app_installed'));
                    Storage::disk('local')->delete('.temp_app_installed');
                    Storage::disk('local')->put('.install_count', Storage::disk('local')->get('.install_count') + 1);
                    $goto = url('/');
                    $message = _trans('installer.Re-installation Process Complete');
                }
            }

            return response()->json(['message' => $message, 'goto' => $goto]);
        } catch (\Throwable$th) {
            return response()->json(['message' => $th->getMessage(), 'status' => 'error']);
        }
    }

    public function database()
    {

        $data['asset_path'] = $this->path;
        $data['title'] = _trans('installer.Check Database Setup and Connection');
        $data['button_text'] = _trans('installer.Continue');
        $data['DB HOST'] = _trans('installer.DB HOST');
        $data['DB PORT'] = _trans('installer.DB PORT');
        $data['DB DATABASE'] = _trans('installer.DB DATABASE');
        $data['DB USERNAME'] = _trans('installer.DB USERNAME');
        $data['DB PASSWORD'] = _trans('installer.DB PASSWORD');
        $data['Force Delete Previous Table'] = _trans('installer.Force Delete Previous Table');
        $data['button_text'] = _trans('installer.Continue');

        // WelcomeNote
        // CheckEnvironment
        // LicenseVerification
        // DatabaseSetup
        // AdminSetup
        // Complete

        // Set a session value
        session(['DatabaseSetup' => true]);
        Storage::disk('local')->put('.DatabaseSetup', 'DatabaseSetup');
        $ac = Storage::disk('local')->exists('.temp_app_installed') ? Storage::disk('local')->get('.temp_app_installed') : null;
        // if (!$ac) {
        //     abort(404);
        // }

        $data['asset_path'] = $this->path;
        $base_path = $this->path;

        return view('installer.install.database', compact('data', 'base_path'));
    }

    public function post_database(Request $request)
    {
        $this->installRepository->validateDatabase($request->all());
        return response()->json(['message' => _trans('installer.connection_established'), 'goto' => route('service.user')]);
    }

    public function done()
    {

        $data['asset_path'] = $this->path;
        $data['title'] = _trans('installer.Complete Installation and Configuration');
        $data['info'] = _trans('installer.Congratulations! You successfully installed the application. Please login to your account to start using the application.');


        $data['asset_path'] = $this->path;
        $base_path = $this->path;

        // WelcomeNote
        // CheckEnvironment
        // LicenseVerification
        // DatabaseSetup
        // AdminSetup
        // Complete

        // Set a session value
        session(['Complete' => true]);
        Storage::disk('local')->put('.Complete', 'Complete');

        $data['user'] = Storage::disk('local')->exists('.user_email') ? Storage::disk('local')->get('.user_email') : null;
        $data['pass'] = Storage::disk('local')->exists('.user_pass') ? Storage::disk('local')->get('.user_pass') : null;

        if ($data['user'] && $data['pass']) {
            Log::info('done');
            Storage::disk('local')->delete(['.user_email', '.user_pass']);
            Storage::disk('local')->put('.install_count', 1);

            return view('installer.install.done', compact('data', 'base_path'));
        } else {
            return view('installer.install.done', compact('data', 'base_path'));
        }

    }

    public function ManageAddOnsValidation(ModuleInstallRequest $request)
    {
        $response = $this->installRepository->installModule($request->all());
        if ($response) {
            if ($request->wantsJson()) {
                return response()->json(['message' => _trans('installer.module_verify'), 'reload' => true]);
            }
            Toastr::success(_trans('installer.module_verify'), 'Success');
        }
        return back();

    }

    public function uninstall()
    {
        $response = $this->installRepository->uninstall($this->request->all());
        $message = 'Uninstall by script author successfully';
        info($message);
        return response()->json(['message' => $message, 'response' => $response]);
    }

    public function installTheme(ThemeInstallRequest $request)
    {
        $this->installRepository->installTheme($request->all());
        $message = _trans('installer.theme_verify');
        if ($request->ajax()) {
            return response()->json(['message' => $message, 'reload' => true]);
        }

        Toastr::success($message);
        return redirect()->back();

    }

    public function reinstall(Request $request, $key)
    {

        if ($key == "sure") {

            $list = [
                '********',
                '.access_code',
                '.access_log',
                '.account_email',
                '.app_installed',
                '.install_count',
                '.logout',
            ];

            foreach ($list as $key => $value) {
                if (Storage::disk('local')->exists($value)) {
                    Storage::disk('local')->delete($value);
                }
            }

            return redirect('/');

        } else {
            abort(404);
        }
    }

    public function index()
    {
        $data['title'] = _trans('installer.Welcome To Installation');
        $data['short_note'] = _trans('installer.Thanks for choosing this installer, to complete the installation, please proceed to the next step!');
        $data['button_text'] = _trans('installer.Get Started');
        $data['asset_path'] = $this->path;

        // check stage & Set a session value
        $this->installRepository->checkStage('WelcomeNote');
        return view('installer.install.welcome', compact('data'));
    }

    public function AdminSetup()
    {


        $data['title'] = _trans('installer.Admin Setup');
        $data['asset_path'] = url('/frontend/installer');

        $this->installRepository->checkStage('AdminSetup');

        // WelcomeNote
        // CheckEnvironment
        // LicenseVerification
        // DatabaseSetup
        // AdminSetup
        // Complete

        // Set a session value



        return view('installer.install.user', compact('data'));
    }

    public function post_user(Request $request)
    {

        if ($request->all() == null) {
            $request['email'] = '<EMAIL>';
            $request['password'] = '123456789';
        }

        try {


            Artisan::call('db:seed', array('--force' => true));
            $user_info = [
                'email' => @$request->email,
                'password' => @$request->password_confirmation,
            ];

            $user = DB::table('users')->find(2);

            if (!$user) {

                DB::table('users')
                    ->insert([
                        'name' => 'Admin',
                        'email' => $params['email'],
                        'password' => Hash::make($request->password),
                        'role_id' => 2
                    ]);
            } else {
                DB::table('users')
                    ->where('id', 2 )
                    ->update([
                        'name' => 'Admin',
                        'email' => $request->email,
                        'password' => Hash::make($request->password),
                        'role_id' => 2
                    ]);

            }

        } catch (\Exception$e) {
            dd($e);
            \Log::info($e);
            return response()->json(['message' => _trans('installer.Installation done. You can now login.'), 'goto' => route('service.done')]);
        }

        return response()->json(['message' => _trans('installer.Installation done. You can now login.'), 'goto' => route('service.done')]);
    }

}
