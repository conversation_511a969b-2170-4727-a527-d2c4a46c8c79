<?php $__env->startSection('title'); ?>
    <?php echo e(@$data['title']); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>

    <div class="page-content">

        
        <div class="page-header">
            <div class="row">
                <div class="col-sm-6">
                    <h4 class="bradecrumb-title mb-1"><?php echo e($data['title']); ?></h1>
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(_trans('landlord.Home')); ?></a></li>
                        <li class="breadcrumb-item"><?php echo e($data['title']); ?></li>
                        <li class="breadcrumb-item"><?php echo e(_trans('landlord.Update')); ?></li>
                    </ol>
                </div>
            </div>
        </div>

        
        <div class="card ot-card">

            <div class="card-body">
                <form action="<?php echo e(route('home.section-titles.update')); ?>" enctype="multipart/form-data"
                    method="post" id="visitForm">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <!-- Process Flow for our business model -->
                            <div class="row mb-3 page-title-description">
                                <!-- title Head Start -->
                                <div class="section-title-head">
                                    <h3><?php echo e(_trans('landlord.Process Flow for our business model')); ?></h3>
                                    <hr>
                                </div>
                                <!-- title Head End -->
                                <div class="col-md-6 mb-3">
                                    <label for="business_model_title" class="form-label "><?php echo e(_trans('landlord.Title')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['business_model_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="business_model_title"
                                        list="datalistOptions" id="business_model_title"
                                        placeholder="<?php echo e(_trans('landlord.process flow for our business model')); ?>" value="<?php echo e(@$data['hometitles']->business_model_title); ?>">
                                    <?php $__errorArgs = ['business_model_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="business_model_description" class="form-label "><?php echo e(_trans('landlord.Short Description')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['business_model_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="business_model_description"
                                        list="datalistOptions" id="business_model_description"
                                        placeholder="<?php echo e(_trans('landlord.max 25 words recommended')); ?>" value="<?php echo e(@$data['hometitles']->business_model_description); ?>">
                                    <?php $__errorArgs = ['business_model_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="business_model_link" class="form-label "><?php echo e(_trans('landlord.Learn More Link')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['business_model_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="business_model_link"
                                        list="datalistOptions" id="business_model_link"
                                        placeholder="<?php echo e(_trans('landlord.Link')); ?>" value="<?php echo e(@$data['hometitles']->business_model_link); ?>">
                                    <?php $__errorArgs = ['business_model_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Amazing Features -->
                            <div class="row mb-3 page-title-description">
                                <!-- title Head Start -->
                                <div class="section-title-head">
                                    <h3><?php echo e(_trans('landlord.Amazing Features')); ?></h3>
                                    <hr>
                                </div>
                                <!-- title Head End -->
                                <div class="col-md-6 mb-3">
                                    <label for="feature_title" class="form-label "><?php echo e(_trans('landlord.Title')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['feature_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="feature_title"
                                        list="datalistOptions" id="feature_title"
                                        placeholder="<?php echo e(_trans('landlord.how_it_works')); ?>" value="<?php echo e(@$data['hometitles']->feature_title); ?>">
                                    <?php $__errorArgs = ['feature_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="feature_description" class="form-label "><?php echo e(_trans('landlord.Short Description')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['feature_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="feature_description"
                                        list="datalistOptions" id="feature_description"
                                        placeholder="<?php echo e(_trans('landlord.max_25_words_recommended')); ?>" value="<?php echo e(@$data['hometitles']->feature_description); ?>">
                                    <?php $__errorArgs = ['feature_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- How It Works -->
                            <div class="row mb-3 page-title-description">
                                <!-- title Head Start -->
                                <div class="section-title-head">
                                    <h3><?php echo e(_trans('landlord.How It Works')); ?></h3>
                                    <hr>
                                </div>
                                <!-- title Head End -->
                                <div class="col-md-6 mb-3">
                                    <label for="howitworks_title" class="form-label "><?php echo e(_trans('landlord.Title')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['howitworks_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="howitworks_title"
                                        list="datalistOptions" id="howitworks_title"
                                        placeholder="<?php echo e(_trans('landlord.how it works')); ?>" value="<?php echo e(@$data['hometitles']->howitworks_title); ?>">
                                    <?php $__errorArgs = ['howitworks_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="howitworks_description" class="form-label "><?php echo e(_trans('landlord.Short Description')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['howitworks_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="howitworks_description"
                                        list="datalistOptions" id="howitworks_description"
                                        placeholder="<?php echo e(_trans('landlord.max 25 words recommended')); ?>" value="<?php echo e(@$data['hometitles']->howitworks_description); ?>">
                                    <?php $__errorArgs = ['howitworks_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Download Your Mobile App -->
                            <div class="row mb-3 page-title-description" id="download_app">
                                <!-- title Head Start -->
                                <div class="section-title-head">
                                    <h3>Download Your Mobile App</h3>
                                    <hr>
                                </div>
                                <!-- title Head End -->
                                <div class="col-md-6 mb-3">
                                    <label for="app_store_link" class="form-label "><?php echo e(_trans('landlord.IOS App URL')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['app_store_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="app_store_link"
                                        list="datalistOptions" id="app_store_link"
                                        placeholder="<?php echo e(_trans('landlord.ios app url link')); ?>" value="<?php echo e(@$data['hometitles']->app_store_link); ?>">
                                    <?php $__errorArgs = ['app_store_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="play_store_link" class="form-label "><?php echo e(_trans('landlord.android app url')); ?><span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['play_store_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="play_store_link"
                                        list="datalistOptions" id="play_store_link"
                                        placeholder="<?php echo e(_trans('landlord.android app url link')); ?>" value="<?php echo e(@$data['hometitles']->play_store_link); ?>">
                                    <?php $__errorArgs = ['play_store_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Testimonial -->
                            <div class="row mb-3 page-title-description">
                                <!-- title Head Start -->
                                <div class="section-title-head">
                                    <h3>Testimonial</h3>
                                    <hr>
                                </div>
                                <!-- title Head End -->
                                <div class="col-md-6 mb-3">
                                    <label for="testimonial_title" class="form-label "><?php echo e(_trans('landlord.title')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['testimonial_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="testimonial_title"
                                        list="datalistOptions" id="testimonial_title"
                                        placeholder="<?php echo e(_trans('landlord.love from out clients around the world')); ?>" value="<?php echo e(@$data['hometitles']->testimonial_title); ?>">
                                    <?php $__errorArgs = ['testimonial_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="testimonial_description" class="form-label "><?php echo e(_trans('landlord.short description')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['testimonial_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="testimonial_description"
                                        list="datalistOptions" id="testimonial_description"
                                        placeholder="<?php echo e(_trans('landlord.max 25 words recommended')); ?>" value="<?php echo e(@$data['hometitles']->testimonial_description); ?>">
                                    <?php $__errorArgs = ['testimonial_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Blog -->
                            <div class="row mb-3 page-title-description">
                                <!-- title Head Start -->
                                <div class="section-title-head">
                                    <h3>Blog</h3>
                                    <hr>
                                </div>
                                <!-- title Head End -->
                                <div class="col-md-6 mb-3">
                                    <label for="blogs_title" class="form-label "><?php echo e(_trans('landlord.title')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['blogs_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="blogs_title"
                                        list="datalistOptions" id="blogs_title"
                                        placeholder="<?php echo e(_trans('landlord.whats_new')); ?>" value="<?php echo e(@$data['hometitles']->blogs_title); ?>">
                                    <?php $__errorArgs = ['blogs_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="blogs_description" class="form-label "><?php echo e(_trans('landlord.short description')); ?> <span class="fillable">*</span></label>
                                    <input class="form-control ot-input <?php $__errorArgs = ['blogs_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="blogs_description"
                                        list="datalistOptions" id="blogs_description"
                                        placeholder="<?php echo e(_trans('landlord.max 25 words recommended')); ?>" value="<?php echo e(@$data['hometitles']->blogs_description); ?>">
                                    <?php $__errorArgs = ['blogs_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div id="validationServer04Feedback" class="invalid-feedback">
                                            <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-12 mt-3">
                            <div class="text-end">
                                <button class="btn btn-lg ot-btn-primary"><span><i class="fa-solid fa-save"></i>
                                    </span><?php echo e(_trans('landlord.update')); ?></button>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\landlord\resources\views/backend/home-page/section_title.blade.php ENDPATH**/ ?>