<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_payments', function (Blueprint $table) {
            $table->id();


            $table->foreignId('master_order_id')->constrained('master_orders')->onDelete('cascade');
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->foreignId('property_id')->constrained('properties')->onDelete('cascade');
            $table->foreignId('tenant_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('builder_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('landowner_id')->constrained('users')->onDelete('cascade');


            $table->string('payment_method')->default('cash');
            $table->double('paid_amount')->default(0);
            $table->double('due_amount')->default(0);
            $table->string('payment_status')->default('pending');
            $table->string('payment_note')->nullable();


            //payment details
            $table->json('payment_details')->nullable();



            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_payments');
    }
};
