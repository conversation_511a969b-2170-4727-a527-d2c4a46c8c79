<?php

namespace App\Models\Property;

use App\Models\User;
use App\Models\Image;
use App\Models\Rental;
use App\Models\Document;
use App\Models\Wishlist;
use App\Models\Advertisement;
use App\Models\Property\Transaction;
use App\Models\Property\PropertyTenant;
use Illuminate\Database\Eloquent\Model;
use App\Models\Property\PropertyGallery;
use App\Models\Property\PropertyCategory;
use App\Models\Property\PropertyFacility;
use App\Models\Property\PropertyLocation;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Property extends Model
{
    protected $casts = [
        'document_ids' => 'array',
        'images' => 'array',
    ];
    use HasFactory;

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }


    public function advertisement(): HasMany
    {
        return $this->hasMany(Advertisement::class,'property_id');
    }

    // hasMany relation with PropertyGallery model
    public function galleries()
    {
        return $this->hasMany(PropertyGallery::class);
    }
    public function floorPlans()
    {
        return $this->hasMany(PropertyGallery::class);
    }
    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }
    public function rentals()
    {
        return $this->hasMany(Rental::class);
    }
    public function tenants()
    {
        return $this->hasMany(PropertyTenant::class);
    }

    // hasMany relation with PropertyFacility model
    public function facilities()
    {
        return $this->hasMany(PropertyFacility::class);
    }

    // belongsTo relation with PropertyCategory model
    public function category()
    {
        return $this->belongsTo(PropertyCategory::class, 'property_category_id');
    }

    public function document()
    {
        return $this->belongsTo(Document::class);
    }

    // default_image
    public function defaultImage()
    {
        return $this->belongsTo(Image::class, 'default_image', 'id');
    }


    public function tenant()
    {
        return $this->belongsTo(PropertyTenant::class);
    }

    public function location():HasOne
    {
        return $this->hasOne(PropertyLocation::class);
    }

    public function wishlist()
    {
        return $this->hasMany(Wishlist::class);
    }
}
