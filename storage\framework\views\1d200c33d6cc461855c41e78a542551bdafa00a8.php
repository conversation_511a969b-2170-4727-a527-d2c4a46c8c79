
<nav>

    <ul class="nav flex-column">
        <li class="nav-item">
            <a class="nav-link <?php echo e(request()->route()->parameters['type'] === 'basicInfo' ? 'active' : ''); ?>" href="<?php echo e(route('admin.properties.details', [$data['property']->id, 'basicInfo'])); ?>">Basic Info</a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(request()->route()->parameters['type'] === 'gallery' ? 'active' : ''); ?> " href="<?php echo e(route('admin.properties.details', [$data['property']->id, 'gallery'])); ?>">Gallery</a>
        </li>
         <li class="nav-item">
            <a class="nav-link <?php echo e(request()->route()->parameters['type'] === 'tenant' ? 'active' : ''); ?>" href="<?php echo e(route('admin.properties.details', [$data['property']->id, 'tenant'])); ?>">Tenants</a>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo e(request()->route()->parameters['type'] === 'facility' ? 'active' : ''); ?>" href="<?php echo e(route('admin.properties.details', [$data['property']->id, 'facility'])); ?>">Facilities</a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(request()->route()->parameters['type'] === 'floorPlan' ? 'active' : ''); ?>" href="<?php echo e(route('admin.properties.details', [$data['property']->id, 'floorPlan'])); ?>">Floor Plan</a>
        </li>
    </ul>
</nav>
<?php /**PATH C:\xampp\htdocs\landlord\resources\views/backend/property/propert_nav.blade.php ENDPATH**/ ?>