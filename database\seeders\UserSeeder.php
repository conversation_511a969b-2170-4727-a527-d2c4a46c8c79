<?php

namespace Database\Seeders;

use App\Models\Image;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    public function run()
    {
            // Blogs Image
            $lists = [
                'frontend/img/users/tenant_1.jpg',
                'frontend/img/users/tenant_2.jpg',
                'frontend/img/users/tenant_3.jpg',
                'frontend/img/users/tenant_4.jpg',
                'frontend/img/users/tenant_5.jpg',
                'frontend/img/users/tenant_6.jpg',
                'frontend/img/users/tenant_7.jpg',
                'frontend/img/users/tenant_8.jpg',
                'frontend/img/users/tenant_9.jpg',
                'frontend/img/users/tenant_10.jpg',
            ];
            foreach ($lists as $key => $list) {
                $image = Image::create([
                    'path' => $list,
                ]);
                $uploaded_tenant_img[] = $image->id;
            }

        User::create([
            'name' => 'Super Admin',
            'phone' => '01811000000',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('12345678'),
            'remember_token' => Str::random(10),
            'role_id' => 1,
            'date_of_birth' => '2022-09-07',
            'image_id' => 1,
            'designation_id' => rand(1, 5),
            'alt_phone' => '***********',
            'permanent_address' => 'Banani 13b',
            'present_address' => 'malibagh 13b',
            'city' => 'Dhaka',
            'state' => 'Dhaka',
            'zip_code' => '213',
            'nationality' => 'Bangladeshi',
            'blood_group' => 'A+',
            'occupation' => 'Business Man',
            'nid' => '6465325235',
            'social_security_number' => '4135325235',
            'passport' => 'eb4153525235',
            'tax_certificate' => 3,
            'property_count' => 3,
            'image_id' => $uploaded_tenant_img[0],
            'permissions' => [
                'user_read',
                'user_create',
                'user_update',
                'user_delete',
                'role_read',
                'role_create',
                'role_update',
                'role_delete',
                'language_read',
                'language_create',
                'language_update',
                'language_update_terms',
                'language_delete',
                'general_settings_read',
                'general_settings_update',
                'storage_settings_read',
                'storage_settings_update',
                'recaptcha_settings_read',
                'recaptcha_settings_update',
                'email_settings_read',
                'email_settings_update',
                'testimonial_read',
                'testimonial_create',
                'testimonial_update',
                'testimonial_delete',
                'blogs_read',
                'blogs_create',
                'blogs_update',
                'blogs_delete',
                'blog_categories_read',
                'blog_categories_create',
                'blog_categories_update',
                'blog_categories_delete',
                'category_read',
                'category_create',
                'category_update',
                'category_delete',
                'how-it-work_read',
                'how-it-work_create',
                'how-it-work_update',
                'how-it-work_delete',
                'business_model_read',
                'business_model_create',
                'business_model_update',
                'business_model_delete',
                'feature_read',
                'feature_create',
                'feature_update',
                'feature_delete',
                'section_titles_update',
                'partners_read',
                'partners_create',
                'partners_update',
                'partners_delete',
                'hero_section_read',
                'hero_section_create',
                'hero_section_update',
                'hero_section_delete',
                'tenant_read',
                'tenant_create',
                'tenant_update',
                'tenant_delete',
                'contact_read',
                'contact_delete',
                'property_read',
                'property_create',
                'property_update',
                'property_delete',
                'about_update',
                'property_category_read',
                'property_category_create',
                'property_category_update',
                'property_category_delete',
                'property_facility_type_read',
                'property_facility_type_create',
                'property_facility_type_update',
                'property_facility_type_delete',
                'ad_action_permission'

            ],
        ]);

        User::create([
            'name' => 'Admin',
            'phone' => '01811000001',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('12345678'),
            'remember_token' => Str::random(10),
            'role_id' => 2,
            'date_of_birth' => '2022-09-07',
            'image_id' => 2,
            'designation_id' => rand(1, 5),
            'alt_phone' => '***********',
            'permanent_address' => 'Banani 13b',
            'present_address' => 'malibagh 13b',
            'city' => 'Dhaka',
            'state' => 'Dhaka',
            'zip_code' => '213',
            'nationality' => 'Bangladeshi',
            'blood_group' => 'A+',
            'occupation' => 'Business Man',
            'nid' => '6465325235',
            'social_security_number' => '4135325235',
            'passport' => 'eb4153525235',
            'join_date' => '2023-09-07',
            'institution' => 'XYZ Company',
            'tax_certificate' => 3,
            'property_count' => 3,
            'permissions' => [
                'user_read',
                'user_create',
                'user_update',
                'user_delete',
                'role_read',
                'role_create',
                'role_update',
                'role_delete',
                'language_read',
                'language_create',
                'language_update_terms',
                'general_settings_read',
                'general_settings_update',
                'storage_settings_read',
                'storage_settings_read',
                'recaptcha_settings_update',
                'email_settings_read',
                'testimonial_read',
                'testimonial_create',
                'testimonial_update',
                'testimonial_delete',
                'blogs_read',
                'blogs_create',
                'blogs_update',
                'blogs_delete',
                'blog_categories_read',
                'blog_categories_create',
                'blog_categories_update',
                'blog_categories_delete',
                'category_read',
                'category_create',
                'category_update',
                'category_delete',
                'how-it-work_read',
                'how-it-work_create',
                'how-it-work_update',
                'how-it-work_delete',
                'partners_read',
                'partners_create',
                'partners_update',
                'partners_delete',
                'business_model_read',
                'business_model_create',
                'business_model_update',
                'business_model_delete',
                'feature_read',
                'feature_create',
                'feature_update',
                'feature_delete',
                'hero_section_read',
                'hero_section_create',
                'hero_section_update',
                'hero_section_delete',
                'tenant_read',
                'tenant_create',
                'tenant_update',
                'tenant_delete',
                'contact_read',
                'contact_delete',
                'property_read',
                'property_create',
                'property_update',
                'property_delete',
                'mail_subscribe_read',
                'mail_subscribe_delete',
                'ad_action_permission'
            ],
        ]);

        // create 3 land owner where role_id = 3
        for ($i = 0; $i <= 3; $i++) {
            User::create([
                'name' => 'Land Owner ' . $i,
                'phone' => '***********'.$i,
                'email' => 'landowner'.$i.'@onest.com',
                'email_verified_at' => now(),
                'password' => Hash::make('12345678'),
                'remember_token' => Str::random(10),
                'role_id' => 3,
                'date_of_birth' => '2022-09-07',
                'image_id' => 3,
                'designation_id' => rand(1, 5),
                'alt_phone' => '***********',
                'permanent_address' => 'Banani 13b',
                'present_address' => 'malibagh 13b',
                'city' => 'Dhaka',
                'state' => 'Dhaka',
                'zip_code' => '213',
                'nationality' => 'Bangladeshi',
                'blood_group' => 'A+',
                'occupation' => 'Business Man',
                'nid' => '6465325235',
                'social_security_number' => '4135325235',
                'passport' => 'eb4153525235',
                'join_date' => '2023-09-07',
                'institution' => 'XYZ Company',
                'tax_certificate' => 3,
                'property_count' => 3,
                'image_id' => $uploaded_tenant_img[$i],
                'permissions' => [
                    'property_read',
                    'property_create',
                    'property_update',
                    'property_delete',
                ],
            ]);
        }
        // create 10 landlords where role_id = 4
        for ($i = 0; $i <= 3; $i++) {
            User::create([
                'name' => 'Land Lord ' . $i,
                'phone' => '***********' . $i,
                'alt_phone' => '***********' . $i,
                'permanent_address' => 'Banani 13b ,' . 'Dhaka ' . $i,
                'present_address' => 'malibagh 13b',
                'city' => 'Dhaka',
                'state' => 'Dhaka',
                'zip_code' => '213',
                'nationality' => 'Bangladeshi',
                'blood_group' => 'A+',
                'occupation' => 'Business Man',
                'nid' => '6465325235',
                'social_security_number' => '4135325235',
                'passport' => 'eb4153525235',
                'tax_certificate' => 3,
                'property_count' => 3,
                'email' => 'landlord' . $i . '@onest.com',
                'email_verified_at' => now(),
                'password' => Hash::make('12345678'),
                'remember_token' => Str::random(10),
                'role_id' => 4,
                'date_of_birth' => '2022-09-07',
                'join_date' => '2023-09-07',
                'institution' => 'XYZ Company',
                'designation_id' => 6,
                'image_id' => $uploaded_tenant_img[$i],
                'permissions' => [
                    'property_read',
                    'property_create',
                    'property_update',
                    'property_delete',
                ],
            ]);
        }



        for ($i = 0; $i < 10; $i++) {
            User::create([
                'name' => 'Tenant ' . $i + 1,
                'phone' => '***********' . $i,
                'alt_phone' => '***********',
                'permanent_address' => 'Banani 13b',
                'present_address' => 'malibagh 13b',
                'city' => 'Dhaka',
                'state' => 'Dhaka',
                'zip_code' => '213',
                'nationality' => 'Bangladeshi',
                'blood_group' => 'A+',
                'occupation' => 'Business Man',
                'nid' => '4135325235',
                'social_security_number' => '4135325235',
                'passport' => 'eb4135325235',
                'email' => 'tenant' . $i + 1 . '@onest.com',
                'email_verified_at' => now(),
                'password' => Hash::make('12345678'),
                'remember_token' => Str::random(10),
                'role_id' => 5,
                'date_of_birth' => '2022-09-07',
                'join_date' => '2023-09-07',
                'institution' => 'XYZ Company',
                'designation_id' => 7,
                'image_id' => $uploaded_tenant_img[$i],
                'permissions' => [],
            ]);
        }
    }
}
