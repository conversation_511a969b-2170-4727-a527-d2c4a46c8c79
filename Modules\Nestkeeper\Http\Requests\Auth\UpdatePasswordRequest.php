<?php

namespace Modules\Nestkeeper\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePasswordRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'password'              => 'required|string|min:6',
            'new_password'          => 'required|string|min:6',
            'confirm_password'      => 'required|string|min:6',
        ];

    }

    public function messages()
    {
        return [
            'password.required'                 => _trans('nestkeeper.Password is required!'),
            'password.min'                      => _trans('nestkeeper.Password length must be 6 character or greater!'),
            'new_password.required'             => _trans('nestkeeper.New password is required!'),
            'new_password.min'                  => _trans('nestkeeper.New password length must be 6 character or greater!'),
            'confirm_password.required'         => _trans('nestkeeper.Confirm password is required!'),
            'confirm_password.min'              => _trans('nestkeeper.Confirm password length must be 6 character or greater!!'),
        ];
    }


}
