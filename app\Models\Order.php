<?php

namespace App\Models;


use App\Models\User;
use App\Models\MasterOrder;
use App\Models\Property\Property;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends Model
{
    use HasFactory;

    public function property()
    {
        return $this->belongsTo(Property::class);
    }
    public function masterOrder()
    {
        return $this->belongsTo(MasterOrder::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

}
