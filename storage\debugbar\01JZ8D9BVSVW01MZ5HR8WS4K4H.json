{"__meta": {"id": "01JZ8D9BVSVW01MZ5HR8WS4K4H", "datetime": "2025-07-03 15:08:08", "utime": **********.954598, "method": "GET", "uri": "/properties-buy", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[15:08:08] LOG.warning: Creation of dynamic property App\\Http\\Controllers\\Frontend\\FrontendPropertyController::$paginateLimit is deprecated in C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\FrontendPropertyController.php on line 27", "message_html": null, "is_string": false, "label": "warning", "time": **********.831455, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.559564, "end": **********.954612, "duration": 0.3950479030609131, "duration_str": "395ms", "measures": [{"label": "Booting", "start": **********.559564, "relative_start": 0, "end": **********.81344, "relative_end": **********.81344, "duration": 0.****************, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.813451, "relative_start": 0.****************, "end": **********.954613, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.823956, "relative_start": 0.****************, "end": **********.8285, "relative_end": **********.8285, "duration": 0.*****************, "duration_str": "4.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Exception", "message": "Cannot add SymfonyMailCollector on Laravel Debugbar: Mailer [] is not defined.", "code": 0, "file": "vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php", "line": 740, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>534</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">addCollectorException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">Cannot add SymfonyMailCollector</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object InvalidArgumentException]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">boot</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">C:\\xampp\\htdocs\\landlord\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["    protected function addCollectorException(string $message, Exception $exception)\n", "    {\n", "        $this->addThrowable(\n", "            new Exception(\n", "                $message . ' on Laravel Debugbar: ' . $exception->getMessage(),\n", "                $exception->getCode(),\n", "                $exception\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=740", "ajax": false, "filename": "LaravelDebugbar.php", "line": "740"}}, {"type": "InvalidArgumentException", "message": "Mailer [] is not defined.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Mail/MailManager.php", "line": 113, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1642428605 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>97</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"68 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>33</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>885</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Mail\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Mail\\MailServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>770</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">build</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>881</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>706</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>866</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1431</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>504</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">offsetGet</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">boot</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">C:\\xampp\\htdocs\\landlord\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642428605\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["        $config = $this->getConfig($name);\n", "\n", "        if (is_null($config)) {\n", "            throw new InvalidArgumentException(\"Mailer [{$name}] is not defined.\");\n", "        }\n", "\n", "        // Once we have created the mailer instance we will set a container instance\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FMail%2FMailManager.php&line=113", "ajax": false, "filename": "MailManager.php", "line": "113"}}]}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "landlord.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 38, "nb_statements": 38, "nb_visible_statements": 38, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.021130000000000003, "accumulated_duration_str": "21.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `advertisements` where `advertisement_type` = 2 and `status` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/FrontendPropertyController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\FrontendPropertyController.php", "line": 136}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.842997, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "FrontendPropertyController.php:136", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/FrontendPropertyController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\FrontendPropertyController.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FFrontendPropertyController.php&line=136", "ajax": false, "filename": "FrontendPropertyController.php", "line": "136"}, "connection": "landlord", "explain": null, "start_percent": 0, "width_percent": 5.017}, {"sql": "select * from `properties` where `properties`.`id` in (1, 3, 5, 6, 8, 10, 11, 13, 15, 16, 18, 20, 21, 23, 25, 26, 28, 30)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/FrontendPropertyController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\FrontendPropertyController.php", "line": 136}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.848093, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "FrontendPropertyController.php:136", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/FrontendPropertyController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\FrontendPropertyController.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FFrontendPropertyController.php&line=136", "ajax": false, "filename": "FrontendPropertyController.php", "line": "136"}, "connection": "landlord", "explain": null, "start_percent": 5.017, "width_percent": 4.401}, {"sql": "select * from `images` where `images`.`id` = 35 limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.854849, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 9.418, "width_percent": 2.319}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.8572092, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 11.737, "width_percent": 2.177}, {"sql": "select * from `images` where `images`.`id` = 47 limit 1", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.859967, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 13.914, "width_percent": 3.833}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.862954, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 17.747, "width_percent": 2.508}, {"sql": "select * from `images` where `images`.`id` = 59 limit 1", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.86577, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 20.256, "width_percent": 2.982}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.868916, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 23.237, "width_percent": 2.982}, {"sql": "select * from `images` where `images`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.8712769, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 26.219, "width_percent": 2.035}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.873521, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 28.254, "width_percent": 2.272}, {"sql": "select * from `images` where `images`.`id` = 77 limit 1", "type": "query", "params": [], "bindings": [77], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.8762221, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 30.525, "width_percent": 3.881}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.879114, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 34.406, "width_percent": 2.461}, {"sql": "select * from `images` where `images`.`id` = 89 limit 1", "type": "query", "params": [], "bindings": [89], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.882087, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 36.867, "width_percent": 3.36}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.885119, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 40.227, "width_percent": 2.556}, {"sql": "select * from `images` where `images`.`id` = 95 limit 1", "type": "query", "params": [], "bindings": [95], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.887331, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 42.783, "width_percent": 2.13}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.889698, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 44.912, "width_percent": 2.177}, {"sql": "select * from `images` where `images`.`id` = 107 limit 1", "type": "query", "params": [], "bindings": [107], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.891799, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 47.089, "width_percent": 2.319}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.894588, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 49.408, "width_percent": 2.792}, {"sql": "select * from `images` where `images`.`id` = 119 limit 1", "type": "query", "params": [], "bindings": [119], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.89736, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 52.201, "width_percent": 2.366}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.899879, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 54.567, "width_percent": 2.887}, {"sql": "select * from `images` where `images`.`id` = 125 limit 1", "type": "query", "params": [], "bindings": [125], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.9024081, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 57.454, "width_percent": 2.224}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.90465, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 59.678, "width_percent": 2.414}, {"sql": "select * from `images` where `images`.`id` = 137 limit 1", "type": "query", "params": [], "bindings": [137], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.906754, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 62.092, "width_percent": 2.13}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.9099169, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 64.221, "width_percent": 2.603}, {"sql": "select * from `images` where `images`.`id` = 149 limit 1", "type": "query", "params": [], "bindings": [149], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.912728, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 66.824, "width_percent": 2.13}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.915233, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 68.954, "width_percent": 2.792}, {"sql": "select * from `images` where `images`.`id` = 155 limit 1", "type": "query", "params": [], "bindings": [155], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.917888, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 71.746, "width_percent": 2.556}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.920176, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 74.302, "width_percent": 2.414}, {"sql": "select * from `images` where `images`.`id` = 167 limit 1", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.922317, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 76.716, "width_percent": 1.278}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.924405, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 77.993, "width_percent": 1.514}, {"sql": "select * from `images` where `images`.`id` = 179 limit 1", "type": "query", "params": [], "bindings": [179], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.927199, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 79.508, "width_percent": 2.792}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.9296849, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 82.3, "width_percent": 1.798}, {"sql": "select * from `images` where `images`.`id` = 185 limit 1", "type": "query", "params": [], "bindings": [185], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.93171, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 84.098, "width_percent": 2.224}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.934703, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 86.323, "width_percent": 2.982}, {"sql": "select * from `images` where `images`.`id` = 197 limit 1", "type": "query", "params": [], "bindings": [197], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.9376369, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 89.304, "width_percent": 1.94}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.939723, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 91.245, "width_percent": 2.461}, {"sql": "select * from `images` where `images`.`id` = 209 limit 1", "type": "query", "params": [], "bindings": [209], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.9428658, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:27", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=27", "ajax": false, "filename": "PropertyAdCollection.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 93.706, "width_percent": 3.455}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 231}], "start": **********.946071, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PropertyAdCollection.php:34", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/PropertyAdCollection.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Resources\\PropertyAdCollection.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FResources%2FPropertyAdCollection.php&line=34", "ajax": false, "filename": "PropertyAdCollection.php", "line": "34"}, "connection": "landlord", "explain": null, "start_percent": 97.16, "width_percent": 2.84}]}, "models": {"data": {"App\\Models\\Advertisement": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FAdvertisement.php&line=1", "ajax": false, "filename": "Advertisement.php", "line": "?"}}, "App\\Models\\Property\\Property": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FProperty%2FProperty.php&line=1", "ajax": false, "filename": "Property.php", "line": "?"}}, "App\\Models\\Image": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FImage.php&line=1", "ajax": false, "filename": "Image.php", "line": "?"}}, "App\\Models\\Property\\PropertyCategory": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FProperty%2FPropertyCategory.php&line=1", "ajax": false, "filename": "PropertyCategory.php", "line": "?"}}}, "count": 72, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/properties-buy", "action_name": "getBuyProperties", "controller_action": "App\\Http\\Controllers\\Frontend\\FrontendPropertyController@getBuyProperties", "uri": "GET properties-buy", "controller": "App\\Http\\Controllers\\Frontend\\FrontendPropertyController@getBuyProperties<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FFrontendPropertyController.php&line=133\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FFrontendPropertyController.php&line=133\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/FrontendPropertyController.php:133-141</a>", "middleware": "web, XssSanitizer, lang", "duration": "414ms", "peak_memory": "24MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-942251069 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-942251069\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1881907376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1881907376\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-553770719 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IkFyUHVRbklTWHFMcUdnSWx0N0lwRWc9PSIsInZhbHVlIjoic2lyVUs0bGcxUTJoZ0xtWHdsSzg3S2kvRm5xWVk5QVl6OWtyckNmemZydnRwNVZxZ3cyenpIUnhEVWVUaGs3VFZyS2g0eFFwSWFqOEFZUUhOK2ZpeThrd3Bxd0FmQWZKbEdIbnZUbzFNUGJqaUFET0FsbzByWDJvaGNadEFCSU4iLCJtYWMiOiIxMzc3OGM1ZjExYjJiMTAzZWRiOGVlMWI0MGUyZmZkOGY4ZGYwYWYzYmU2MjJkNDFhNzMxM2E4YjU5OGI1NDNkIiwidGFnIjoiIn0%3D; landlord_session=eyJpdiI6Ik1ObFIvOFdkN216UXRqSmtrWW53VHc9PSIsInZhbHVlIjoiMGpucm1zbW03R0FxQ0I0eTVvZkZJUm45SCtxZ21HQzdaa1lxaXkwOTMvWFBEa1h3QlVldisyQWQrL2JUc21iNXdtR2c0UnppOU5lTlhqcm9pSktENW80UHloQUdGSmxaT0RZNVEvT0FzRit3b1pvdzRCQjRkZXQ5OWxQd1FEQnEiLCJtYWMiOiI2MDRlNDFmZWIzOGM4ZWZjM2VhY2M5Y2EwYjY4NzNhNjMxNTVmNjE4OTk4ODBiNGYyYmQ1NjFlYjM1NGEzYWNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553770719\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zd8n8oVrnqq7VrOib2a0aR6Mg0x9iKyNiIzK3AUb</span>\"\n  \"<span class=sf-dump-key>landlord_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wjN97PJEcLLEIbZmnCzEPYaoZw3NxJJzOOvYVDV4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2076190029 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 15:08:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076190029\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1479210845 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zd8n8oVrnqq7VrOib2a0aR6Mg0x9iKyNiIzK3AUb</span>\"\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1479210845\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/properties-buy", "action_name": "getBuyProperties", "controller_action": "App\\Http\\Controllers\\Frontend\\FrontendPropertyController@getBuyProperties"}, "badge": null}}