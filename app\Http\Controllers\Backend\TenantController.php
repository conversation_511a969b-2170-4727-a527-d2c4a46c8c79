<?php

namespace App\Http\Controllers\Backend;

use Illuminate\Http\Request;
use App\Interfaces\TenantInterface;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Schema;
use App\Interfaces\PermissionInterface;
use App\Http\Requests\Tenant\TenantStoreRequest;

class TenantController extends Controller
{
    private $tenant;
    private $permission;

    function __construct(TenantInterface $tenantInterface, PermissionInterface $permissionInterface)
    {

        if (!Schema::hasTable('settings') && !Schema::hasTable('users')  ) {
            abort(400);
        }
        $this->tenant   = $tenantInterface;
        $this->permission = $permissionInterface;
    }

    public function index()
    {

        $data['tenant']  = $this->tenant->getAll();
        $data['title']      = ___('common.tenant');
        return view('backend.tenant.index',compact('data'));
    }

    public function create()
    {

        $data['title']       = ___('common.create_tenant');
        $data['permissions'] = $this->permission->all();
        return view('backend.tenant.create', compact('data'));
    }

    public function store(TenantStoreRequest $request)
    {
        $result = $this->tenant->store($request);
        if ($result) {
            return redirect()->route('tenants.index')->with('success', ___('alert.tenant_created_successfully'));
        }
        return redirect()->route('tenants.index')->with('danger', ___('alert.something_went_wrong_please_try_again'));
    }

    public function edit($id)
    {
        $data['tenant']    = $this->tenant->show($id);
        $data['title']       = ___('common.categories');
        $data['permissions'] = $this->permission->all();
        return view('backend.tenant.edit',compact('data'));
    }

    public function update(TenantUpdateRequest $request,$id)
    {
        $result = $this->tenant->update($request,$id);
        if($result){
            return redirect()->route('categories.index')->with('success', ___('alert.tenant_updated_successfully'));
        }
        return redirect()->route('categories.index')->with('danger', ___('alert.something_went_wrong_please_try_again'));
    }

    public function delete($id)
    {
        if ($this->tenant->destroy($id)):
            $success[0] = "Deleted Successfully";
            $success[1] = "Success";
            $success[2] = "Deleted";
        else:
            $success[0] = "Something went wrong, please try again.";
            $success[1] = 'error';
            $success[2] = "oops";
        endif;
        return response()->json($success);

    }
}
