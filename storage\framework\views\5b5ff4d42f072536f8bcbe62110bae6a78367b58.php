<?php $__env->startSection('title'); ?>
    <?php echo e(@$data['title']); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('style'); ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datepicker/1.0.10/datepicker.min.css"
        integrity="sha512-YdYyWQf8AS4WSB0WWdc3FbQ3Ypdm0QCWD2k4hgfqbQbRCJBEgX0iAegkl2S1Evma5ImaVXLBeUkIlP6hQ1eYKQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-content">

    
    <div class="page-header">
        <div class="row">
            <div class="col-sm-6">
                <h4 class="bradecrumb-title mb-1"><?php echo e($data['title']); ?></h1>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"> <?php echo e(___('common.home')); ?> </a></li>
                    <li class="breadcrumb-item"><?php echo e($data['title']); ?></li>
                </ol>
            </div>
        </div>
    </div>
    

    <div class="card ot-card">
        <div class="card-header ">
            <h4><?php echo e(___('settings.aws_s3_info')); ?></h4>
        </div>
        <div class="card-body">
            <form action="<?php echo e(route('settings.storageSettingUpdate')); ?>" enctype="multipart/form-data" method="post"
                id="visitForm">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="row mb-3">
                    <div class="col-md-12">
                            <div class="row mb-3">
                                
                                <div class="col-sm-6 mb-3">

                                
                                    <label for="inputname" class="form-label"><?php echo e(___('settings.file_system')); ?> <span class="text-danger">*</span></label>
                                    <select class="nice-select niceSelect form-select ot-input file_system  bordered_style wide <?php $__errorArgs = ['file_system'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        value="<?php echo e(Setting('file_system')); ?>"
                                        name="file_system" id="validationServer04"
                                        aria-describedby="validationServer04Feedback">
                                        <option value=""><?php echo e(___('common.select')); ?></option>
                                        <option value="local" <?php echo e(setting('file_system') == "local" ? "selected":""); ?>><?php echo e(___('settings.local')); ?></option>
                                        <option value="s3" <?php echo e(setting('file_system') == "s3" ? "selected":""); ?>><?php echo e(___('settings.s3')); ?></option>
                                    </select>
                                <?php $__errorArgs = ['file_system'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div id="validationServer04Feedback" class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                            </div>
                        

                        
                        <div class="col-12 col-md-6 col-xl-6 col-lg-6 mb-3 _common_div">
                                <label for="inputname" class="form-label"><?php echo e(___('settings.aws_access_key_id')); ?> <span class="text-danger">*</span></label>
                                <input type="text" name="aws_access_key_id"
                                    class="form-control ot-input <?php $__errorArgs = ['aws_access_key_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    value="<?php echo e(setting('aws_access_key_id')); ?>" placeholder="<?php echo e(___('settings.aws_access_key_id')); ?>">
                                <?php $__errorArgs = ['aws_access_key_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div id="validationServer04Feedback" class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        

                        
                        <div class="col-12 col-md-6 col-xl-6 col-lg-6 mb-3 _common_div">
                                <label for="inputname" class="form-label"> <?php echo e(___('settings.aws_secret_key')); ?> <span class="text-danger">*</span></label>
                                <input type="text" name="aws_secret_key"
                                    class="form-control ot-input <?php $__errorArgs = ['aws_secret_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    value="<?php echo e(setting('aws_secret_key')); ?>" placeholder="<?php echo e(___('settings.aws_secret_key')); ?>">
                                <?php $__errorArgs = ['aws_secret_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div id="validationServer04Feedback" class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        

                        
                        <div class="col-12 col-md-6 col-xl-6 col-lg-6 mb-3 _common_div">
                                <label for="inputname" class="form-label"><?php echo e(___('settings.aws_default_region')); ?> <span class="text-danger">*</span></label>
                                <input type="text" name="aws_region"
                                    class="form-control ot-input <?php $__errorArgs = ['aws_region'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    value="<?php echo e(Setting('aws_region')); ?>" placeholder="<?php echo e(___('settings.aws_default_region')); ?>">
                                <?php $__errorArgs = ['aws_region'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div id="validationServer04Feedback" class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        

                        
                        <div class="col-12 col-md-6 col-xl-6 col-lg-6 mb-3 _common_div">
                                <label for="inputname" class="form-label"><?php echo e(___('settings.aws_bucket')); ?> <span class="text-danger">*</span></label>
                                <input type="text" name="aws_bucket"
                                    class="form-control ot-input <?php $__errorArgs = ['aws_bucket'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    value="<?php echo e(setting('aws_bucket')); ?>" placeholder="<?php echo e(___('settings.aws_bucket')); ?>">
                                <?php $__errorArgs = ['aws_bucket'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div id="validationServer04Feedback" class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        

                        
                        <div class="col-12 col-md-6 col-xl-6 col-lg-6 mb-3 _common_div">
                                <label for="inputname" class="form-label"><?php echo e(___('settings.aws_endpoint')); ?> <span class="text-danger">*</span></label>
                                <input type="text" name="aws_endpoint"
                                    class="form-control ot-input <?php $__errorArgs = ['aws_endpoint'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    value="<?php echo e(Setting('aws_endpoint')); ?>" placeholder="<?php echo e(___('settings.aws_endpoint')); ?>">
                                <?php $__errorArgs = ['aws_endpoint'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div id="validationServer04Feedback" class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                        </div>

                    </div>

                    <div class="col-md-12 mt-3">
                        <div class="text-end">
                            <?php if(hasPermission('email_settings_update')): ?>
                                <button class="btn btn-lg ot-btn-primary">
                                    <span>
                                        <i class="fa-solid fa-save"></i>
                                    </span><?php echo e(___('common.update')); ?>

                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('backend.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\landlord\resources\views/backend/settings/storage_setting.blade.php ENDPATH**/ ?>