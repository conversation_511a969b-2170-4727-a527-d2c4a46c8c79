[2025-07-03 15:06:15] local.ERROR: The "--table" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--table\" option does not exist. at C:\\xampp\\htdocs\\landlord\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\xampp\\htdocs\\landlord\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('table', 'roles')
#1 C:\\xampp\\htdocs\\landlord\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--table=roles')
#2 C:\\xampp\\htdocs\\landlord\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--table=roles', true)
#3 C:\\xampp\\htdocs\\landlord\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\xampp\\htdocs\\landlord\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(152): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\xampp\\htdocs\\landlord\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\xampp\\htdocs\\landlord\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\xampp\\htdocs\\landlord\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\xampp\\htdocs\\landlord\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-07-03 15:06:45] local.ALERT: array (
  0 => 
  array (
    'query' => 'select count(*) as aggregate from `properties`',
    'bindings' => 
    array (
    ),
    'time' => 0.51,
  ),
  1 => 
  array (
    'query' => 'select count(*) as aggregate from `properties`',
    'bindings' => 
    array (
    ),
    'time' => 0.42,
  ),
  2 => 
  array (
    'query' => 'select * from `properties` limit 18',
    'bindings' => 
    array (
    ),
    'time' => 0.67,
  ),
)  
[2025-07-03 15:06:46] local.ALERT: array (
  0 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?)',
    'bindings' => 
    array (
      0 => 1,
    ),
    'time' => 0.52,
  ),
  1 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?)',
    'bindings' => 
    array (
      0 => 1,
    ),
    'time' => 0.51,
  ),
  2 => 
  array (
    'query' => 'select * from `properties` where `deal_type` in (?) limit 18',
    'bindings' => 
    array (
      0 => 1,
    ),
    'time' => 0.52,
  ),
)  
[2025-07-03 15:06:48] local.ALERT: array (
  0 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?, ?)',
    'bindings' => 
    array (
      0 => 1,
      1 => 2,
    ),
    'time' => 0.53,
  ),
  1 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?, ?)',
    'bindings' => 
    array (
      0 => 1,
      1 => 2,
    ),
    'time' => 0.37,
  ),
  2 => 
  array (
    'query' => 'select * from `properties` where `deal_type` in (?, ?) limit 18',
    'bindings' => 
    array (
      0 => 1,
      1 => 2,
    ),
    'time' => 0.62,
  ),
)  
[2025-07-03 15:06:49] local.ALERT: array (
  0 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?)',
    'bindings' => 
    array (
      0 => 2,
    ),
    'time' => 0.5,
  ),
  1 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?)',
    'bindings' => 
    array (
      0 => 2,
    ),
    'time' => 0.39,
  ),
  2 => 
  array (
    'query' => 'select * from `properties` where `deal_type` in (?) limit 18',
    'bindings' => 
    array (
      0 => 2,
    ),
    'time' => 0.63,
  ),
)  
[2025-07-03 15:06:50] local.ALERT: array (
  0 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?, ?)',
    'bindings' => 
    array (
      0 => 1,
      1 => 2,
    ),
    'time' => 0.38,
  ),
  1 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?, ?)',
    'bindings' => 
    array (
      0 => 1,
      1 => 2,
    ),
    'time' => 0.23,
  ),
  2 => 
  array (
    'query' => 'select * from `properties` where `deal_type` in (?, ?) limit 18',
    'bindings' => 
    array (
      0 => 1,
      1 => 2,
    ),
    'time' => 0.41,
  ),
)  
[2025-07-03 15:06:51] local.ALERT: array (
  0 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?)',
    'bindings' => 
    array (
      0 => 1,
    ),
    'time' => 0.44,
  ),
  1 => 
  array (
    'query' => 'select count(*) as aggregate from `properties` where `deal_type` in (?)',
    'bindings' => 
    array (
      0 => 1,
    ),
    'time' => 0.33,
  ),
  2 => 
  array (
    'query' => 'select * from `properties` where `deal_type` in (?) limit 18',
    'bindings' => 
    array (
      0 => 1,
    ),
    'time' => 0.54,
  ),
)  
