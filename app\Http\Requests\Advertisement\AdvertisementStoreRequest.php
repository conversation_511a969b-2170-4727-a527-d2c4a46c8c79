<?php

namespace App\Http\Requests\Advertisement;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class AdvertisementStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $advertisement_type = $this->input('advertisement_type');
        $property_id = $this->input('property_id');

        $rules = [
            'advertisement_type' => 'required|in:1,2',
            'property_id' => [ 'required',
                Rule::unique('advertisements')->where(function ($query) use($advertisement_type,$property_id) {
                    return $query->where('advertisement_type', $advertisement_type)
                    ->where('property_id', $property_id);
                }),],
            'booking_amount' => 'required|numeric|min:0',
            'negotiable' => 'boolean',
        ];

        if ($advertisement_type == 1) {
            $rules['rent_amount'] = 'required|numeric|min:0';
            $rules['rent_type'] = 'required|string';
            $rules['rent_start_date'] = 'required|date';
            $rules['rent_end_date'] = 'required|date|after:rent_start_date';
        } elseif ($advertisement_type == 2) {
            $rules['sell_amount'] = 'required|numeric|min:0';
            $rules['sell_start_date'] = 'required|date';
        }

        return $rules;
    }
   
}
