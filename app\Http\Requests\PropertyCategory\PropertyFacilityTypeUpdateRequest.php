<?php

namespace App\Http\Requests\PropertyCategory;

use Illuminate\Foundation\Http\FormRequest;

class PropertyFacilityTypeUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'image_id' => 'mimes:jpeg,png,jpg',
            'status' => 'required',
        ];
    }
}
