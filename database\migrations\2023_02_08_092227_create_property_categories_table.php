<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('property_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name', 191)->nullable();
            $table->string('slug', 191)->nullable();
            $table->string('icon', 191)->nullable();
            $table->unsignedBigInteger('image_id')->nullable();
            $table->integer('serial')->nullable();
            $table->tinyInteger('status')->default(App\Enums\Status::ACTIVE);
            $table->tinyInteger('featured')->default(0)->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('property_categories');
    }
};
