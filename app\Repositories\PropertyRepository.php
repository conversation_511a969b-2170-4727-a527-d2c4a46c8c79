<?php

namespace App\Repositories;

use Illuminate\Support\Str;
use App\Models\Property\Property;
use App\Traits\CommonHelperTrait;
use App\Interfaces\TenantInterface;
use Illuminate\Support\Facades\Auth;
use App\Interfaces\PropertyInterface;
use App\Models\Property\PropertyGallery;
use App\Models\Property\PropertyFacility;
use App\Models\Property\PropertyLocation;

class PropertyRepository implements PropertyInterface{
    use CommonHelperTrait;
    private Property $model;
    private PropertyFacility $facility;

    public function __construct(Property $model, PropertyFacility $facility){
        $this->model = $model;
        $this->facility = $facility;
    }

    public function index($request){

        return Property::get();

    }

    public function status($request){

        return $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);

    }

    public function deletes($request){

        return $this->model->destroy((array)$request->ids);

    }

    public function getAll()
    {
        return Property::latest()->paginate(5);
    }

    public function getCreatedBy()
    {
        return Property::Where('user_id', Auth::id())->paginate(5);
    }

    // For Property Advertisement
    public function getActiveAll()
    {
        return Property::where('status', 1)->latest()->get();
    }

    public function getActiveCreatedBy()
    {
        return Property::Where('user_id', Auth::id())->where('status', 1)->get();
    }

    public function store($request)
    {

        try {
            $propertyStore                                       = new $this->model;
            $propertyStore->name                                 = $request->name;
            $propertyStore->slug                                 = Str::slug($request->name);
            $propertyStore->size                                 = $request->size_of_property;
            $propertyStore->bedroom                              = $request->bedroom;
            $propertyStore->bathroom                             = $request->bathroom;
            $propertyStore->rent_amount                          = $request->rent_price;
            $propertyStore->flat_no                              = $request->Flat_Number;
            $propertyStore->type                                 = $request->property_type;
            $propertyStore->dining_combined                      = $request->has('drawing_dining_combined')?1:0;
            $propertyStore->vacant                               = $request->has('vacant')?'1':'0';
            $propertyStore->status                               = $request->status;
            $propertyStore->completion                           = $request->completion;
            $propertyStore->description                          = $request->Description;
            // if($request->has('image')){
                $propertyStore->default_image                    = $this->UploadImageCreate($request->image, 'backend/uploads/properties')??33;
            // }
            $propertyStore->property_category_id                 = $request->property_category;
            $propertyStore->user_id                              = Auth::user()->id;
            $propertyStore->save();

            // Property Location
            $property_location = new PropertyLocation;
            $property_location->address = $request->address;
            $property_location->property_id = $propertyStore->id;
            $property_location->user_id = Auth::user()->id;
            $property_location->country_id = $request->country;
            $property_location->post_code = $request->post_code;
            $property_location->state_id = $request->state; // Assuming you have a state select field in your form
            $property_location->city_id = $request->city; // Assuming you have a city select field in your form

            $property_location->save();

            return true;
        } catch (\Throwable $th) {
            dd($th);
            return false;
        }
    }

    public function show($id)
    {
        return $this->model->find($id);
    }



    public function update($request, $id, $type)
    {


        $propertyStore                                       = $this->model->find($id);
        if($type == 'basicInfo'){
            try {
                $propertyStore->name                                 = $request->name;
                $propertyStore->size                                 = $request->size_of_property;
                $propertyStore->bedroom                              = $request->bedroom;
                $propertyStore->bathroom                             = $request->bathroom;
                $propertyStore->rent_amount                          = $request->rent_price;
                $propertyStore->flat_no                              = $request->Flat_Number;
                $propertyStore->type                                 = $request->property_type;
                $propertyStore->dining_combined                      = $request->has('drawing_dining_combined')?1:0;
                $propertyStore->vacant                               = $request->has('vacant')?'1':'0';
                $propertyStore->status                               = $request->status;
                $propertyStore->completion                           = $request->completion;
                $propertyStore->description                          = $request->Description;
                if($request->has('image')){
                    $propertyStore->default_image                    = $this->UploadImageCreate($request->image, 'backend/uploads/properties')??33;
                }
                $propertyStore->property_category_id                 = $request->property_category;
                $propertyStore->user_id                              = Auth::user()->id;
                $propertyStore->save();

                // Property Location
                $property_location                                   = PropertyLocation::where('property_id', $id)->first();
                $property_location->address                          = $request->address;
                $property_location->country_id                       = $request->country;
                if($request->state){

                    $property_location->state_id                        = $request->state;
                }
                if ($request->city) {

                    $property_location->city_id                        = $request->city;
                }


                $property_location->save();
                return true;
            } catch (\Throwable $th) {
            
                return false;
            }
        }elseif($type == 'gallery'){
            try {
                $property_gallery                                    = new PropertyGallery();
                $property_gallery->type                              = 'gallery';
                $property_gallery->title                             = $request->Title;
                $property_gallery->property_id                       = $id;
                $property_gallery->serial                            = $propertyStore->galleries->where('type','gallery')->count()+1;
                $property_gallery->image_id                          = $this->UploadImageCreate($request->image, 'backend/uploads/properties');
                $property_gallery->save();
                return true;
            } catch (\Throwable $th) {
                dd($th);
                //throw $th;
                return false;
            }

        }elseif($type == 'tenants'){

        }elseif($type == 'facility'){
            try {
                $property_facility                                    = new PropertyFacility();
                $property_facility->property_id                       = $id;
                $property_facility->property_facility_type_id         = $request->facility_type;
                $property_facility->content                           = $request->content;
                $property_facility->save();
                return true;
            } catch (\Throwable $th) {
                dd($th);
                //throw $th;
                return false;
            }

        }elseif($type == 'floor_plan'){
            try {
                $property_gallery                                    = new PropertyGallery();
                $property_gallery->type                              = 'floor_plan';
                $property_gallery->title                             = $request->Title;
                $property_gallery->property_id                       = $id;
                $property_gallery->serial                            = $propertyStore->galleries->where('type','floor_plan')->count()+1;
                $property_gallery->image_id                          = $this->UploadImageCreate($request->image, 'backend/uploads/properties');
                $property_gallery->save();
                return true;
            } catch (\Throwable $th) {
                dd($th);
                //throw $th;
                return false;
            }
        }else{

        }

    }

    public function destroy($id)
    {
        try {
            $propertyDestroy   = $this->model->find($id);
            $this->UploadImageDelete($propertyDestroy->image_id); // delete image & record
            $propertyDestroy->delete();
            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }
    public function facilityDestroy($id)
    {
        try {
            $facility   = $this->facility->find($id);

            $facility->delete();
            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function deleteImage($id){
        try {
            $this->UploadImageDelete($id);

            return true;
        } catch (\Throwable $th) {
            //throw $th;
            return false;
        }
    }

}
