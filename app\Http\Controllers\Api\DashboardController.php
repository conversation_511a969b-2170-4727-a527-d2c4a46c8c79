<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\DashboardTransactionCollection;
use App\Models\Property\Transaction;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\UserListResource;
use App\Http\Resources\PropertyListResource;
use App\Http\Resources\TransactionAllList;
use App\Http\Resources\TransactionPaginateCollection;


class DashboardController extends Controller
{
    public function index()
    {
        $properties = Auth::user()->properties;
        $total_properties = $properties->count();
        $vacant = $properties->where('vacant', 1)->count();
        $occupied = $total_properties - $vacant;
        $transactions = Transaction::paginate(3);


        return response()->json([
            'user' => UserListResource::collection([Auth::user()]),
            'properties' => PropertyListResource::collection(Auth::user()->properties)->take(3),
            'transactions' => new TransactionPaginateCollection($transactions),
            'total_properties' => $total_properties,
            'total_occupied' => $occupied,

            'vacant' => $vacant
        ]);
    }
}
