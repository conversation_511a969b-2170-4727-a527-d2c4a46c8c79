<?php

namespace App\Repositories;

use App\Interfaces\TenantInterface;
use App\Models\Tenant;
use App\Traits\CommonHelperTrait;

class TenantRepository implements TenantInterface{
    use CommonHelperTrait;
    private Tenant $model;

    public function __construct(Tenant $model){
        $this->model = $model;
    }

    public function index($request){

        return Tenant::all();

    }

    public function status($request){

        return $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);

    }

    public function deletes($request){

        return $this->model->destroy((array)$request->ids);

    }

    public function getAll()
    {
        return Tenant::latest()->paginate(5);
    }

    public function store($request)
    {
        try {
            $tenantStore                       = new $this->model;
            $tenantStore->name                 = $request->name;
            $tenantStore->email                = $request->email;
            $tenantStore->phone                = $request->phone;
            $tenantStore->permanent_address    = $request->permanent_address;
            $tenantStore->city                 = $request->city;
            $tenantStore->state                = $request->state;
            $tenantStore->zip_code             = $request->zip_code;
            $tenantStore->occupation           = $request->occupation;
            $tenantStore->designation          = $request->designation;
            $tenantStore->nid                  = $request->nid;
            $tenantStore->passport             = $request->passport;
            $tenantStore->status               = $request->status;
            $tenantStore->image_id             = $this->UploadImageCreate($request->image, 'backend/uploads/tenants');
            $tenantStore->save();
            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function show($id)
    {
        return $this->model->find($id);
    }


    public function update($request, $id)
    {
        try {
            $tenantUpdate                       = new $this->model;
            $tenantUpdate->name                 = $request->name;
            $tenantUpdate->email                = $request->email;
            $tenantUpdate->phone                = $request->phone;
            $tenantUpdate->permanent_address    = $request->permanent_address;
            $tenantUpdate->city                 = $request->city;
            $tenantUpdate->state                = $request->state;
            $tenantUpdate->zip_code             = $request->zip_code;
            $tenantUpdate->occupation           = $request->occupation;
            $tenantUpdate->designation          = $request->designation;
            $tenantUpdate->nid                  = $request->nid;
            $tenantUpdate->passport             = $request->passport;
            $tenantUpdate->status               = $request->status;
            $tenantUpdate->image_id             = $this->UploadImageUpdate($request->image, 'backend/uploads/tenants',$tenantUpdate->image_id);
            $tenantUpdate->save();
            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function destroy($id)
    {
        try {
            $tenantDestroy   = $this->model->find($id);
            $this->UploadImageDelete($tenantDestroy->image_id); // delete image & record
            $tenantDestroy->delete();
            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

}
?>
