<?php

namespace App\Http\Controllers\Backend;

use App\Models\Blog;
use App\Models\Role;
use App\Models\User;
use App\Models\Search;
use App\Models\Language;
use App\Models\Permission;
use App\Models\Designation;
use Illuminate\Http\Request;
use App\Interfaces\UserInterface;
use App\Models\Property\Property;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;

class DashboardController extends Controller
{
    private $user;

    public function __construct(UserInterface $userInterface)
    {
        $this->middleware('auth');
        if (!Schema::hasTable('settings') && !Schema::hasTable('users')) {
            abort(400);
        }
        $this->user = $userInterface;
    }

    public function index(Request $request)
    {
        /*
        Summery
         */

        //Landlords
        $landlord['total'] = User::where('role_id', 4)->count();
        $landlord['active'] = User::where('role_id', 4)->where('status', 1)->count();
        $landlord['inactive'] = User::where('role_id', 4)->where('status', 0)->count();

        //Tenant
        $tenant['total'] = User::where('role_id', 5)->count();
        $tenant['active'] = User::where('role_id', 5)->where('status', 1)->count();
        $tenant['inactive'] = User::where('role_id', 5)->where('status', 0)->count();

        //Property
        $property['total'] = Property::count();
        $property['active'] = Property::where('status', 1)->count();
        $property['inactive'] = Property::where('status', 0)->count();

        //Blogs
        $blogs['total'] = Blog::count();
        $blogs['active'] = Blog::where('status', 1)->count();
        $blogs['inactive'] = Blog::where('status', 0)->count();

        //users
        $user = $this->user->getAll();

        $users = $this->user->index($request);
        $designations = Designation::query()->get(['id', 'title']);

        return view('backend.dashboard', [
            "users" => $users,
            "property" => $property,
            "blogs" => $blogs,
            "tenant" => $tenant,
            "user" => $user,
            "users" => $users,
            "designations" => $designations,
            "landlord" => $landlord,
        ]);
    }
    public function schoolDashboard()
    {
        /*
        Summery
         */

        //Users
        $users['total'] = User::count();
        $users['active'] = User::where('status', 1)->count();
        $users['inactive'] = User::where('status', 4)->count();

        //Roles
        $roles['total'] = Role::count();
        $roles['active'] = Role::where('status', 1)->count();
        $roles['inactive'] = Role::where('status', 4)->count();

        //Languages
        $languages['total'] = Language::count();
        $languages['active'] = Language::count(); //lanuage status are not assigned
        $languages['inactive'] = 0; //lanuage status are not assigned

        //Languages
        $permissions['total'] = Permission::count();
        $permissions['active'] = Permission::count(); //lanuage status are not assigned
        $permissions['inactive'] = 0; //lanuage status are not assigned

        //users
        $user = $this->user->getAll();

        return view('backend.school_dashboard', [
            "users" => $users,
            "roles" => $roles,
            "languages" => $languages,
            "permissions" => $permissions,
            "user" => $user,
        ]);
    }
    public function lmsDashboard()
    {
        /*
        Summery
         */

        //Users
        $users['total'] = User::count();
        $users['active'] = User::where('status', 1)->count();
        $users['inactive'] = User::where('status', 4)->count();

        //Roles
        $roles['total'] = Role::count();
        $roles['active'] = Role::where('status', 1)->count();
        $roles['inactive'] = Role::where('status', 4)->count();

        //Languages
        $languages['total'] = Language::count();
        $languages['active'] = Language::count(); //lanuage status are not assigned
        $languages['inactive'] = 0; //lanuage status are not assigned

        //Languages
        $permissions['total'] = Permission::count();
        $permissions['active'] = Permission::count(); //lanuage status are not assigned
        $permissions['inactive'] = 0; //lanuage status are not assigned

        //users
        $user = $this->user->getAll();

        return view('backend.lms_dashboard', [
            "users" => $users,
            "roles" => $roles,
            "languages" => $languages,
            "permissions" => $permissions,
            "user" => $user,
        ]);
    }
    public function crmDashboard()
    {
        /*
        Summery
         */

        //Users
        $users['total'] = User::count();
        $users['active'] = User::where('status', 1)->count();
        $users['inactive'] = User::where('status', 4)->count();

        //Roles
        $roles['total'] = Role::count();
        $roles['active'] = Role::where('status', 1)->count();
        $roles['inactive'] = Role::where('status', 4)->count();

        //Languages
        $languages['total'] = Language::count();
        $languages['active'] = Language::count(); //lanuage status are not assigned
        $languages['inactive'] = 0; //lanuage status are not assigned

        //Languages
        $permissions['total'] = Permission::count();
        $permissions['active'] = Permission::count(); //lanuage status are not assigned
        $permissions['inactive'] = 0; //lanuage status are not assigned

        //users
        $user = $this->user->getAll();

        return view('backend.landlord_dashboard', [
            "users" => $users,
            "roles" => $roles,
            "languages" => $languages,
            "permissions" => $permissions,
            "user" => $user,
        ]);
    }

    public function searchMenuData(Request $request)
    {
        $targets = Search::where('url', 'LIKE', "%{$request->searchData}%")->get();
        $view = View('backend.menu-autocomplete', compact('targets'))->render();
        return response()->json(['data' => $view]);
    }
}
