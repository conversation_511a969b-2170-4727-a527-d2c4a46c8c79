<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Mail\SendEmail;
use App\Models\User;
use App\Traits\ApiReturnFormatTrait;
use App\Traits\CommonHelperTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Throwable;

class AuthController extends Controller
{
    use ApiReturnFormatTrait, CommonHelperTrait;

    public function notLogined()
    {
        return response()->json([
            'result' => false,
            'api_end_point' => \request()->url(),
            'message' => 'Unauthenticated. Please login first',
            'error' => 'Token invalid or expired',
        ], 401);
    }
    public function register(Request $request)
    {


        $data = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|string|email|unique:users',
            'password' => 'required|string|min:8',
            'type' => 'required',
        ]);

        if ($data->fails()) {
            return response()->json([
                'errors' => $data->errors(),
            ], 422);
        }


        $user = User::create([
            'role_id' => $request->type == "landlord" ? 4 : 5,
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),

        ]);

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'access_token' => $token,
            'token_type' => 'Bearer',
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
            "date_of_birth" => $user->date_of_birth,
            "nid" => $user->nid,
            "passport" => $user->passport,
            "gender" => $user->gender,
            'role_id' => $user->role_id,
        ], 200);
    }

    public function login(Request $request)
    {
        $data = Validator::make($request->all(), [
            'email' => 'required',
            'password' => 'required',
        ]);

        if ($data->fails()) {
            return response()->json([
                'errors' => $data->errors(),
            ], 422);
        }

        if (!Auth::attempt($request->only('email', 'password'))) {
            return response()->json([
                'message' => 'Invalid login details',
            ], 401);
        }

        $user = User::where('email', $request['email'])->firstOrFail();
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'access_token' => $token,
            'token_type' => 'Bearer',
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
            "date_of_birth" => $user->date_of_birth,
            "nid" => $user->nid,
            "passport" => $user->passport,
            "gender" => $user->gender,
            'role_id' => $user->role_id,
        ], 200);
    }

    public function user(Request $request)
    {
        try {
            $user = User::where('id', Auth::user()->id)->first();
            $data['messages'] = "Profile Details";
            $data['profile_info'] = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                "date_of_birth" => $user->date_of_birth,
                "join_date" => $user->join_date,
                "nid" => $user->nid,
                "passport" => $user->passport,
                "occupation" => $user->occupation,
                "institution" => $user->institution,
                "designation" => $user->designation->title,
                "gender" => $user->gender,
                'role_id' => $user->role_id,
            ];
            $response = $this->responseWithSuccess($data['messages'], $data, 200);
            return $response;
        } catch (\Throwable $th) {
            return $this->responseExceptionError($th->getMessage(), $th->getTrace(), 500);
        }
    }


    public function requestOtp(Request $request)
    {

        // validation
        $data = Validator::make($request->all(), [
            'email' => 'required|max:255',
        ]);
        if ($data->fails()) {
            return response()->json([
                'errors' => $data->errors(),
            ], 422);
        }

        $otp = rand(100000, 999999);

        Log::info("otp = " . $otp);

        $user = User::where('email', $request->email)->firstOrFail();
        if ($user) {
            $user->otp = $otp;
            $mail_details = [
                'subject' => 'Testing Application OTP',
                'body' => 'Your OTP is : ' . $otp,
            ];

            try {
                Mail::to($request->email)->send(new SendEmail($mail_details));
            } catch (Throwable $e) {
                Log::error($e->getMessage());
            }

            // return response(["status" => 200, "message" => "OTP sent successfully"]);
            return response(["status" => 200, "message" => "OTP sent successfully " . $otp]);
        } else {
            return response(["status" => 401, 'message' => 'Invalid']);
        }
    }
    public function forgetPassword(Request $request)
    {

        // validation
        $data = Validator::make($request->all(), [
            'email' => 'required|max:255',
        ]);
        if ($data->fails()) {
            return response()->json([
                'errors' => $data->errors(),
            ], 422);
        }

        $otp = rand(100000, 999999);

        Log::info("otp = " . $otp);

        $user = User::where('email', $request->email)->first();
        if ($user) {
            $user->otp = $otp;
            $mail_details = [
                'subject' => 'Forget Password OTP',
                'body' => 'Your OTP is : ' . $otp,
            ];

            $user->save();

            try {
                Mail::to($request->email)->send(new SendEmail($mail_details));
            } catch (Throwable $e) {
                Log::error($e->getMessage());
            }

            // return response(["status" => 200, "message" => "OTP sent successfully"]);
            return response(["status" => 200, "message" => "OTP sent successfully " . $otp]);
        } else {
            return response(["status" => 401, 'message' => 'Invalid']);
        }
    }

    public function verifyOtp(Request $request)
    {

        $data = Validator::make($request->all(), [
            'otp' => 'required|numeric',
        ]);
        if ($data->fails()) {
            return response()->json([
                'errors' => $data->errors(),
            ], 422);
        }

        $user = User::where('otp', '=', $request->otp)->firstOrFail();
        if ($user) {
            auth()->login($user, true);
            $user->otp = null;
            $user->save();
            $accessToken = auth()->user()->createToken('authToken')->accessToken;
            return response(["status" => 200, "message" => "Success", 'user' => auth()->user(), 'access_token' => $accessToken]);
        } else {
            return response(["status" => 401, 'message' => 'Not Found', 'user' => auth()->user(), 'access_token']);
        }
    }

    public function resetPassword(Request $request)
    {

        $data = Validator::make($request->all(), [
            'otp' => 'required|numeric',
            'email' => 'required|email',
            'password' => 'required',
        ]);
        if ($data->fails()) {
            return response()->json([
                'errors' => $data->errors(),
            ], 422);
        }
        $user = User::where([
            ['otp', '=', $request->otp],
            ['email', '=', $request->email],
        ])->first();
        if ($user) {
            $user->password = Hash::make($request->password);
            $user->otp = null;
            $user->save();

            auth()->login($user, true);
            $accessToken = auth()->user()->createToken('authToken')->accessToken;
            return response(["status" => 200, "message" => "Success", 'user' => auth()->user(), 'access_token' => $accessToken]);
        } else {
            return response(["status" => 401, 'message' => 'Not Found', 'user' => auth()->user()]);
        }
    }

    public function profileUpdate(Request $request)
    {


        $user = Auth::user();
        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        if ($request->has('name') && $request->get('name') != null) {
            $user->name = $request->input('name');
        }
        if ($request->has('email') && $request->get('email') != null) {
            $user->email = $request->input('email');
        }
        if ($request->has('phone') && $request->get('phone') != null) {
            $user->phone = $request->input('phone');
        }
        if ($request->has('date_of_birth') && $request->get('date_of_birth') != null) {
            $user->date_of_birth = $request->input('date_of_birth');
        }
        if ($request->has('nid') && $request->get('nid') != null) {
            $user->nid = $request->input('nid');
        }
        if ($request->has('designation_id') && $request->get('designation_id') != null) {
            $user->designation_id = $request->input('designation_id');
        }
        if ($request->has('occupation') && $request->get('occupation') != null) {
            $user->occupation = $request->input('occupation');
        }
        if ($request->has('join_date') && $request->get('join_date') != null) {
            $user->join_date = $request->input('join_date');
        }
        if ($request->has('passport') && $request->get('passport') != null) {
            $user->passport = $request->input('passport');
        }
        if ($request->has('institution') && $request->get('institution') != null) {
            $user->institution = $request->input('institution');
        }
        if ($request->has('gender') && $request->get('gender') != null) {
            $user->gender = $request->input('gender');
        }
        $user->save();
        // $token = $user->createToken('auth_token')->plainTextToken;
        $data['messages'] = 'Profile update successfully';
        $response = $this->responseWithSuccess($data['messages'], $data, 200);
        return $response;
    }

    public function changePassword(Request $request)
    {
        $data = Validator::make($request->all(), [
            'password' => 'required',
            'new_password' => 'required|string|min:8',
            'confirm_password' => 'required|string|min:8',
        ]);
        if ($data->fails()) {
            return response()->json([
                'errors' => $data->errors(),
            ], 422);
        }
        $user = Auth::user();
        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $currentPassword = $request->input('password');
        $newPassword = $request->input('new_password');
        $confirmPassword = $request->input('confirm_password');

        if (!Hash::check($currentPassword, $user->password)) {
            return response()->json(['message' => 'Current password is incorrect'], 422);
        }

        if ($newPassword !== $confirmPassword) {
            return response()->json(['message' => 'New password and confirm password do not match'], 422);
        }

        $user->password = Hash::make($newPassword);
        $user->save();

        return response()->json(['message' => 'Password changed successfully'], 200);
    }
}
