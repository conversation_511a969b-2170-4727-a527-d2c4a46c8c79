<?php $__env->startSection('title', @$data['title']); ?>
<?php $__env->startSection('content'); ?>


    <!-- prodcuts_area ::start  -->
    <div class="prodcuts_area ">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-xl-3">
                    <div id="product_category_chose" class="product_category_chose mb_30 mt_15">
                        <div class="course_title mb_15 d-flex align-items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="19.5" height="13" viewBox="0 0 19.5 13">
                                <g id="filter-icon" transform="translate(28)">
                                    <rect id="Rectangle_1" data-name="Rectangle 1" width="19.5" height="2"
                                        rx="1" transform="translate(-28)" fill="#F99417" />
                                    <rect id="Rectangle_2" data-name="Rectangle 2" width="15.5" height="2"
                                        rx="1" transform="translate(-26 5.5)" fill="#F99417" />
                                    <rect id="Rectangle_3" data-name="Rectangle 3" width="5" height="2"
                                        rx="1" transform="translate(-20.75 11)" fill="#F99417" />
                                </g>
                            </svg>
                            <h5 class="font_16 f_w_700 mb-0 "><?php echo e(_trans('landlord.Filter Category')); ?></h5>
                            <div class="catgory_sidebar_closeIcon flex-fill justify-content-end d-flex d-lg-none">
                                <button id="catgory_sidebar_closeIcon"
                                    class="home10_primary_btn2 gj-cursor-pointer mb-0 small_btn"><?php echo e(_trans('landlord.close')); ?></button>
                            </div>
                        </div>
                        <div class="course_category_inner">
                            <div class="single_pro_categry">
                                <h4 class="font_18 f_w_700 ">
                                    <?php echo e(_trans('landlord.Purpose')); ?>

                                </h4>
                                <ul class="Check_sidebar mb_35">
                                    <li>
                                        <label class="primary_checkbox d-flex"   onchange="updateProperties()">
                                            <input type="checkbox" value="RENT" class="__purpose" >
                                            <span class="checkmark mr_10"></span>
                                            <span class="label_name"><?php echo e(_trans('landlord.RENT')); ?></span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="primary_checkbox d-flex"   onchange="updateProperties()">
                                            <input type="checkbox" value="SELL" class="__purpose" >
                                            <span class="checkmark mr_10"></span>
                                            <span class="label_name"><?php echo e(_trans('landlord.SELL')); ?></span>
                                        </label>
                                    </li>
                                </ul>
                            </div>
                            <div class="single_pro_categry">
                                <div
                                    class="d-flex align-items-center gap-2 land_border_bottom pb-2 mb-3 justify-content-between">
                                    <h4 class="font_18 f_w_700 m-0 border-0 p-0">
                                        <?php echo e(_trans('landlord.Filter by Categories')); ?>

                                    </h4>
                                </div>
                                <ul class="Check_sidebar mb_35" id="category_list_loading">
                                </ul>
                            </div>


                            <div class="single_pro_categry">
                                <h4 class="font_18 f_w_700">
                                    <?php echo e(_trans('landlord.Filter by Beds')); ?>

                                </h4>
                                <ul class="Check_sidebar mb_35">
                                    <?php for($i=1; $i<=5; $i++): ?>
                                    <li>
                                        <label class="primary_checkbox d-flex"   onchange="updateProperties()">
                                            <input type="checkbox" value="<?php echo e($i); ?>" class="__beds"  >
                                            <span class="checkmark mr_10"></span>
                                            <span class="label_name"> <?php echo e($i); ?> <?php echo e($i>=2 ? ' Beds': ' Bed'); ?></span>
                                        </label>
                                    </li>
                                    <?php endfor; ?>
                                </ul>
                            </div>
                            <div class="single_pro_categry">
                                <h4 class="font_18 f_w_700">
                                    <?php echo e(_trans('landlord.Filter by Baths')); ?>

                                </h4>
                                <ul class="Check_sidebar mb_35">
                                    <?php for($i=1; $i<=5; $i++): ?>
                                    <li>
                                        <label class="primary_checkbox d-flex"   onchange="updateProperties()">
                                            <input type="checkbox" value="<?php echo e($i); ?>" class="__baths" >
                                            <span class="checkmark mr_10"></span>
                                            <span class="label_name"> <?php echo e($i); ?> <?php echo e($i>=2 ? ' Baths': ' Bath'); ?></span>
                                        </label>
                                    </li>
                                    <?php endfor; ?>
                                </ul>
                            </div>
                            <div class="single_pro_categry">
                                <h4 class="font_18 f_w_700">
                                    <?php echo e(_trans('landlord.Area')); ?> (<?php echo e(_trans('landlord.sqft')); ?>)
                                </h4>
                                <ul class="Check_sidebar mb_35">
                                    <?php for($i=1; $i<=7; $i++): ?>
                                    <li>
                                        <label class="primary_checkbox d-flex"   onchange="updateProperties()">
                                            <input type="checkbox" value="<?php echo e($i * 1000); ?>-<?php echo e($i * 1000 + 1000); ?>" class="__sqfts" >
                                            <span class="checkmark mr_10"></span>
                                            <span class="label_name"><?php echo e($i * 1000); ?>-<?php echo e($i * 1000 + 1000); ?> <?php echo e(_trans('landlord.Sq Ft')); ?></span>
                                        </label>
                                    </li>
                                    <?php endfor; ?>
                                </ul>
                            </div>
                            <div class="single_pro_categry d-none">
                                <h4 class="font_18 f_w_700">
                                    <?php echo e(_trans('landlord.Filter by Rating')); ?>

                                </h4>
                                <ul class="rating_lists mb_35">
                                    <li>
                                        <div class="ratings">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="ratings">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <span><?php echo e(_trans('landlord.And Up')); ?></span>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="ratings">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <span><?php echo e(_trans('landlord.And Up')); ?></span>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="ratings">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <span><?php echo e(_trans('landlord.And Up')); ?></span>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="ratings">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <i class="fas fa-star unrated"></i>
                                            <span><?php echo e(_trans('landlord.And Up')); ?></span>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="single_pro_categry d-none">
                                <h4 class="font_18 f_w_700">
                                    <?php echo e(_trans('landlord.Filter by Price')); ?>

                                </h4>
                                <div class="filter_wrapper">
                                    <div id="slider-range"></div>
                                    <div class="d-flex align-items-center prise_line">
                                        <button class="home10_primary_btn2 mr_20 mb-0 small_btn"><?php echo e(_trans('landlord.Filter')); ?></button>
                                        <span><?php echo e(_trans('landlord.Price')); ?>: </span> <input type="text" id="amount" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-8 col-xl-9">
                    <div class="row ">
                        <div class="col-12">
                            <div class="box_header d-flex flex-wrap align-items-center justify-content-between">
                                <div class="d-flex gap-2 flex-fill align-items-center flex-wrap">
                                    <h5 class="font_16 f_w_500 mr_10 mb-0" id="__countResults">7 Results</h5>
                                    <div class="keyword_lists d-flex gap-2 align-items-center flex-wrap" id="__keyword_lists">

                                    </div>
                                </div>
                                <div class="box_header_right ">
                                    <div class="short_select d-flex align-items-center gap_10 flex-wrap">
                                        <div class="prduct_showing_style">
                                            <ul class="nav align-items-center" id="myTab" role="tablist">
                                                
                                                
                                            </ul>
                                        </div>
                                        <div class="shorting_box d-none">
                                            <select class="o_land_select">
                                                <option data-display="Filter By City"><?php echo e(_trans('landlord.City')); ?></option>
                                                <option value="1">Rangpur</option>
                                                <option value="2">Dhaka</option>
                                                <option value="3">Chattogram</option>
                                                <option value="4">Sylhet</option>
                                                <option value="5">Barishal </option>
                                            </select>
                                        </div>
                                        <div class="shorting_box d-none">
                                            <select class="o_land_select">
                                                <option data-display="Completion Status"><?php echo e(_trans('landlord.Completion Status')); ?></option>
                                                <option value="1">ALL</option>
                                                <option value="2">Ready</option>
                                                <option value="3">Under construcion</option>
                                                <option value="4">Low to High</option>
                                                <option value="5">High to Low </option>
                                            </select>
                                        </div>
                                        <div class="flex-fill text-end">
                                            <div class="category_toggler d-inline-block d-lg-none  gj-cursor-pointer">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="19.5" height="13"
                                                    viewBox="0 0 19.5 13">
                                                    <g id="filter-icon" transform="translate(28)">
                                                        <rect id="Rectangle_1" data-name="Rectangle 1" width="19.5"
                                                            height="2" rx="1" transform="translate(-28)"
                                                            fill="#F99417" />
                                                        <rect id="Rectangle_2" data-name="Rectangle 2" width="15.5"
                                                            height="2" rx="1" transform="translate(-26 5.5)"
                                                            fill="#F99417" />
                                                        <rect id="Rectangle_3" data-name="Rectangle 3" width="5"
                                                            height="2" rx="1"
                                                            transform="translate(-20.75 11)" fill="#F99417" />
                                                    </g>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-content mb_30" id="myTabContent">
                        <div class="tab-pane fade show active" id="home" role="tabpanel"
                            aria-labelledby="home-tab">
                            <!-- content  -->
                            <div class="row custom_rowProduct" id="filtering-property-items">


                            </div>
                            <!--/ content  -->
                        </div>
                        <div class="tab-pane fade " id="profile" role="tabpanel" aria-labelledby="profile-tab">
                            <!-- content  -->
                            <div class="row">
                                <div class="col-xl-12">
                                    <div class="product_widget5 mb_30 list_style_product">
                                        <div class="product_thumb_upper m-0">
                                            <a href="<?php echo e(route('properties.details')); ?>" class="thumb">
                                                <img src="<?php echo e(url('frontend/img/o_land_property/9.png')); ?>" alt="">
                                            </a>
                                            <div class="product_action">
                                                <a href="compare.php">
                                                    <i class="ti-control-shuffle"></i>
                                                </a>
                                                <a data-bs-toggle="modal" data-bs-target="#theme_modal">
                                                    <i class="ti-eye"></i>
                                                </a>
                                                <a href="#">
                                                    <i class="ti-star"></i>
                                                </a>
                                            </div>
                                            <span class="badge_1"> -20% </span>
                                            <span class="badge_1 style2 text-uppercase"> <?php echo e(_trans('landlord.New')); ?> </span>
                                        </div>
                                        <div class="product__meta text-start">
                                            <div class="d-flex justify-content-between flex-wrap gap-2">
                                                <span class="product_banding secondary_text">Apartment</span>
                                                <h5 class="f_w_600 font_16">৳ 55,90,998</h5>
                                            </div>
                                            <a href="<?php echo e(route('properties.details')); ?>">
                                                <h4>Apartment For Sale In Shantibag</h4>
                                            </a>
                                            <div class="d-flex flex-wrap gap-3  mt_10">
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="fas fa-bed fw-bolder body_text"></i>4 Bed</span>
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="far fa-bath fw-bolder body_text"></i>5 bath</span>
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="far fa-border-all fw-bolder body_text"></i>1400sqft</span>
                                            </div>
                                            <p>Powered by 4500mAh battery which lets you be more energetic in business
                                                games, videos and reading. 5MP selfie camera allows.</p>
                                            <div class="d-flex gap-2 mt-3">
                                                <?php echo $__env->make('frontend.property.partials.call-email', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-12">
                                    <div class="product_widget5 mb_30 list_style_product">
                                        <div class="product_thumb_upper m-0">
                                            <a href="<?php echo e(route('properties.details')); ?>" class="thumb">
                                                <img src="<?php echo e(url('frontend/img/o_land_property/1.png')); ?>" alt="">
                                            </a>
                                            <div class="product_action">
                                                <a href="compare.php">
                                                    <i class="ti-control-shuffle"></i>
                                                </a>
                                                <a data-bs-toggle="modal" data-bs-target="#theme_modal">
                                                    <i class="ti-eye"></i>
                                                </a>
                                                <a href="#">
                                                    <i class="ti-star"></i>
                                                </a>
                                            </div>
                                            <span class="badge_1"> -20% </span>
                                            <span class="badge_1 style2 text-uppercase"> New </span>
                                        </div>
                                        <div class="product__meta text-start">
                                            <div class="d-flex justify-content-between flex-wrap gap-2">
                                                <span class="product_banding secondary_text">Apartment</span>
                                                <h5 class="f_w_600 font_16">৳ 55,90,998</h5>
                                            </div>
                                            <a href="<?php echo e(route('properties.details')); ?>">
                                                <h4>Apartment For Sale In Shantibag</h4>
                                            </a>
                                            <div class="d-flex flex-wrap gap-3  mt_10">
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="fas fa-bed fw-bolder body_text"></i>4 Bed</span>
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="far fa-bath fw-bolder body_text"></i>5 bath</span>
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="far fa-border-all fw-bolder body_text"></i>1400sqft</span>
                                            </div>
                                            <p>Powered by 4500mAh battery which lets you be more energetic in business
                                                games, videos and reading. 5MP selfie camera allows.</p>
                                            <div class="d-flex gap-2 mt-3">
                                                <?php echo $__env->make('frontend.property.partials.call-email', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-12">
                                    <div class="product_widget5 mb_30 list_style_product">
                                        <div class="product_thumb_upper m-0">
                                            <a href="<?php echo e(route('properties.details')); ?>" class="thumb">
                                                <img src="<?php echo e(url('frontend/img/o_land_property/10.png')); ?>"
                                                    alt="">
                                            </a>
                                            <div class="product_action">
                                                <a href="compare.php">
                                                    <i class="ti-control-shuffle"></i>
                                                </a>
                                                <a data-bs-toggle="modal" data-bs-target="#theme_modal">
                                                    <i class="ti-eye"></i>
                                                </a>
                                                <a href="#">
                                                    <i class="ti-star"></i>
                                                </a>
                                            </div>
                                            <span class="badge_1"> -20% </span>
                                            <span class="badge_1 style2 text-uppercase"> New </span>
                                        </div>
                                        <div class="product__meta text-start">
                                            <div class="d-flex justify-content-between flex-wrap gap-2">
                                                <span class="product_banding secondary_text">Apartment</span>
                                                <h5 class="f_w_600 font_16">৳ 55,90,998</h5>
                                            </div>
                                            <a href="<?php echo e(route('properties.details')); ?>">
                                                <h4>Apartment For Sale In Shantibag</h4>
                                            </a>
                                            <div class="d-flex flex-wrap gap-3  mt_10">
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="fas fa-bed fw-bolder body_text"></i>4 Bed</span>
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="far fa-bath fw-bolder body_text"></i>5 bath</span>
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="far fa-border-all fw-bolder body_text"></i>1400sqft</span>
                                            </div>
                                            <p>Powered by 4500mAh battery which lets you be more energetic in business
                                                games, videos and reading. 5MP selfie camera allows.</p>
                                            <div class="d-flex gap-2 mt-3">
                                                <?php echo $__env->make('frontend.property.partials.call-email', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-12">
                                    <div class="product_widget5 mb_30 list_style_product">
                                        <div class="product_thumb_upper m-0">
                                            <a href="<?php echo e(route('properties.details')); ?>" class="thumb">
                                                <img src="<?php echo e(url('frontend/img/o_land_property/11.png')); ?>"
                                                    alt="">
                                            </a>
                                            <div class="product_action">
                                                <a href="compare.php">
                                                    <i class="ti-control-shuffle"></i>
                                                </a>
                                                <a data-bs-toggle="modal" data-bs-target="#theme_modal">
                                                    <i class="ti-eye"></i>
                                                </a>
                                                <a href="#">
                                                    <i class="ti-star"></i>
                                                </a>
                                            </div>
                                            <span class="badge_1"> -20% </span>
                                            <span class="badge_1 style2 text-uppercase"> New </span>
                                        </div>
                                        <div class="product__meta text-start">
                                            <div class="d-flex justify-content-between flex-wrap gap-2">
                                                <span class="product_banding secondary_text">Apartment</span>
                                                <h5 class="f_w_600 font_16">৳ 55,90,998</h5>
                                            </div>
                                            <a href="<?php echo e(route('properties.details')); ?>">
                                                <h4>Apartment For Sale In Shantibag</h4>
                                            </a>
                                            <div class="d-flex flex-wrap gap-3  mt_10">
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="fas fa-bed fw-bolder body_text"></i>4 Bed</span>
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="far fa-bath fw-bolder body_text"></i>5 bath</span>
                                                <span
                                                    class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                        class="far fa-border-all fw-bolder body_text"></i>1400sqft</span>
                                            </div>
                                            <p>Powered by 4500mAh battery which lets you be more energetic in business
                                                games, videos and reading. 5MP selfie camera allows.</p>
                                            <div class="d-flex gap-2 mt-3">
                                                <?php echo $__env->make('frontend.property.partials.call-email', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--/ content  -->
                        </div>
                    </div>

                    <!-- pagination -->
                   <div class="row d-none">
                   </div>
                    <div class="o_land_pagination d-flex align-items-center pt_15 mb_20 d-none">
                        <a class="arrow_btns d-inline-flex align-items-center justify-content-center" href="#">
                            <i class="fas fa-chevron-left"></i>
                            <span>Prev</span>
                        </a>
                        <a class="page_counter active" href="#">1</a>
                        <a class="page_counter" href="#">2</a>
                        <a class="page_counter" href="#">3</a>
                        <a href="#">...</a>
                        <a class="page_counter" href="#">8</a>
                        <a class="arrow_btns d-inline-flex align-items-center justify-content-center" href="#">
                            <span>Next</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script'); ?>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>



        function updateProperties() {

            var search = $('#topSearchBar').val();

            var beds = [];
            $('.__beds:checked').each(function() {
                beds.push($(this).val());
            });

            var baths = [];
            $('.__baths:checked').each(function() {
                baths.push($(this).val());
            });

            var sqfts = [];
            $('.__sqfts:checked').each(function() {
                sqfts.push($(this).val());
            });

            var purpose = [];
            $('.__purpose:checked').each(function() {
                purpose.push($(this).val());
            });

            var categories = [];
            $('.__categories:checked').each(function() {
                categories.push($(this).val());
            });

            $.ajax({
                url: "<?php echo e(route('properties.filters')); ?>",
                method: "POST",
                data: {
                    search: search,
                    purpose: purpose,
                    categories: categories,
                    beds: beds,
                    baths: baths,
                    sqfts: sqfts,
                    _token: "<?php echo e(csrf_token()); ?>"
                },
                success: function(data) {
                    console.log('---------------------------------');
                    console.log(data);
                    console.log('---------------------------------');

                    let html = '';
                    let properties = data.properties;
                    $.each(properties, function(i, property) {
                        console.log(property);

                        html += `
                            <div class="col-xl-4 col-lg-4 col-md-6 col-6">
                                <div class="product_widget5 mb_30">
                                    <div class="product_thumb_upper">
                                        <a href="${property.details_url}" class="thumb">
                                            <img src="${property.image}" alt=""
                                                class="${property.image}">
                                        </a>
                                        <div class="product_action">
                                            <a href="compare.php">
                                                <i class="ti-control-shuffle"></i>
                                            </a>
                                            <a data-bs-toggle="modal" data-bs-target="#theme_modal">
                                                <i class="ti-eye"></i>
                                            </a>
                                            <a href="#">
                                                <i class="ti-star"></i>
                                            </a>
                                        </div>
                                        <span class="badge_1"> -20% </span>
                                        <span class="badge_1 style2 text-uppercase"> ${property.deal_type} </span>
                                    </div>
                                    <div class="product__meta text-start">
                                        <div class="d-flex justify-content-between flex-wrap gap-2">
                                            <span class="product_banding secondary_text">${property.category}</span>
                                            <h5 class="f_w_600 font_16">৳ ${property.price}</h5>
                                        </div>
                                        <a href="${property.details_url}">
                                            <h4>${property.name}</h4>
                                        </a>
                                        <div class="d-flex flex-wrap gap-3  mt_10">
                                            <span
                                                class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                    class="fas fa-bed fw-bolder body_text"></i>${property.bedrooms} Bed</span>
                                            <span
                                                class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                    class="far fa-bath fw-bolder body_text"></i>${property.bathrooms} bath</span>
                                            <span
                                                class="body_text d-flex gap_6 align-items-center justify-content-center font_14"><i
                                                    class="far fa-border-all fw-bolder body_text"></i>${property.size} sqft</span>
                                        </div>
                                        <div class="d-flex gap-2 mt-3">
                                            <a href="javascript:void(0)" onclick="showForRegister('call',<?php if(Auth::check()): ?> 'logged' <?php else: ?> 'notlogged' <?php endif; ?>  )"
                                                class="o_land_primary_btn small_btn radius_5px flex-fill">Call</a>
                                            <a href="javascript:void(0)" onclick="showForRegister('email',<?php if(Auth::check()): ?> 'logged' <?php else: ?> 'notlogged' <?php endif; ?>  )"
                                                class="o_land_primary_btn2 small_btn radius_5px flex-fill">Email</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;

                    });

                    $('#filtering-property-items').html(html);



                    let keywordStr = "";
                    let keywords =data.keyword??[];
                    console.log(keywords);
                    $.each(keywords, function(i, keyword) {

                        keywordStr += `
                        <button class="tag_added alert fade show d-flex align-items-center m-0">${keyword}
                            <span
                                class="position-relative p-0 ms-2 d-flex align-items-center justify-content-center"
                                data-bs-dismiss="alert">
                                <i class="ti-close"></i>
                            </span>
                        </button>
                        `;

                    });


                    $('#__keyword_lists').html(keywordStr);

                    $('#__countResults').html(data.count);
                },
                error: function(data) {
                    console.log(data);
                }
            });
        }

        function categoryLoad(){

            $('#category_list_loading').empty();
            // ajax call
            $.ajax({
                url: "<?php echo e(route('properties.categories')); ?>",
                method: "POST",
                data: {
                    _token: "<?php echo e(csrf_token()); ?>"
                },
                success: function(data) {
                    let html = '';
                    let categories = data.categories;
                    // categories loop
                    $.each(categories, function(i, category) {
                        html += `
                        <li>
                            <label class="primary_checkbox d-flex"   onchange="updateProperties()">
                                <input type="checkbox" name="" value="${category.id}" class="__categories">
                                <span class="checkmark mr_10"></span>
                                <span class="label_name">${category.name}</span>
                            </label>
                        </li>

                        `;
                    });

                    $('#category_list_loading').html(html);
                }
            });
        }
        categoryLoad();
        updateProperties();
    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\landlord\resources\views/frontend/property/index.blade.php ENDPATH**/ ?>