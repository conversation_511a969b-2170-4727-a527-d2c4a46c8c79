<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\Setting;
use Illuminate\Support\Str;
use App\Models\Locations\Country;
use App\Interfaces\CountryInterface;
use Illuminate\Support\Facades\Schema;
use App\Interfaces\PermissionInterface;
use App\Http\Requests\ContactStoreRequest;
use App\Http\Requests\Country\CountryStoreRequest;
use App\Http\Requests\Country\CountryUpdateRequest;

class CountryController extends Controller
{
    private $country;
    private $permission;

    function __construct(CountryInterface $countryInterface, PermissionInterface $permissionInterface)
    {

        if (!Schema::hasTable('settings') && !Schema::hasTable('users')) {
            abort(400);
        }
        $this->country   = $countryInterface;
        $this->permission = $permissionInterface;
    }

    public function index()
    {

        $data['countries']  = $this->country->getAll();
        $data['title']      = ___('common.Countries');
        return view('backend.country.index', compact('data'));
    }

    public function create()
    {

        $data['title']       = ___('common.create_country');
        $data['permissions'] = $this->permission->all();
        return view('backend.country.create', compact('data'));
    }

    public function store(CountryStoreRequest $request)
    {
        $result = $this->country->store($request);
        if ($result) {
            return redirect()->route('countries.index')->with('success', ___('alert.country_created_successfully'));
        }
        return redirect()->route('countries.index')->with('danger', ___('alert.something_went_wrong_please_try_again'));
    }

    public function edit($id)
    {
        $data['country']    = $this->country->show($id);
        $data['title']       = ___('common.countries');
        $data['permissions'] = $this->permission->all();
        return view('backend.country.edit', compact('data'));
    }

    public function update(CountryUpdateRequest $request, $id)
    {
        $result = $this->country->update($request, $id);
        if ($result) {
            // dd($result);
            return redirect()->route('countries.index')->with('success', ___('alert.country_updated_successfully'));
        }
        return redirect()->route('countries.index')->with('danger', ___('alert.something_went_wrong_please_try_again'));
    }

    public function delete($id)
    {
        if ($this->country->destroy($id)) :
            $success[0] = "Deleted Successfully";
            $success[1] = "Success";
            $success[2] = "Deleted";
        else :
            $success[0] = "Something went wrong, please try again.";
            $success[1] = 'error';
            $success[2] = "oops";
        endif;
        return response()->json($success);
    }
}
