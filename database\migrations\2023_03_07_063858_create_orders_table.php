<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('master_order_id')->constrained('master_orders')->onDelete('cascade');
            $table->foreignId('property_id')->constrained('properties')->onDelete('cascade');
            $table->foreignId('tenant_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('builder_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('landowner_id')->constrained('users')->onDelete('cascade');
            // advertisements
            $table->foreignId('advertisement_id')->constrained('advertisements')->onDelete('cascade');

            $table->double('total_amount', 16, 2)->default(0);
            $table->double('discount_amount')->default(0);

            // campaign info
            $table->string('campaign_name')->nullable();

            //coupon info
            $table->string('coupon_code')->nullable();
            $table->double('coupon_discount_amount')->default(0);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
};
