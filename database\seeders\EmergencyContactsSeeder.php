<?php

namespace Database\Seeders;

use App\Models\EmergencyContact;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Property\PropertyTenant;


class EmergencyContactsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $tenants = PropertyTenant::all();
        foreach ($tenants as $tenant) {
            $data = [
                [
                    'name' => '<PERSON>',
                    'phone' => '+****************',
                    'relation' => 'Brother',
                    'email' => '<EMAIL>',
                    'image_id' => 124,
                    'occupied' => 'businessman',
                ],
                [
                    'name' => '<PERSON>',
                    'phone' => '+****************',
                    'relation' => 'Sister',
                    'email' => '<EMAIL>',
                    'image_id' => 259,
                    'occupied' => 'Engineer',
                ],
                [
                    'name' => '<PERSON>',
                    'phone' => '+****************',
                    'relation' => 'Father',
                    'email' => '<EMAIL>',
                    'image_id' => 376,
                    'occupied' => 'businessman',
                ],
                [
                    'name' => '<PERSON>',
                    'phone' => '+****************',
                    'relation' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'image_id' => 423,
                    'occupied' => 'businessman',
                ],
                [
                    'name' => 'Sophia <PERSON>',
                    'phone' => '+****************',
                    'relation' => 'Brother',
                    'email' => '<EMAIL>',
                    'image_id' => 555,
                    'occupied' => 'service holder',
                ],
                [
                    'name' => 'James Kim',
                    'phone' => '+****************',
                    'relation' => 'Sister',
                    'email' => '<EMAIL>',
                    'image_id' => 662,
                    'occupied' => 'Banker',
                ],
                [
                    'name' => 'Ashley Nguyen',
                    'phone' => '+****************',
                    'relation' => 'Mother',
                    'email' => '<EMAIL>',
                    'image_id' => 713,
                    'occupied' => 'Manager',
                ],
                [
                    'name' => 'Dylan Patel',
                    'phone' => '+****************',
                    'relation' => 'Brother',
                    'email' => '<EMAIL>',
                    'image_id' => 879,
                    'occupied' => 'businessman',
                ]
            ];
            // $contacts = PropertyTenant::where('id', $tenant->user_id)->get();
            $types = ['emergency', 'reference'];

            foreach ($data as $innerArray) {
                EmergencyContact::create([
                    'property_tenant_id' => $tenant->id,
                    'name' => $innerArray['name'],
                    'relation' => $innerArray['relation'],
                    'phone' => $innerArray['phone'],
                    'email' => $innerArray['email'],
                    'image_id' => rand(1,10),
                    'occupied' => $innerArray['occupied'],
                    'type' => $types[array_rand($types, 1)]
                ]);
            }

        }
    }
}
