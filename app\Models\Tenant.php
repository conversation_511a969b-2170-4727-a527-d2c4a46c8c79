<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tenant extends Model
{
    use HasFactory;
    protected $fillable=[
        'name',
        'email',
        'phone',
        'permanent_address',
        'city',
        'state',
        'zip_code',
        'occupation',
        'designation',
        'image',
        'nid',
        'passport'

    ];
    public function image(){
        return $this-> belongsTo(Image::class);
    }


}
