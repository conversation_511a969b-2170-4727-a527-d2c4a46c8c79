<?php

namespace Database\Seeders;

use App\Models\FlagIcon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;

class FlagIconSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();
        FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ad','title' => 'ad',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ae','title' => 'ae',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-af','title' => 'af',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ag','title' => 'ag',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ai','title' => 'ai',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-al','title' => 'al',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-am','title' => 'am',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ao','title' => 'ao',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-aq','title' => 'aq',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ar','title' => 'ar',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-as','title' => 'as',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-at','title' => 'at',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-au','title' => 'au',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-aw','title' => 'aw',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ax','title' => 'ax',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-az','title' => 'az',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ba','title' => 'ba',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bb','title' => 'bb',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bd','title' => 'bd',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-be','title' => 'be',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bf','title' => 'bf',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bg','title' => 'bg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bh','title' => 'bh',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bi','title' => 'bi',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bj','title' => 'bj',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bl','title' => 'bl',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bm','title' => 'bm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bn','title' => 'bn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bo','title' => 'bo',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bq','title' => 'bq',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-br','title' => 'br',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bs','title' => 'bs',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bt','title' => 'bt',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bv','title' => 'bv',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bw','title' => 'bw',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-by','title' => 'by',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-bz','title' => 'bz',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ca','title' => 'ca',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cc','title' => 'cc',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cd','title' => 'cd',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cf','title' => 'cf',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cg','title' => 'cg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ch','title' => 'ch',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ci','title' => 'ci',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ck','title' => 'ck',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cl','title' => 'cl',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cm','title' => 'cm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cn','title' => 'cn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-co','title' => 'co',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cr','title' => 'cr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cu','title' => 'cu',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cv','title' => 'cv',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cw','title' => 'cw',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cx','title' => 'cx',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cy','title' => 'cy',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-cz','title' => 'cz',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-de','title' => 'de',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-dj','title' => 'dj',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-dk','title' => 'dk',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-dm','title' => 'dm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-do','title' => 'do',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-dz','title' => 'dz',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ec','title' => 'ec',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ee','title' => 'ee',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-eg','title' => 'eg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-eh','title' => 'eh',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-er','title' => 'er',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-es','title' => 'es',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-et','title' => 'et',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-fi','title' => 'fi',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-fj','title' => 'fj',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-fk','title' => 'fk',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-fm','title' => 'fm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-fo','title' => 'fo',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-fr','title' => 'fr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ga','title' => 'ga',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gb','title' => 'gb',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gd','title' => 'gd',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ge','title' => 'ge',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gf','title' => 'gf',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gg','title' => 'gg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gh','title' => 'gh',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gi','title' => 'gi',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gl','title' => 'gl',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gm','title' => 'gm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gn','title' => 'gn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gp','title' => 'gp',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gq','title' => 'gq',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gr','title' => 'gr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gs','title' => 'gs',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gt','title' => 'gt',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gu','title' => 'gu',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gw','title' => 'gw',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-gy','title' => 'gy',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-hk','title' => 'hk',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-hm','title' => 'hm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-hn','title' => 'hn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-hr','title' => 'hr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ht','title' => 'ht',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-hu','title' => 'hu',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-id','title' => 'id',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ie','title' => 'ie',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-il','title' => 'il',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-im','title' => 'im',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-in','title' => 'in',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-io','title' => 'io',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-iq','title' => 'iq',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ir','title' => 'ir',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-is','title' => 'is',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-it','title' => 'it',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-je','title' => 'je',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-jm','title' => 'jm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-jo','title' => 'jo',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-jp','title' => 'jp',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ke','title' => 'ke',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-kg','title' => 'kg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-kh','title' => 'kh',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ki','title' => 'ki',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-km','title' => 'km',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-kn','title' => 'kn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-kp','title' => 'kp',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-kr','title' => 'kr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-kw','title' => 'kw',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ky','title' => 'ky',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-kz','title' => 'kz',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-la','title' => 'la',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-lb','title' => 'lb',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-lc','title' => 'lc',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-li','title' => 'li',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-lk','title' => 'lk',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-lr','title' => 'lr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ls','title' => 'ls',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-lt','title' => 'lt',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-lu','title' => 'lu',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-lv','title' => 'lv',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ly','title' => 'ly',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ma','title' => 'ma',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mc','title' => 'mc',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-md','title' => 'md',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-me','title' => 'me',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mf','title' => 'mf',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mg','title' => 'mg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mh','title' => 'mh',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mk','title' => 'mk',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ml','title' => 'ml',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mm','title' => 'mm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mn','title' => 'mn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mo','title' => 'mo',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mp','title' => 'mp',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mq','title' => 'mq',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mr','title' => 'mr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ms','title' => 'ms',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mt','title' => 'mt',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mu','title' => 'mu',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mv','title' => 'mv',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mw','title' => 'mw',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mx','title' => 'mx',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-my','title' => 'my',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-mz','title' => 'mz',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-na','title' => 'na',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-nc','title' => 'nc',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ne','title' => 'ne',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-nf','title' => 'nf',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ng','title' => 'ng',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ni','title' => 'ni',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-nl','title' => 'nl',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-no','title' => 'no',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-np','title' => 'np',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-nr','title' => 'nr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-nu','title' => 'nu',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-nz','title' => 'nz',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-om','title' => 'om',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pa','title' => 'pa',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pe','title' => 'pe',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pf','title' => 'pf',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pg','title' => 'pg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ph','title' => 'ph',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pk','title' => 'pk',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pl','title' => 'pl',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pm','title' => 'pm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pn','title' => 'pn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pr','title' => 'pr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ps','title' => 'ps',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pt','title' => 'pt',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-pw','title' => 'pw',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-py','title' => 'py',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-qa','title' => 'qa',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-re','title' => 're',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ro','title' => 'ro',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-rs','title' => 'rs',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ru','title' => 'ru',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-rw','title' => 'rw',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sa','title' => 'sa',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sb','title' => 'sb',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sc','title' => 'sc',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sd','title' => 'sd',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-se','title' => 'se',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sg','title' => 'sg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sh','title' => 'sh',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-si','title' => 'si',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sj','title' => 'sj',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sk','title' => 'sk',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sl','title' => 'sl',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sm','title' => 'sm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sn','title' => 'sn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-so','title' => 'so',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sr','title' => 'sr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ss','title' => 'ss',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-st','title' => 'st',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sv','title' => 'sv',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sx','title' => 'sx',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sy','title' => 'sy',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-sz','title' => 'sz',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tc','title' => 'tc',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-td','title' => 'td',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tf','title' => 'tf',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tg','title' => 'tg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-th','title' => 'th',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tj','title' => 'tj',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tk','title' => 'tk',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tl','title' => 'tl',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tm','title' => 'tm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tn','title' => 'tn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-to','title' => 'to',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tr','title' => 'tr',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tt','title' => 'tt',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tv','title' => 'tv',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tw','title' => 'tw',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-tz','title' => 'tz',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ua','title' => 'ua',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ug','title' => 'ug',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-um','title' => 'um',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-us','title' => 'us',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-uy','title' => 'uy',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-uz','title' => 'uz',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-va','title' => 'va',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-vc','title' => 'vc',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ve','title' => 've',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-vg','title' => 'vg',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-vi','title' => 'vi',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-vn','title' => 'vn',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-vu','title' => 'vu',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-wf','title' => 'wf',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ws','title' => 'ws',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-ye','title' => 'ye',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-yt','title' => 'yt',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-za','title' => 'za',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-zm','title' => 'zm',]);
		FlagIcon::create(['icon_class'=> 'flag-icon flag-icon-zw','title' => 'zw',]);
    }
}
