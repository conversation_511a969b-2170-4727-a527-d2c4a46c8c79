<?php

namespace App\Models\Property;

use App\Models\Image;
use App\Models\Property\Property;
use Illuminate\Database\Eloquent\Model; 
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PropertyCategory extends Model
{
    use HasFactory;

    Protected $fillable= [
        'name',
        'slug',
        'icon',
        'image_id',
        'serial',
        'status',
        'featured',
        'parent_id',
    ];

    // hasMany relation with Property model
    public function properties()
    {
        return $this->hasMany(Property::class);
    }

    // belongsTo relation with PropertyCategory model
    public function parent()
    {
        return $this->belongsTo(PropertyCategory::class, 'parent_id');
    }

    // belongsTo relation with images model
    public function image()
    {
        return $this->belongsTo(Image::class, 'image_id');
    }

    // belongsTo relation with images model
    public function icon()
    {
        return $this->belongsTo(Image::class, 'icon');
    }
}
