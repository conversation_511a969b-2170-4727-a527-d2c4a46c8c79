<?php

namespace App\Models;

use App\Models\MasterOrder;
use App\Models\Property\Property;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DuePayment extends Model
{
    use HasFactory;

    public function property()
    {
        return $this->belongsTo(Property::class);
    }
    public function masterOrder()
    {
        return $this->belongsTo(MasterOrder::class);
    }
}
