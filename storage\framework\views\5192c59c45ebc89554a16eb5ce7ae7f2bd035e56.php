<?php $__env->startSection('content'); ?>
    <!-- checkout_wrapper_area::start   -->
    <div class="checkout_v3_area">
        <div class="checkout_v3_left d-flex justify-content-end">
            <div class="checkout_v3_inner">
                
                <div class="shiping_address_box checkout_form m-0">
                    <div class="billing_address">
                        <form action="<?php echo e(route('customer.placeOrder')); ?>" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <h3 class="check_v3_title mb_25"><?php echo e(_trans('landlord.Billing Address')); ?></h3>
                                <div class="accordion" id="accordionExample">
                                    <div class="accordion-item mb-3">
                                        <h2 class="accordion-header" id="address">

                                            <button class="accordion-button collapsed" type="button"
                                                data-bs-toggle="collapse" data-bs-target="#collapseAddress"
                                                aria-expanded="false" aria-controls="collapseAddress">
                                                <?php echo e(_trans('landlord.Add Address')); ?>

                                            </button>
                                        </h2>
                                        <div id="collapseAddress" class="accordion-collapse collapse"
                                            aria-labelledby="address" data-bs-parent="#accordionExample">
                                            <input type="hidden" name="new_address">
                                            <div class="accordion-body">
                                                <div class="col-lg-12 mb_20">
                                                    <label
                                                        class="primary_label2 style3"><?php echo e(_trans('landlord.Name')); ?></label>
                                                    <input class="primary_input3 style5 radius_3px " type="text"
                                                        placeholder="<?php echo e(_trans('landlord.Enter your first and last name')); ?>"
                                                        name="name" value="<?php echo e(old('name')); ?>">
                                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <small class="text-danger"><?php echo e($message); ?></small>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-lg-6 mb_20">
                                                    <label
                                                        class="primary_label2 style3"><?php echo e(_trans('landlord.Phone')); ?></label>
                                                    <input class="primary_input3 style5 radius_3px" type="number"
                                                        placeholder="<?php echo e(_trans('landlord.154327')); ?>" name="phone">
                                                    <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <small class="text-danger"><?php echo e($message); ?></small>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-lg-6 mb_20">
                                                    <label
                                                        class="primary_label2 style3"><?php echo e(_trans('landlord.Email')); ?></label>
                                                    <input class="primary_input3 style5 radius_3px" type="email"
                                                        placeholder="<?php echo e(_trans('landlord.154327')); ?>" name="email">
                                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <small class="text-danger"><?php echo e($message); ?></small>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-lg-12 mb_20">
                                                    <label
                                                        class="primary_label2 style3"><?php echo e(_trans('landlord.Address')); ?></label>
                                                    <input class="primary_input3 style5 radius_3px " type="text"
                                                        placeholder="<?php echo e(_trans('landlord.House# 122, Street# 245, ABC Road')); ?>"
                                                        name="address" value="<?php echo e(old('address')); ?>">
                                                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <small class="text-danger"><?php echo e($message); ?></small>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-lg-6 mb_20">
                                                    <label
                                                        class="primary_label2 style3"><?php echo e(_trans('landlord.Country')); ?></label>
                                                    <select class="form-select style5 primary_input3"
                                                        aria-label="Default select example" name="country">
                                                        <option class="primary_input3" value="">
                                                            <?php echo e(_trans('landlord.Select Country')); ?></option>
                                                        <?php $__currentLoopData = $data['country']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($country->id); ?>"><?php echo e($country->name); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <small class="text-danger"><?php echo e($message); ?></small>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-lg-6 mb_20">
                                                    <label
                                                        class="primary_label2 style3"><?php echo e(_trans('landlord.Postal Code')); ?></label>
                                                    <input class="primary_input3 style5 radius_3px" type="number"
                                                        placeholder="<?php echo e(_trans('landlord.154327')); ?>" name="postal">
                                                    <?php $__errorArgs = ['postal'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <small class="text-danger"><?php echo e($message); ?></small>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>

                                                <div class="col-12 mb_25">
                                                    <label class="primary_checkbox d-flex">
                                                        <input type="checkbox" name="terms_and_condition">
                                                        <span class="checkmark mr_15"></span>
                                                        <span
                                                            class="label_name f_w_400 "><?php echo e(_trans('landlord.I agree with the terms and conditions.')); ?></span>

                                                    </label>
                                                    <?php $__errorArgs = ['terms_and_condition'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <small class="text-danger"><?php echo e($message); ?></small>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <?php if($data['address']->count() > 0): ?>
                                    <?php $__currentLoopData = $data['address']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $address): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="aiz-megabox d-block bg-white">
                                            <span class="d-flex p-3 gap-2 address-box">
                                                <input type="radio" name="billing_address_id" value="<?php echo e($address->id); ?>">
                                                <span class=" flex-shrink-0 mt-1"></span>
                                                <span class="flex-grow-1 pl-3 text-left line-break">
                                                    <div> <span class="w-50 fw-600">Name:</span>
                                                        <span class="ml-2"><?php echo e($address->name); ?></span>
                                                    </div>
                                                    <div> <span class="w-50 fw-600">Phone:</span>
                                                        <span class="ml-2"><?php echo e($address->phone); ?></span>
                                                    </div>
                                                    <div> <span class="w-50 fw-600">Address:</span>
                                                        <span class="ml-2"><?php echo e($address->address); ?></span>
                                                    </div>
                                                </span>
                                            </span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <p>No address found for this user.</p>
                                <?php endif; ?>

                                
                            </div>

                            <div class="row">
                                <div class="col-12 mb_10">
                                    <h3 class="check_v3_title2">Payment</h3>
                                    <h6 class="shekout_subTitle_text">All transactions are secure and encrypted.</h6>
                                </div>
                                <div class="col-12">
                                    <div class="accordion checkout_acc_style mb_30" id="accordionExample">
                                        <div class="accordion-item">
                                            
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingTwo">
                                                <span class="accordion-button shadow-none collapsed">
                                                    <span>
                                                        <label class="primary_checkbox d-inline-flex style5 gap_10">
                                                            <input name="payment_method" value="cash_on_delivery"
                                                                type="radio" checked>
                                                            <span class="checkmark m-0"></span>
                                                            <span class="label_name f_w_500 ">
                                                                Cash on Delivery (COD)
                                                            </span>
                                                        </label>
                                                    </span>
                                                </span>
                                            </h2>
                                        </div>
                                        
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="check_v3_btns flex-wrap d-flex align-items-center">
                                        <button type="submit"
                                            class="o_land_primary_btn style2  min_200 text-center text-uppercase ">Place
                                            Order</button>
                                        <a href="#" class="return_text">Return to shipping</a>
                                    </div>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
        <div class="checkout_v3_right d-flex justify-content-start">
            <div class="order_sumery_box flex-fill">
                <h3 class="check_v3_title mb_25"><?php echo e(_trans('landlord.Order Summary')); ?></h3>
                <div class="subtotal_lists">
                    <?php
                        $total = 0;
                    ?>
                    <?php $__empty_1 = true; $__currentLoopData = $data['carts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cart): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="single_total_list d-flex align-items-center">
                            <div class="single_total_left flex-fill">
                                <h4><?php echo e(_trans('landlord.Subtotal')); ?></h4>
                            </div>
                            <div class="single_total_right">
                                <span> ৳ <?php echo e(@$cart->durations * @$cart->property->rent_amount); ?></span>
                            </div>
                        </div>
                        <div class="single_total_list d-flex align-items-center flex-wrap">
                            <div class="single_total_left flex-fill">
                                <h4><?php echo e(_trans('landlord.Shipping Charge')); ?></h4>
                                <p>Package Wise Shipping Charge</p>
                            </div>
                            <div class="single_total_right">
                                <span> ৳ <?php echo e(@$cart->property->location->upazila->charge); ?></span>
                            </div>
                        </div>
                        <div class="single_total_list d-flex align-items-center flex-wrap">
                            <div class="single_total_left flex-fill">
                                <h4><?php echo e(_trans('landlord.Discount')); ?></h4>
                            </div>
                            <div class="single_total_right">
                                <span>৳ <?php echo e(@$cart->property->discount_amount); ?></span>
                            </div>
                        </div>
                        <div class="total_amount d-flex align-items-center flex-wrap mb_25">
                            <div class="single_total_left flex-fill">
                                <span class="total_text"><?php echo e(_trans('landlord.Sub Total')); ?> (Incl. VAT)</span>
                            </div>
                            <div class="single_total_right">
                                <span class="total_text"> <span>৳ <?php echo e((@$cart->durations * @$cart->property->rent_amount) + @$cart->property->location->upazila->charge + @$cart->property->discount_amount); ?></span></span>
                            </div>
                        </div>

                        <?php
                            $total += (@$cart->durations * @$cart->property->rent_amount) + @$cart->property->location->upazila->charge + @$cart->property->discount_amount
                        ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p><?php echo e(_trans('landlord.No property found')); ?></p>
                    <?php endif; ?>
                    <div class="total_amount d-flex align-items-center flex-wrap mb_25">
                        <div class="single_total_left flex-fill">
                            <span class="total_text"><?php echo e(_trans('landlord.Total')); ?> (Incl. VAT)</span>
                        </div>
                        <div class="single_total_right">
                            <span class="total_text"> <span>৳ <?php echo e($total); ?></span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
    <!-- checkout_wrapper_area::end   -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>
        $(document).ready(function() {
            // Listen to changes in the select element
            $('.o_land_select6').on('change', function() {
                // Get the selected month value and cart ID
                var selectedMonth = $(this).val();
                console.log(selectedMonth);
                var cartId = $(this).data('cart-id');
                console.log(cartId);
                $.ajax({
                    url: "<?php echo e(route('customer.checkOutProcess')); ?>",
                    method: 'POST',
                    data: {
                        month: selectedMonth,
                        cart_id: cartId,
                        _token: "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(response) {
                        console.log(response);
                        toastr.success(response.message, 'Success');
                        // Update the HTML on the page with the updated data
                        $('#updated-amount').html(response.data.totalAmount);
                    }
                });

                // Calculate the total amount for the selected cart
                var rentAmount = $('#rent-amount-' + cartId).text().replace(/[^\d]/g,
                    ''); // remove non-numeric characters from rent amount
                var totalAmount = rentAmount * selectedMonth;
                $('#total-amount-' + cartId).text('৳' + totalAmount);

                // Calculate the grand total amount for all carts
                var grandTotal = 0;
                $('.total-amount').each(function() {
                    var totalAmount = $(this).text().replace(/[^\d]/g,
                        ''); // remove non-numeric characters from total amount
                    grandTotal += parseInt(totalAmount);
                });
                $('#grand-total').text('৳' + grandTotal);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\landlord\resources\views/frontend/customer/checkout.blade.php ENDPATH**/ ?>