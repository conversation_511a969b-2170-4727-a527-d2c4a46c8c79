<?php

namespace Database\Seeders;

use Carbon\Carbon;
use App\Enums\Status;
use App\Enums\DealType;
use App\Enums\ApprovalStatus;
use App\Models\Advertisement;
use Illuminate\Database\Seeder;
use App\Models\Property\Property;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class AdvertisementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $properties = Property::get();
        // for ($i = 1; $i <= 80; $i++) {
       foreach($properties as $property) {
            $advertisement = new Advertisement();

            // set the user_id to a random user ID (assuming you have a `users` table)
            $advertisement->user_id = 2;

            // set the property_id to a random property ID (assuming you have a `properties` table)
            // $advertisement->property_id = rand(1,30);
            $advertisement->property_id = $property->id;

            // set the advertisement_type to either rent or sell
            // $advertisement->advertisement_type = rand(DealType::RENT, DealType::SELL);
            $advertisement->advertisement_type = $property->deal_type;

            // set the booking_amount to a random value between 1000 and 10000
            $advertisement->booking_amount = rand(1000, 10000);

            // if the advertisement is for rent
            if ($advertisement->advertisement_type == DealType::RENT) {
                $advertisement->rent_amount = rand(5000, 10000);
                $advertisement->rent_type = rand(1, 2);
                $advertisement->rent_start_date = Carbon::now()->addDays(rand(1, 30));
                $advertisement->rent_end_date = $advertisement->rent_start_date->copy()->addMonths(rand(1, 12));
            }

            // if the advertisement is for sell
            if ($advertisement->advertisement_type == DealType::SELL) {
                $advertisement->sell_amount = rand(100000, 500000);
                $advertisement->sell_start_date = Carbon::now()->addDays(rand(1, 30));
            }

            // set the negotiable flag to either true or false
            $advertisement->negotiable = rand(0, 1);

            // set the status to active
            $advertisement->status = Status::ACTIVE;

            // set the approval status to either pending or approved
            $advertisement->approval_status = rand(ApprovalStatus::PENDING, ApprovalStatus::APPROVED);
            if ($advertisement->approval_status == ApprovalStatus::APPROVED) {
                $advertisement->approved_by = 1;
                $advertisement->approved_at = Carbon::now();
            }

            $advertisement->save();
        }
    }
}
