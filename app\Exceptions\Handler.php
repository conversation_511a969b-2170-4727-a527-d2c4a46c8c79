<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use App\Exceptions\InvalidOrderException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Illuminate\Auth\AuthenticationException;


class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function render($request, Throwable $exception)
    {

        if ($this->isHttpException($exception)) {

            if ($exception->getStatusCode() == 404) {

                return response()->view('errors.404');

            } elseif ($exception->getStatusCode() == 403) {

                return response()->view('errors.403');

            } elseif ($exception->getStatusCode() == 405) {

                 return response()->view('errors.405');
            }
            elseif ($exception->getStatusCode() == 500) {

                 return response()->view('errors.500');

            }
        }

        return parent::render($request, $exception);
    }


}
