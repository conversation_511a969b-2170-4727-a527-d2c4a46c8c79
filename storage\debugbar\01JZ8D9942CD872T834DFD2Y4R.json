{"__meta": {"id": "01JZ8D9942CD872T834DFD2Y4R", "datetime": "2025-07-03 15:08:06", "utime": **********.147044, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751555282.688929, "end": **********.147056, "duration": 3.458127021789551, "duration_str": "3.46s", "measures": [{"label": "Booting", "start": 1751555282.688929, "relative_start": 0, "end": **********.461085, "relative_end": **********.461085, "duration": 1.***************, "duration_str": "1.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.461094, "relative_start": 1.****************, "end": **********.147058, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.69s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.470725, "relative_start": 1.****************, "end": **********.474422, "relative_end": **********.474422, "duration": 0.003696918487548828, "duration_str": "3.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend.home", "start": **********.967505, "relative_start": 3.***************, "end": **********.967505, "relative_end": **********.967505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.986269, "relative_start": 3.***************, "end": **********.986269, "relative_end": **********.986269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.998313, "relative_start": 3.****************, "end": **********.998313, "relative_end": **********.998313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.003072, "relative_start": 3.314142942428589, "end": **********.003072, "relative_end": **********.003072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.007991, "relative_start": 3.3190619945526123, "end": **********.007991, "relative_end": **********.007991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.014259, "relative_start": 3.3253300189971924, "end": **********.014259, "relative_end": **********.014259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.019829, "relative_start": 3.330899953842163, "end": **********.019829, "relative_end": **********.019829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.026072, "relative_start": 3.3371429443359375, "end": **********.026072, "relative_end": **********.026072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.031585, "relative_start": 3.342655897140503, "end": **********.031585, "relative_end": **********.031585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.03724, "relative_start": 3.348310947418213, "end": **********.03724, "relative_end": **********.03724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.043629, "relative_start": 3.3546998500823975, "end": **********.043629, "relative_end": **********.043629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.049249, "relative_start": 3.3603198528289795, "end": **********.049249, "relative_end": **********.049249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.054818, "relative_start": 3.365888833999634, "end": **********.054818, "relative_end": **********.054818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.057728, "relative_start": 3.3687989711761475, "end": **********.057728, "relative_end": **********.057728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.061342, "relative_start": 3.372412919998169, "end": **********.061342, "relative_end": **********.061342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.property.partials.call-email", "start": **********.064291, "relative_start": 3.375361919403076, "end": **********.064291, "relative_end": **********.064291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.layouts.master", "start": **********.087471, "relative_start": 3.3985419273376465, "end": **********.087471, "relative_end": **********.087471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.include.header", "start": **********.089793, "relative_start": 3.4008638858795166, "end": **********.089793, "relative_end": **********.089793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.logged-user-profile", "start": **********.101196, "relative_start": 3.412266969680786, "end": **********.101196, "relative_end": **********.101196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.property-category", "start": **********.105333, "relative_start": 3.4164040088653564, "end": **********.105333, "relative_end": **********.105333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.include.footer", "start": **********.119869, "relative_start": 3.4309399127960205, "end": **********.119869, "relative_end": **********.119869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.script.landloardScript", "start": **********.136123, "relative_start": 3.4471938610076904, "end": **********.136123, "relative_end": **********.136123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 25542168, "peak_usage_str": "24MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Exception", "message": "Cannot add SymfonyMailCollector on Laravel Debugbar: Mailer [] is not defined.", "code": 0, "file": "vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php", "line": 740, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-2093196688 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>534</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">addCollectorException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">Cannot add SymfonyMailCollector</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object InvalidArgumentException]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">boot</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">C:\\xampp\\htdocs\\landlord\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2093196688\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["    protected function addCollectorException(string $message, Exception $exception)\n", "    {\n", "        $this->addThrowable(\n", "            new Exception(\n", "                $message . ' on Laravel Debugbar: ' . $exception->getMessage(),\n", "                $exception->getCode(),\n", "                $exception\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=740", "ajax": false, "filename": "LaravelDebugbar.php", "line": "740"}}, {"type": "InvalidArgumentException", "message": "Mailer [] is not defined.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Mail/MailManager.php", "line": 113, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-434749543 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>97</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"68 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>33</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>885</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Mail\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Mail\\MailServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>770</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">build</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>881</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>706</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>866</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1431</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>504</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">offsetGet</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">boot</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">C:\\xampp\\htdocs\\landlord\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434749543\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["        $config = $this->getConfig($name);\n", "\n", "        if (is_null($config)) {\n", "            throw new InvalidArgumentException(\"Mailer [{$name}] is not defined.\");\n", "        }\n", "\n", "        // Once we have created the mailer instance we will set a container instance\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FMail%2FMailManager.php&line=113", "ajax": false, "filename": "MailManager.php", "line": "113"}}]}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "landlord.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "frontend.home", "param_count": null, "params": [], "start": **********.967451, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/home.blade.phpfrontend.home", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.986217, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.99826, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.003017, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.007938, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.014207, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.019777, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.026021, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.031534, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.037189, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.043577, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.049198, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.054719, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.057676, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.061285, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.property.partials.call-email", "param_count": null, "params": [], "start": **********.064238, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.phpfrontend.property.partials.call-email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fproperty%2Fpartials%2Fcall-email.blade.php&line=1", "ajax": false, "filename": "call-email.blade.php", "line": "?"}}, {"name": "frontend.layouts.master", "param_count": null, "params": [], "start": **********.087413, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/layouts/master.blade.phpfrontend.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "frontend.include.header", "param_count": null, "params": [], "start": **********.089736, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/include/header.blade.phpfrontend.include.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Finclude%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "components.logged-user-profile", "param_count": null, "params": [], "start": **********.101144, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/components/logged-user-profile.blade.phpcomponents.logged-user-profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fcomponents%2Flogged-user-profile.blade.php&line=1", "ajax": false, "filename": "logged-user-profile.blade.php", "line": "?"}}, {"name": "components.property-category", "param_count": null, "params": [], "start": **********.105282, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/components/property-category.blade.phpcomponents.property-category", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fcomponents%2Fproperty-category.blade.php&line=1", "ajax": false, "filename": "property-category.blade.php", "line": "?"}}, {"name": "frontend.include.footer", "param_count": null, "params": [], "start": **********.119817, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/include/footer.blade.phpfrontend.include.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Finclude%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "frontend.script.landloardScript", "param_count": null, "params": [], "start": **********.136065, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/frontend/script/landloardScript.blade.phpfrontend.script.landloardScript", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Ffrontend%2Fscript%2FlandloardScript.blade.php&line=1", "ajax": false, "filename": "landloardScript.blade.php", "line": "?"}}]}, "queries": {"count": 152, "nb_statements": 152, "nb_visible_statements": 152, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.1985900000000003, "accumulated_duration_str": "1.2s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 52 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select `id`, `title`, `highlight_title_one`, `btn_one`, `image_id`, `status` from `hero_sections` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.636636, "duration": 0.****************, "duration_str": "535ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:61", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=61", "ajax": false, "filename": "HomeController.php", "line": "61"}, "connection": "landlord", "explain": null, "start_percent": 0, "width_percent": 44.6}, {"sql": "select `id`, `path` from `images` where `images`.`id` in (224, 225, 226)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.175929, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:61", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=61", "ajax": false, "filename": "HomeController.php", "line": "61"}, "connection": "landlord", "explain": null, "start_percent": 44.6, "width_percent": 0.053}, {"sql": "select * from `properties` where `status` = 1 order by RAND() limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 63}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.178934, "duration": 0.10986, "duration_str": "110ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:63", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=63", "ajax": false, "filename": "HomeController.php", "line": "63"}, "connection": "landlord", "explain": null, "start_percent": 44.652, "width_percent": 9.166}, {"sql": "select * from `images` where `images`.`id` = 173 limit 1", "type": "query", "params": [], "bindings": [173], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.291138, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 53.818, "width_percent": 0.063}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.294863, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 53.881, "width_percent": 0.06}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 24 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.300107, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 53.941, "width_percent": 0.051}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.303119, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 53.992, "width_percent": 0.06}, {"sql": "select * from `images` where `images`.`id` = 53 limit 1", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.305835, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 54.052, "width_percent": 0.04}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3086278, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 54.092, "width_percent": 0.047}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 4 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.310875, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 54.139, "width_percent": 0.043}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.313019, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 54.182, "width_percent": 0.042}, {"sql": "select * from `images` where `images`.`id` = 59 limit 1", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.315212, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 54.224, "width_percent": 0.036}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3173301, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 54.26, "width_percent": 0.037}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 5 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.319388, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 54.296, "width_percent": 0.051}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.321589, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 54.347, "width_percent": 0.045}, {"sql": "select * from `images` where `images`.`id` = 89 limit 1", "type": "query", "params": [], "bindings": [89], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.32449, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 54.392, "width_percent": 0.053}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.327846, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 54.445, "width_percent": 0.059}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 10 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.330544, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 54.504, "width_percent": 0.046}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.332948, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 54.55, "width_percent": 0.043}, {"sql": "select * from `images` where `images`.`id` = 149 limit 1", "type": "query", "params": [], "bindings": [149], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3351812, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 54.592, "width_percent": 0.037}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.337709, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 54.629, "width_percent": 0.04}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 20 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.339892, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 54.669, "width_percent": 0.043}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 64}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.342405, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 54.712, "width_percent": 0.06}, {"sql": "select * from `properties` where `status` = 1 order by RAND() limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 66}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3457022, "duration": 0.*****************, "duration_str": "155ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:66", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=66", "ajax": false, "filename": "HomeController.php", "line": "66"}, "connection": "landlord", "explain": null, "start_percent": 54.772, "width_percent": 12.957}, {"sql": "select * from `images` where `images`.`id` = 173 limit 1", "type": "query", "params": [], "bindings": [173], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5032198, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 67.729, "width_percent": 0.048}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.505556, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 67.776, "width_percent": 0.042}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 24 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.507819, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 67.818, "width_percent": 0.058}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.510534, "duration": 0.*****************, "duration_str": "331ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 67.876, "width_percent": 27.65}, {"sql": "select * from `images` where `images`.`id` = 137 limit 1", "type": "query", "params": [], "bindings": [137], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.844498, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 95.526, "width_percent": 0.06}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.847501, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 95.586, "width_percent": 0.048}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 18 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.849907, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 95.634, "width_percent": 0.045}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.852021, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 95.679, "width_percent": 0.043}, {"sql": "select * from `images` where `images`.`id` = 77 limit 1", "type": "query", "params": [], "bindings": [77], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.854191, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 95.722, "width_percent": 0.04}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.856319, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 95.762, "width_percent": 0.041}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 8 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.859467, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 95.803, "width_percent": 0.087}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8625832, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 95.889, "width_percent": 0.054}, {"sql": "select * from `images` where `images`.`id` = 53 limit 1", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.864938, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 95.944, "width_percent": 0.04}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8670862, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 95.984, "width_percent": 0.04}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 4 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.869164, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 96.024, "width_percent": 0.044}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.87128, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 96.068, "width_percent": 0.05}, {"sql": "select * from `images` where `images`.`id` = 191 limit 1", "type": "query", "params": [], "bindings": [191], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8738492, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 96.118, "width_percent": 0.039}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8770158, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 96.157, "width_percent": 0.063}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 27 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.879781, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 96.221, "width_percent": 0.046}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 67}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.881962, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 96.266, "width_percent": 0.047}, {"sql": "select * from `properties` where `status` = 1 order by RAND() limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 69}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8841832, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:69", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=69", "ajax": false, "filename": "HomeController.php", "line": "69"}, "connection": "landlord", "explain": null, "start_percent": 96.313, "width_percent": 0.334}, {"sql": "select * from `images` where `images`.`id` = 197 limit 1", "type": "query", "params": [], "bindings": [197], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.890454, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 96.647, "width_percent": 0.038}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.892908, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 96.685, "width_percent": 0.041}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 28 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8950438, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 96.726, "width_percent": 0.046}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8971791, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 96.772, "width_percent": 0.04}, {"sql": "select * from `images` where `images`.`id` = 185 limit 1", "type": "query", "params": [], "bindings": [185], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.899308, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 96.812, "width_percent": 0.039}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.901436, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 96.851, "width_percent": 0.042}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 26 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.903594, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 96.893, "width_percent": 0.044}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.905701, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 96.937, "width_percent": 0.045}, {"sql": "select * from `images` where `images`.`id` = 41 limit 1", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.908216, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 96.982, "width_percent": 0.046}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9104369, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 97.028, "width_percent": 0.033}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 2 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.912419, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 97.062, "width_percent": 0.038}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.914441, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 97.1, "width_percent": 0.04}, {"sql": "select * from `images` where `images`.`id` = 113 limit 1", "type": "query", "params": [], "bindings": [113], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9165678, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 97.14, "width_percent": 0.034}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.918669, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 97.174, "width_percent": 0.032}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 14 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9206321, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 97.206, "width_percent": 0.037}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.922658, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 97.243, "width_percent": 0.039}, {"sql": "select * from `images` where `images`.`id` = 125 limit 1", "type": "query", "params": [], "bindings": [125], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9259298, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:38", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=38", "ajax": false, "filename": "HomeController.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 97.282, "width_percent": 0.051}, {"sql": "select * from `property_categories` where `property_categories`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.928953, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=45", "ajax": false, "filename": "HomeController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 97.333, "width_percent": 0.048}, {"sql": "select * from `property_locations` where `property_locations`.`property_id` = 16 and `property_locations`.`property_id` is not null limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9311512, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "landlord", "explain": null, "start_percent": 97.381, "width_percent": 0.039}, {"sql": "select * from `countries` where `countries`.`id` = 233 limit 1", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 28}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 70}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.933234, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:48", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=48", "ajax": false, "filename": "HomeController.php", "line": "48"}, "connection": "landlord", "explain": null, "start_percent": 97.42, "width_percent": 0.041}, {"sql": "select * from `partners` where `status` = 1 limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.935759, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:72", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=72", "ajax": false, "filename": "HomeController.php", "line": "72"}, "connection": "landlord", "explain": null, "start_percent": 97.461, "width_percent": 0.103}, {"sql": "select * from `how_it_works` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.939483, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:75", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=75", "ajax": false, "filename": "HomeController.php", "line": "75"}, "connection": "landlord", "explain": null, "start_percent": 97.565, "width_percent": 0.085}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.944229, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 97.65, "width_percent": 0.049}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.947045, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 97.699, "width_percent": 0.031}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.949448, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 97.73, "width_percent": 0.028}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.951469, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 97.758, "width_percent": 0.029}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.953655, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 97.787, "width_percent": 0.028}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.955693, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 97.815, "width_percent": 0.028}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.9620788, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 97.843, "width_percent": 0.035}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.965161, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 97.878, "width_percent": 0.03}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.981502, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 97.908, "width_percent": 0.037}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.984092, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 97.945, "width_percent": 0.031}, {"sql": "select * from `users` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": "view", "name": "frontend.property.partials.call-email", "file": "C:\\xampp\\htdocs\\landlord\\resources\\views/frontend/property/partials/call-email.blade.php", "line": 1}], "start": **********.9889429, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "landlord", "explain": null, "start_percent": 97.976, "width_percent": 0.035}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.992419, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.011, "width_percent": 0.057}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.9960082, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.068, "width_percent": 0.038}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.9986749, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.105, "width_percent": 0.021}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.0008948, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.126, "width_percent": 0.022}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.003347, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.148, "width_percent": 0.02}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.005605, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.168, "width_percent": 0.022}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.008433, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.19, "width_percent": 0.03}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.0118961, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.22, "width_percent": 0.04}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.0153852, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.26, "width_percent": 0.027}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.01771, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.286, "width_percent": 0.028}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.020806, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.314, "width_percent": 0.027}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.023176, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.341, "width_percent": 0.028}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.027121, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.368, "width_percent": 0.03}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.02949, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.398, "width_percent": 0.026}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.032625, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.424, "width_percent": 0.025}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.034904, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.449, "width_percent": 0.025}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.0383968, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.474, "width_percent": 0.033}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.041016, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.507, "width_percent": 0.043}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.04475, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.551, "width_percent": 0.029}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.04709, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.58, "width_percent": 0.028}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.0503252, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 98.608, "width_percent": 0.025}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.052597, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.633, "width_percent": 0.028}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0563111, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.66, "width_percent": 0.028}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.057131, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.688, "width_percent": 0.033}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.058976, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.72, "width_percent": 0.045}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.060478, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.765, "width_percent": 0.05}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.062825, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.815, "width_percent": 0.032}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.063746, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.847, "width_percent": 0.028}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0808108, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.875, "width_percent": 0.036}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.081509, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.91, "width_percent": 0.025}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.082021, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.935, "width_percent": 0.025}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.08253, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.96, "width_percent": 0.023}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.083028, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 98.984, "width_percent": 0.023}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.083561, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.007, "width_percent": 0.028}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.084106, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.036, "width_percent": 0.026}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.084636, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.061, "width_percent": 0.026}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.085248, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.087, "width_percent": 0.02}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.085716, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.107, "width_percent": 0.019}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0862408, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.126, "width_percent": 0.022}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0870209, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.148, "width_percent": 0.021}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.088093, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.169, "width_percent": 0.023}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0886302, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.192, "width_percent": 0.023}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0893302, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.215, "width_percent": 0.023}, {"sql": "select * from `wishlists` where `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.09261, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.237, "width_percent": 0.032}, {"sql": "select * from `carts` where `tenant_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.094111, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.269, "width_percent": 0.025}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.099858, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.294, "width_percent": 0.032}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1006851, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.326, "width_percent": 0.025}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.101634, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.351, "width_percent": 0.028}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1039531, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.379, "width_percent": 0.028}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.104775, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.408, "width_percent": 0.028}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.106017, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.436, "width_percent": 0.028}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.106535, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.464, "width_percent": 0.028}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.107049, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.491, "width_percent": 0.028}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1076832, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.519, "width_percent": 0.039}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1084862, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.559, "width_percent": 0.024}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.108953, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.583, "width_percent": 0.025}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.109517, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.608, "width_percent": 0.023}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.109986, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.631, "width_percent": 0.025}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.110465, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.656, "width_percent": 0.023}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.111021, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.679, "width_percent": 0.023}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.111473, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.702, "width_percent": 0.023}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1119199, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.726, "width_percent": 0.021}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.112441, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.746, "width_percent": 0.015}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.112808, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.761, "width_percent": 0.02}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1132169, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.781, "width_percent": 0.015}, {"sql": "select * from `images` where `images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.113657, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.796, "width_percent": 0.015}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.113993, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.811, "width_percent": 0.015}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.114325, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.826, "width_percent": 0.016}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.118499, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.842, "width_percent": 0.028}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.119281, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.87, "width_percent": 0.029}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1335828, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.899, "width_percent": 0.035}, {"sql": "select * from `settings` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.134211, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.934, "width_percent": 0.023}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1349509, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.957, "width_percent": 0.022}, {"sql": "select * from `languages` where `code` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.135666, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "landlord", "explain": null, "start_percent": 99.978, "width_percent": 0.022}]}, "models": {"data": {"App\\Models\\Language": {"value": 66, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Image": {"value": 35, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FImage.php&line=1", "ajax": false, "filename": "Image.php", "line": "?"}}, "App\\Models\\Setting": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Property\\Property": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FProperty%2FProperty.php&line=1", "ajax": false, "filename": "Property.php", "line": "?"}}, "App\\Models\\Property\\PropertyCategory": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FProperty%2FPropertyCategory.php&line=1", "ajax": false, "filename": "PropertyCategory.php", "line": "?"}}, "App\\Models\\Property\\PropertyLocation": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FProperty%2FPropertyLocation.php&line=1", "ajax": false, "filename": "PropertyLocation.php", "line": "?"}}, "App\\Models\\Locations\\Country": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FLocations%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\Partner": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FPartner.php&line=1", "ajax": false, "filename": "Partner.php", "line": "?"}}, "App\\Models\\HowItWork": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FHowItWork.php&line=1", "ajax": false, "filename": "HowItWork.php", "line": "?"}}, "App\\Models\\HeroSection": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FHeroSection.php&line=1", "ajax": false, "filename": "HeroSection.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 200, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=58\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=58\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:58-92</a>", "middleware": "web", "duration": "3.48s", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-217251467 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-217251467\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-663766347 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-663766347\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-503566921 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/customer/checkout</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IlF0L3dSNVVLS2hScndVK2ZISE5QaHc9PSIsInZhbHVlIjoiSW1tSWw2dkRwRTZSQk9kcWZaNFFmSHlBeEdKS2xtckpqMXNJS2hkVmg2NzZva20zc2dEMCtJSWRKc2U2NEI5MXp2Y0JSR2NXY0xqNmp6R2t2alNJV045QjFyZ3BpOG1QTlpIWWJrSlo4TTJUZzdBK0sveEZUcVdLUlFCU3B0dTEiLCJtYWMiOiI3MmM1NDkwMTFiNTBlZmE5MWNjMjdkOWExYmU5NWVkMzVhYWFlOGY1ZGZmZmFiYzdlMjg0MWUyM2NkOWM2MzU5IiwidGFnIjoiIn0%3D; landlord_session=eyJpdiI6IkNTdEpmd3d1dUFHUHBXbVlHRzFWRUE9PSIsInZhbHVlIjoiTDc5VzhUeG4zQWY2cmVRU1RCWlZXY1phMkJUQ3BqeVN1Nlg0cm5jL2toYy8zVjQ0Y3dEZVFacGE0bXg1Wm9zdldCdy8xd0l4M2RuT3pwQnV3bjhTWVFlU0M2QVJFeHlhdWY2d0J6eFJRQkVBT3JORE9YUVczMW9BQTJheWxnK2giLCJtYWMiOiJhOGFmNmZkMjUwNDFhOWQxMjFmM2EwODBlOTA4YTJmOGUxMzg0M2ZiOWUyNjRhOTY3NWUwNWI5ZjAwYzE0NDIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-503566921\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-59290322 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zd8n8oVrnqq7VrOib2a0aR6Mg0x9iKyNiIzK3AUb</span>\"\n  \"<span class=sf-dump-key>landlord_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wjN97PJEcLLEIbZmnCzEPYaoZw3NxJJzOOvYVDV4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59290322\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1900796074 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 15:08:05 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900796074\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1850621518 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zd8n8oVrnqq7VrOib2a0aR6Mg0x9iKyNiIzK3AUb</span>\"\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/customer/checkout</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JZ8D95H5DQYYRY6CQJXTWKA6</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850621518\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index"}, "badge": null}}