<?php

namespace Database\Seeders\Property;

use App\Enums\Status;
use App\Models\Image;
use App\Models\Rental;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use App\Models\Property\Property;
use App\Models\Property\Transaction;
use App\Models\Property\PropertyTenant;

class TransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

       $tenants = PropertyTenant::all();

         foreach ($tenants as $tenant) {

              $properties = Property::where('id',$tenant->property_id)->get();
              foreach ($properties as $property) {
                  $types =['rent', 'deposit', 'other', 'income', 'expense'];

                  Transaction::create([
                    'property_id' => $property->id,
                    'property_tenant_id' => $tenant->id,
                    'rental_id' => @$property->rental->id??1,
                    'amount' => 1000,
                    'type' => $types[array_rand($types,1)],
                    'date' => date('Y-m-d'),
                    'note' => 'This is a note',
                    'payment_method' => 'cash',
                    'cheque_number' => '123456',
                    'bank_name' => 'Bank Name',
                    'bank_branch' => 'Bank Branch',
                    'bank_account_number' => '*********',
                    'bank_account_name' => 'Bank Account Name',
                    'invoice_number' =>  Str::random(10),
                    'invoice_date' =>  date('Y-m-d'),
                    'attachment_id' => Image::inRandomOrder()->first()->id,
                    'created_by' => 1, // Replace with your user ID
                    'updated_by' => 1, // Replace with your user ID
                    'status' => Status::ACTIVE,
                    'payment_status' => 'unpaid',
                ]);

              }
         }
    }
}
