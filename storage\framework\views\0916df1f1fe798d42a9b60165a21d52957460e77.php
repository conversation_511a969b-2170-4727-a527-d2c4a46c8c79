<?php $__env->startSection('content'); ?>
    <!-- checkout_v3_area::start  -->
    <div class="pt-5 pb-5 container">

        <?php if($data['carts']->count() > 0): ?>

                    <div class="table-responsive mb-0">
                        <table class="table o_landy_table3 style4 mb-0 table-scrollable">
                            <thead>
                                <tr>
                                    <th class="font_14 f_w_700 m-0 text-nowrap priamry_text">
                                        <?php echo e(_trans('landlord.Products')); ?>

                                    </th>
                                    <th class="font_14 f_w_700 m-0 text-nowrap priamry_text">
                                        <?php echo e(_trans('landlord.Price')); ?>

                                    </th>
                                    <th class="font_14 f_w_700 m-0 text-nowrap priamry_text">
                                        <?php echo e(_trans('landlord.Duration')); ?>

                                    </th>
                                    <th class="font_14 f_w_700 m-0 text-nowrap priamry_text">
                                        <?php echo e(_trans('landlord.Total')); ?>

                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $data['carts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cart): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    
                                    <tr>
                                        <td>
                                            <a href="<?php echo e(url('property/' . $cart->property->id . '/details' . '/' . $cart->property->slug)); ?>"
                                                class="d-flex align-items-center gap_20">
                                                <div class="thumb cart_img overflow-hidden">
                                                    <img class="img-fluid"
                                                        src="<?php echo e(url($cart->property->defaultImage->path)); ?>" alt="">
                                                </div>
                                                <div class="summery_pro_content">
                                                    <h4 class="font_16 f_w_700 text-nowrap m-0 theme_hover">
                                                        <?php echo e(@$cart->property->name); ?></h4>
                                                    <p class="font_14 f_w_400 m-0 "><?php echo e(@$cart->property->size); ?>

                                                        <?php echo e(_trans('landlord.sqft')); ?></p>
                                                </div>
                                            </a>
                                        </td>

                                        <td>
                                            <h4 class="font_16 f_w_500 m-0 text-nowrap rent-amount"
                                                id="rent-amount-<?php echo e($cart->id); ?>">
                                                ৳<?php echo e(@$cart->property->rent_amount); ?></h4>
                                        </td>
                                        <td>
                                            <div class="lag_select">
                                                <select class="o_land_select6 min_100 wide"
                                                    data-cart-id="<?php echo e($cart->id); ?>">
                                                    <option <?php echo e($cart->durations == 1 ? 'selected' : ''); ?> value="1">1
                                                        month</option>
                                                    <option <?php echo e($cart->durations == 3 ? 'selected' : ''); ?> value="3">3
                                                        months</option>
                                                    <option <?php echo e($cart->durations == 6 ? 'selected' : ''); ?> value="6">6
                                                        months</option>
                                                    <option <?php echo e($cart->durations == 12 ? 'selected' : ''); ?> value="12">12
                                                        months</option>
                                                </select>

                                            </div>
                                        </td>
                                        <td>
                                            <div class="m-0 d-flex gap_10 align-items-center">
                                                <h4 class="font_16 f_w_500 m-0 text-nowrap total-amount"
                                                    id="total-amount-<?php echo e($cart->id); ?>">
                                                    ৳<?php echo e($cart->durations * $cart->property->rent_amount); ?></h4>
                                                <a class="pe-auto cart-delete" data-id="<?php echo e(@$cart->id); ?>">

                                                   <span class="text-danger"><i class="far fa-trash-alt"></i></span>
                                                </a>
                                            </div>
                                        </td>
                                        
                                    </tr>

                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <p><?php echo e(_trans('landlord.No property found')); ?></p>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <table class="table o_landy_table3 style4 mb-0">
                            <tbody>
                                <tr >
                                    <td colspan="4">
                                        <div class="m-0 d-flex justify-content-end gap_10 align-items-center">

                                                <h4><?php echo e(_trans('landlord.Sub Total')); ?></h4>

                                            <div class="single_total_right">
                                                ৳ <span id="updated-amount"> <?php echo e(@$data['totalAmount']); ?></span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex gap_10 align-items-center flex-wrap mt_20">
                        <div class="d-flex align-items-center gap_10 flex-fill flex-wrap">
                            <a href="<?php echo e(route('properties')); ?>"
                                class="o_land_primary_btn2 style3"><?php echo e(_trans('landlord.Add Another Property')); ?></a>
                        </div>
                        <a href="<?php echo e(route('customer.checkout')); ?>"
                            class="o_land_primary_btn min_200 style2"><?php echo e(_trans('landlord.Checkout')); ?></a>
                    </div>

            <?php else: ?>
            <div class="d-flex justify-content-center">
                <p>No Property Found</p>
            </div>
        <?php endif; ?>


        
    </div>
    <!-- checkout_v3_area::end  -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>
        $(document).ready(function() {
            // Listen to changes in the select element
            $('.o_land_select6').on('change', function() {
                // Get the selected month value and cart ID
                var selectedMonth = $(this).val();
                console.log(selectedMonth);
                var cartId = $(this).data('cart-id');
                console.log(cartId);
                $.ajax({
                    url: "<?php echo e(route('customer.checkOutProcess')); ?>",
                    method: 'POST',
                    data: {
                        month: selectedMonth,
                        cart_id: cartId,
                        _token: "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(response) {
                        console.log(response);
                        toastr.success(response.message, 'Success');
                        // Update the HTML on the page with the updated data
                        $('#updated-amount').html(response.data.totalAmount);
                    }
                });

                // Calculate the total amount for the selected cart
                var rentAmount = $('#rent-amount-' + cartId).text().replace(/[^\d]/g,
                    ''); // remove non-numeric characters from rent amount
                var totalAmount = rentAmount * selectedMonth;
                $('#total-amount-' + cartId).text('৳' + totalAmount);

                // Calculate the grand total amount for all carts
                var grandTotal = 0;
                $('.total-amount').each(function() {
                    var totalAmount = $(this).text().replace(/[^\d]/g,
                        ''); // remove non-numeric characters from total amount
                    grandTotal += parseInt(totalAmount);
                });
                $('#grand-total').text('৳' + grandTotal);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\landlord\resources\views/frontend/customer/cart.blade.php ENDPATH**/ ?>