{"__meta": {"id": "01JZ8DD3V4QPZ9E3VH2YGWVP72", "datetime": "2025-07-03 15:10:11", "utime": **********.812797, "method": "GET", "uri": "/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.327459, "end": **********.812812, "duration": 0.4853529930114746, "duration_str": "485ms", "measures": [{"label": "Booting", "start": **********.327459, "relative_start": 0, "end": **********.563368, "relative_end": **********.563368, "duration": 0.*****************, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.56338, "relative_start": 0.*****************, "end": **********.812814, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "249ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.573292, "relative_start": 0.****************, "end": **********.578162, "relative_end": **********.578162, "duration": 0.004869937896728516, "duration_str": "4.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: backend.dashboard", "start": **********.669893, "relative_start": 0.****************, "end": **********.669893, "relative_end": **********.669893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.partials.delete-ajax", "start": **********.686114, "relative_start": 0.****************, "end": **********.686114, "relative_end": **********.686114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.master", "start": **********.691995, "relative_start": 0.****************, "end": **********.691995, "relative_end": **********.691995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.partials.header", "start": **********.720444, "relative_start": 0.39298486709594727, "end": **********.720444, "relative_end": **********.720444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.partials.sidebar", "start": **********.732574, "relative_start": 0.40511488914489746, "end": **********.732574, "relative_end": **********.732574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.partials.footer", "start": **********.798481, "relative_start": 0.4710218906402588, "end": **********.798481, "relative_end": **********.798481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.partials.alert-message", "start": **********.805326, "relative_start": 0.47786688804626465, "end": **********.805326, "relative_end": **********.805326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 24903128, "peak_usage_str": "24MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Exception", "message": "Cannot add SymfonyMailCollector on Laravel Debugbar: Mailer [] is not defined.", "code": 0, "file": "vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php", "line": 740, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1243571182 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>534</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">addCollectorException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">Cannot add SymfonyMailCollector</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object InvalidArgumentException]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">boot</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">C:\\xampp\\htdocs\\landlord\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243571182\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["    protected function addCollectorException(string $message, Exception $exception)\n", "    {\n", "        $this->addThrowable(\n", "            new Exception(\n", "                $message . ' on Laravel Debugbar: ' . $exception->getMessage(),\n", "                $exception->getCode(),\n", "                $exception\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=740", "ajax": false, "filename": "LaravelDebugbar.php", "line": "740"}}, {"type": "InvalidArgumentException", "message": "Mailer [] is not defined.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Mail/MailManager.php", "line": 113, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1573718706 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>97</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"68 characters\">vendor/laravel/framework/src/Illuminate/Mail/MailServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>33</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Illuminate\\Mail\\MailManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>885</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Mail\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Mail\\MailServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>770</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">build</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>881</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>706</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>866</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1431</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>504</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">offsetGet</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">mailer</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">boot</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Barryvdh\\Debugbar\\LaravelDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">C:\\xampp\\htdocs\\landlord\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573718706\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["        $config = $this->getConfig($name);\n", "\n", "        if (is_null($config)) {\n", "            throw new InvalidArgumentException(\"Mailer [{$name}] is not defined.\");\n", "        }\n", "\n", "        // Once we have created the mailer instance we will set a container instance\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FMail%2FMailManager.php&line=113", "ajax": false, "filename": "MailManager.php", "line": "113"}}]}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "landlord.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "backend.dashboard", "param_count": null, "params": [], "start": **********.669836, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/backend/dashboard.blade.phpbackend.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fbackend%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "backend.partials.delete-ajax", "param_count": null, "params": [], "start": **********.686061, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/backend/partials/delete-ajax.blade.phpbackend.partials.delete-ajax", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fbackend%2Fpartials%2Fdelete-ajax.blade.php&line=1", "ajax": false, "filename": "delete-ajax.blade.php", "line": "?"}}, {"name": "backend.master", "param_count": null, "params": [], "start": **********.691903, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/backend/master.blade.phpbackend.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fbackend%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "backend.partials.header", "param_count": null, "params": [], "start": **********.720387, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/backend/partials/header.blade.phpbackend.partials.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fbackend%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "backend.partials.sidebar", "param_count": null, "params": [], "start": **********.732523, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/backend/partials/sidebar.blade.phpbackend.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fbackend%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "backend.partials.footer", "param_count": null, "params": [], "start": **********.798428, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/backend/partials/footer.blade.phpbackend.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fbackend%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "backend.partials.alert-message", "param_count": null, "params": [], "start": **********.805273, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\landlord\\resources\\views/backend/partials/alert-message.blade.phpbackend.partials.alert-message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fbackend%2Fpartials%2Falert-message.blade.php&line=1", "ajax": false, "filename": "alert-message.blade.php", "line": "?"}}]}, "queries": {"count": 58, "nb_statements": 58, "nb_visible_statements": 58, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02576, "accumulated_duration_str": "25.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from information_schema.tables where table_schema = 'landlord' and table_name = 'settings' and table_type = 'BASE TABLE'", "type": "query", "params": [], "bindings": ["landlord", "settings"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 26}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 881}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 706}], "start": **********.583378, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:26", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=26", "ajax": false, "filename": "DashboardController.php", "line": "26"}, "connection": "landlord", "explain": null, "start_percent": 0, "width_percent": 1.941}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.5907779, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "landlord", "explain": null, "start_percent": 1.941, "width_percent": 2.834}, {"sql": "select count(*) as aggregate from `users` where `role_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.600261, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:39", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=39", "ajax": false, "filename": "DashboardController.php", "line": "39"}, "connection": "landlord", "explain": null, "start_percent": 4.775, "width_percent": 1.669}, {"sql": "select count(*) as aggregate from `users` where `role_id` = 4 and `status` = 1", "type": "query", "params": [], "bindings": [4, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 40}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6026921, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:40", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=40", "ajax": false, "filename": "DashboardController.php", "line": "40"}, "connection": "landlord", "explain": null, "start_percent": 6.444, "width_percent": 1.126}, {"sql": "select count(*) as aggregate from `users` where `role_id` = 4 and `status` = 0", "type": "query", "params": [], "bindings": [4, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 41}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.60486, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:41", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=41", "ajax": false, "filename": "DashboardController.php", "line": "41"}, "connection": "landlord", "explain": null, "start_percent": 7.57, "width_percent": 1.825}, {"sql": "select count(*) as aggregate from `users` where `role_id` = 5", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6076, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:44", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=44", "ajax": false, "filename": "DashboardController.php", "line": "44"}, "connection": "landlord", "explain": null, "start_percent": 9.394, "width_percent": 2.329}, {"sql": "select count(*) as aggregate from `users` where `role_id` = 5 and `status` = 1", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 45}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.611019, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:45", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=45", "ajax": false, "filename": "DashboardController.php", "line": "45"}, "connection": "landlord", "explain": null, "start_percent": 11.724, "width_percent": 2.213}, {"sql": "select count(*) as aggregate from `users` where `role_id` = 5 and `status` = 0", "type": "query", "params": [], "bindings": [5, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.613704, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:46", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=46", "ajax": false, "filename": "DashboardController.php", "line": "46"}, "connection": "landlord", "explain": null, "start_percent": 13.936, "width_percent": 1.941}, {"sql": "select count(*) as aggregate from `properties`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.616125, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:49", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=49", "ajax": false, "filename": "DashboardController.php", "line": "49"}, "connection": "landlord", "explain": null, "start_percent": 15.877, "width_percent": 1.747}, {"sql": "select count(*) as aggregate from `properties` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.618319, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:50", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=50", "ajax": false, "filename": "DashboardController.php", "line": "50"}, "connection": "landlord", "explain": null, "start_percent": 17.624, "width_percent": 1.747}, {"sql": "select count(*) as aggregate from `properties` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.620676, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=51", "ajax": false, "filename": "DashboardController.php", "line": "51"}, "connection": "landlord", "explain": null, "start_percent": 19.371, "width_percent": 1.786}, {"sql": "select count(*) as aggregate from `blogs`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6233191, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:54", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=54", "ajax": false, "filename": "DashboardController.php", "line": "54"}, "connection": "landlord", "explain": null, "start_percent": 21.157, "width_percent": 1.747}, {"sql": "select count(*) as aggregate from `blogs` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 55}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.627377, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:55", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=55", "ajax": false, "filename": "DashboardController.php", "line": "55"}, "connection": "landlord", "explain": null, "start_percent": 22.904, "width_percent": 2.873}, {"sql": "select count(*) as aggregate from `blogs` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.630349, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:56", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=56", "ajax": false, "filename": "DashboardController.php", "line": "56"}, "connection": "landlord", "explain": null, "start_percent": 25.776, "width_percent": 2.135}, {"sql": "select count(*) as aggregate from `users` where `id` != 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 85}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.632887, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:85", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FRepositories%2FUserRepository.php&line=85", "ajax": false, "filename": "UserRepository.php", "line": "85"}, "connection": "landlord", "explain": null, "start_percent": 27.911, "width_percent": 1.669}, {"sql": "select * from `users` where `id` != 1 order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 85}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.635152, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:85", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FRepositories%2FUserRepository.php&line=85", "ajax": false, "filename": "UserRepository.php", "line": "85"}, "connection": "landlord", "explain": null, "start_percent": 29.581, "width_percent": 2.562}, {"sql": "select * from `images` where `images`.`id` in (5, 6, 7, 8, 9, 10, 11, 12, 13, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 85}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6395872, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:85", "source": {"index": 20, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FRepositories%2FUserRepository.php&line=85", "ajax": false, "filename": "UserRepository.php", "line": "85"}, "connection": "landlord", "explain": null, "start_percent": 32.143, "width_percent": 1.863}, {"sql": "select count(*) as aggregate from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.644571, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:68", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FRepositories%2FUserRepository.php&line=68", "ajax": false, "filename": "UserRepository.php", "line": "68"}, "connection": "landlord", "explain": null, "start_percent": 34.006, "width_percent": 2.484}, {"sql": "select * from `users` order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.647443, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:68", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FRepositories%2FUserRepository.php&line=68", "ajax": false, "filename": "UserRepository.php", "line": "68"}, "connection": "landlord", "explain": null, "start_percent": 36.491, "width_percent": 3.144}, {"sql": "select * from `images` where `images`.`id` in (5, 6, 7, 8, 9, 10, 11, 12, 13, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 68}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 61}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.650493, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:68", "source": {"index": 20, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FRepositories%2FUserRepository.php&line=68", "ajax": false, "filename": "UserRepository.php", "line": "68"}, "connection": "landlord", "explain": null, "start_percent": 39.635, "width_percent": 2.096}, {"sql": "select * from `designations` where `designations`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 68}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 61}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6541948, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:68", "source": {"index": 20, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\UserRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FRepositories%2FUserRepository.php&line=68", "ajax": false, "filename": "UserRepository.php", "line": "68"}, "connection": "landlord", "explain": null, "start_percent": 41.731, "width_percent": 1.553}, {"sql": "select `id`, `title` from `designations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 62}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.656441, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:62", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=62", "ajax": false, "filename": "DashboardController.php", "line": "62"}, "connection": "landlord", "explain": null, "start_percent": 43.284, "width_percent": 1.786}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.664525, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 45.07, "width_percent": 2.057}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.66755, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 47.127, "width_percent": 1.863}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.68138, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 48.991, "width_percent": 1.708}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.6839051, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 50.699, "width_percent": 1.786}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.687092, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 52.484, "width_percent": 1.242}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.689328, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 53.727, "width_percent": 1.32}, {"sql": "select * from `settings` where `name` = 'favicon' limit 1", "type": "query", "params": [], "bindings": ["favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.694011, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 55.047, "width_percent": 2.989}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.696869, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 58.036, "width_percent": 1.514}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.698995, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 59.55, "width_percent": 1.32}, {"sql": "select `direction` from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.701751, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:29", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=29", "ajax": false, "filename": "common-helpers.php", "line": "29"}, "connection": "landlord", "explain": null, "start_percent": 60.87, "width_percent": 1.398}, {"sql": "select `direction` from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.704134, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:29", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=29", "ajax": false, "filename": "common-helpers.php", "line": "29"}, "connection": "landlord", "explain": null, "start_percent": 62.267, "width_percent": 1.281}, {"sql": "select `direction` from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.706427, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:29", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=29", "ajax": false, "filename": "common-helpers.php", "line": "29"}, "connection": "landlord", "explain": null, "start_percent": 63.548, "width_percent": 1.242}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/LanguageRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\LanguageRepository.php", "line": 21}, {"index": 16, "namespace": null, "name": "app/View/Composers/LanguageComposer.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\View\\Composers\\LanguageComposer.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}], "start": **********.7113218, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "LanguageRepository.php:21", "source": {"index": 15, "namespace": null, "name": "app/Repositories/LanguageRepository.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Repositories\\LanguageRepository.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FRepositories%2FLanguageRepository.php&line=21", "ajax": false, "filename": "LanguageRepository.php", "line": "21"}, "connection": "landlord", "explain": null, "start_percent": 64.79, "width_percent": 2.252}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Composers/LanguageComposer.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\View\\Composers\\LanguageComposer.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.714153, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "LanguageComposer.php:38", "source": {"index": 15, "namespace": null, "name": "app/View/Composers/LanguageComposer.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\View\\Composers\\LanguageComposer.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FView%2FComposers%2FLanguageComposer.php&line=38", "ajax": false, "filename": "LanguageComposer.php", "line": "38"}, "connection": "landlord", "explain": null, "start_percent": 67.042, "width_percent": 1.436}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.716241, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 68.478, "width_percent": 1.009}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.718398, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 69.488, "width_percent": 1.281}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.partials.header", "file": "C:\\xampp\\htdocs\\landlord\\resources\\views/backend/partials/header.blade.php", "line": 428}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.72259, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "backend.partials.header:428", "source": {"index": 20, "namespace": "view", "name": "backend.partials.header", "file": "C:\\xampp\\htdocs\\landlord\\resources\\views/backend/partials/header.blade.php", "line": 428}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fresources%2Fviews%2Fbackend%2Fpartials%2Fheader.blade.php&line=428", "ajax": false, "filename": "header.blade.php", "line": "428"}, "connection": "landlord", "explain": null, "start_percent": 70.769, "width_percent": 1.165}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.727414, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 71.933, "width_percent": 2.096}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.730361, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 74.03, "width_percent": 1.786}, {"sql": "select * from `settings` where `name` = 'light_logo' limit 1", "type": "query", "params": [], "bindings": ["light_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.733342, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 75.815, "width_percent": 1.398}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.735464, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 77.213, "width_percent": 1.359}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.737681, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 78.571, "width_percent": 1.32}, {"sql": "select * from `settings` where `name` = 'dark_logo' limit 1", "type": "query", "params": [], "bindings": ["dark_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.739693, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 79.891, "width_percent": 1.32}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.7434788, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 81.211, "width_percent": 2.368}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.7462862, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 83.579, "width_percent": 1.747}, {"sql": "select * from `settings` where `name` = 'light_logo' limit 1", "type": "query", "params": [], "bindings": ["light_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.748976, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 85.326, "width_percent": 1.203}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.750964, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 86.53, "width_percent": 1.32}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.752925, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 87.849, "width_percent": 1.281}, {"sql": "select * from `settings` where `name` = 'favicon' limit 1", "type": "query", "params": [], "bindings": ["favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.754939, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 89.13, "width_percent": 1.281}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.7569401, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 90.411, "width_percent": 1.281}, {"sql": "select * from `settings` where `name` = 'file_system' limit 1", "type": "query", "params": [], "bindings": ["file_system"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.760795, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 91.693, "width_percent": 1.863}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.793819, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 93.556, "width_percent": 1.863}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.796408, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 95.419, "width_percent": 1.242}, {"sql": "select * from `settings` where `name` = 'footer_text' limit 1", "type": "query", "params": [], "bindings": ["footer_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.798804, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "common-helpers.php:19", "source": {"index": 15, "namespace": null, "name": "app/Helpers/common-helpers.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Helpers\\common-helpers.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHelpers%2Fcommon-helpers.php&line=19", "ajax": false, "filename": "common-helpers.php", "line": "19"}, "connection": "landlord", "explain": null, "start_percent": 96.661, "width_percent": 1.087}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.8009992, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:27", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=27", "ajax": false, "filename": "AppServiceProvider.php", "line": "27"}, "connection": "landlord", "explain": null, "start_percent": 97.748, "width_percent": 1.165}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\landlord\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "start": **********.8032322, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:28", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\landlord\\app\\Providers\\AppServiceProvider.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FProviders%2FAppServiceProvider.php&line=28", "ajax": false, "filename": "AppServiceProvider.php", "line": "28"}, "connection": "landlord", "explain": null, "start_percent": 98.913, "width_percent": 1.087}]}, "models": {"data": {"App\\Models\\Language": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Image": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FImage.php&line=1", "ajax": false, "filename": "Image.php", "line": "?"}}, "App\\Models\\Setting": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Designation": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FDesignation.php&line=1", "ajax": false, "filename": "Designation.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 93, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/dashboard", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\Backend\\DashboardController@index", "uri": "GET dashboard", "controller": "App\\Http\\Controllers\\Backend\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Flandlord%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/DashboardController.php:32-74</a>", "middleware": "web, auth.routes", "duration": "504ms", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-956313275 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-956313275\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-723939166 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-723939166\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-437343213 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6ImhsMXRmTVZmM1VqdTBaMzVUaFZXZFE9PSIsInZhbHVlIjoidkY2ZmVqKzdLeVpLUEh6V043WHRyY1F5bmoyV2tYdk8yakpXS1JVVjhPMG9hYUlidWN4aGxOQlZjY2ZZWDFXSGtVSDFnNDJjdnN0bWQyRFUxVEJBK0dxT3hCMStwOTl6SEFsbDNwNDNDT0s2K3d6cnhnbXdWNk5OYkF1VWl5ZEsiLCJtYWMiOiJmN2EyYmU4ZmZhNTExMjQzZmM3ZDk4YTcwYTA2MTk2M2Q2Mzc4NjQyYjY2YWM2YjJiYTI2M2Q4ZDc2Y2ZjZDhmIiwidGFnIjoiIn0%3D; landlord_session=eyJpdiI6Ik1wU3JZNlVKZkpYTy85NHU5UlJ0d1E9PSIsInZhbHVlIjoiaFNUSHlPKzA2cmd0VjVBSHB0dlBhaFFGYW81bGNHWGhiTDFwY0FMdkIrVWJpRFZDNFlBZnpXRnJBbzJJVWFsWlBNVFpjOTljenB5aDFsT0ttb0VrbTRqMWdQZ2pjSWZZdzAyTy85RUE5MUhwWU5qUVBFSnN5NzdJaFJiTGFLSjUiLCJtYWMiOiI0OWJhM2QwZGVjM2U3ZjBiZjM2MGYwZjA1NTcwMGJmOWI3NjczNGE4MGJiMzIyYjNkNDNmMThlMWRmYmI0ZGMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437343213\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-927724429 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zd8n8oVrnqq7VrOib2a0aR6Mg0x9iKyNiIzK3AUb</span>\"\n  \"<span class=sf-dump-key>landlord_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XVE6cioz7w1VYx9blYXT2UOByuO4hyBIZOxJjykp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927724429\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-267318339 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 15:10:11 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-267318339\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-467786414 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zd8n8oVrnqq7VrOib2a0aR6Mg0x9iKyNiIzK3AUb</span>\"\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467786414\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/dashboard", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\Backend\\DashboardController@index"}, "badge": null}}