/*
    Main scss for the application.

    Index
    -----

    1. Variables
    2. Mixins
    3. Functions
    4. Fonts
    5. Config
    6. Components
        1) Scrollbar
        2) Topbar
        3) Notification
        4) Profile Expand
        5) Sidebar
        6) Theme Switch

*/
/*
    --------------------------------------------------
    Mixins for the base stylesheet.
    Here will import all the mixins from the functions file.
    --------------------------------------------------
*/
/*
    --------------------------------------------------
    Break points variables
    --------------------------------------------------
*/
/*
    --------------------------------------------------
    Colors variables
    --------------------------------------------------
*/
/* Primary Blue */
/* Secondary Cyan  */
/* Success */
/* Danger  */
/* Warning */
/* Accent */
/*
    Media Queries mixin for the theme

    --------------------------------------------------
    Break points variables
    --------------------------------------------------
    @break-xs: 576px; // Extra Small Devices, Phones
    @break-sm: 576px; // Small Devices, Tablets
    @break-md: 768px; // Medium Devices, Desktops
    @break-lg: 992px; // Large Devices, Laptops, Desktops
    @break-xl: 1200px; // Extra Large Devices, Wide Laptops, Desktops
    @break-xxl: 1400px; // Extra Extra Large Devices, Wide Desktops
    --------------------------------------------------

*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lexend:wght@300;400;500;600;700&family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap");

/* text style */
.text-success {
    color: #00BF08 !important;
}

.input-w-100 {
    width: 100%;
    max-width: 100% !important;
}
.w-50percent {
    width: 50%;
}

.dropdown-text-light {
  color: #6F767E;
}

.dropdown-text-dark {
  color: #1A1D1F;
}

.dropdown-text-red {
  color: #FF0022;
}

.dropdown-text-disable {
  color: #B2BEC3;
}

.dropdown-text-blue {
  color: #645CBB;
}

.dropdown-content {
  margin-top: 24px;
  margin-left: 32px;
}

.dropdown-section {
  padding: 80px;
  background: #FCFCFC;
  margin-top: 8px;
}

.dropdown-section h1 {
  font-weight: 700;
  font-size: 32px;
  text-transform: uppercase;
  color: #120D26;
}

.dropdown-section p {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #4B4B4B;
}

.button-title {
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  color: #120D26;
}

.dropdown-with-down-arrow, .dropdown2-with-down-arrow {
  margin-top: 12px;
  background-color: #FCFCFC;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1A1D1F;
  border: 2px solid #F0EEEE;
  border-radius: 5px;
}

.dropdown-with-down-arrow i, .dropdown2-with-down-arrow i {
  margin-left: 12px;
  font-weight: bold;
}

.dropdown-with-down-arrow:hover, .dropdown2-with-down-arrow:hover {
  border: 2px solid #645CBB;
  color: #645CBB;
}

.dropdown-with-down-arrow:hover i, .dropdown2-with-down-arrow:hover i {
  color: #645CBB;
}

.dropdown-with-down-arrow:focus, .dropdown2-with-down-arrow:focus {
  border: 2px solid #4D5EE5;
  color: #066ECF;
}

.dropdown-with-down-arrow:focus i, .dropdown2-with-down-arrow:focus i {
  color: #066ECF;
}

.dropdwon-btn-section {
  margin-top: 40px;
}

.dropdown-with-dots {
  margin-top: 12px;
}

button {
  border: none;
  outline: none;
}

.dropdown-menu {
  margin-top: 10px !important;
}

#dropdown {
  background-color: #FCFCFC;
  border: 2px solid #F0EEEE;
  border-radius: 5px 0px 0px 5px;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1A1D1F;
}

#dropdown:hover {
  border: 2px solid #645CBB;
  color: #645CBB;
}

#dropdown:focus {
  border: 2px solid #4D5EE5;
  color: #066ECF;
}

#three-dots {
  background-color: #FCFCFC;
  border: 2px solid #F0EEEE;
  border-radius: 0px 5px 5px 0px;
  padding: 8px;
  letter-spacing: 0.02em;
  color: #1A1D1F;
}

#three-dots:hover {
  border: 2px solid #645CBB;
  color: #645CBB;
}

#three-dots:focus {
  border: 2px solid #4D5EE5;
  color: #066ECF;
}

.dropdown-items, .second-item, .third-item {
  position: relative;
  width: 191px;
  background: #FFFFFF;
  border: 1px solid #F0EEEE;
  border-radius: 7px;
}

.dropdown-items ul, .second-item ul, .third-item ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 16px;
  padding-right: 0px;
  gap: 16px;
}

.dropdown-items ul .text-secondary>i, .second-item ul .text-secondary>i, .third-item ul .text-secondary>i {
  padding-right: 16px;
}

.dropdown-items ul li, .second-item ul li, .third-item ul li {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  padding: 8px;
  padding-right: 24px;
}

.dropdown-items ul .dropdown-text-dark>i, .second-item ul .dropdown-text-dark>i, .third-item ul .dropdown-text-dark>i {
  padding-left: 16px;
}

.second-item {
  display: block;
  width: 180px;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  left: 100%;
  margin-top: -40px;
  transition: all ease-in-out 0.4s;
}

.third-item {
  display: none;
  width: 180px;
  position: absolute;
  left: 112%;
  margin-top: -66px;
}

ul li.dropdown-text-dark:hover .second-item {
  visibility: visible;
  opacity: 1;
  transition-delay: 0s;
  margin-left: 10px;
}

.dropdown-search-checkbox {
  margin-top: 12px;
  background-color: #FCFCFC;
  border: 2px solid #F0EEEE;
  border-radius: 50px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.02em;
  padding: 8px 16px;
  color: #645CBB;
}

.search-container {
  margin-top: 12px;
  width: 272px;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
}

.search-input-checkbox input {
  width: 20px !important;
  height: 20px;
  border: 1px solid #EAEAEA;
  border-radius: 5px;
}

.search-input-checkbox label {
  color: #6F767E;
  margin-left: 12px;
}

.search-input-checkbox ul li {
  display: flex;
}

.search-input {
  position: relative;
  text-align: center;
  padding: 15px 16px 15px 16px;
  border-bottom: 2px solid #F0EEEE;
}

.search-input input {
  padding: 12px;
  width: 100%;
  border: 2px solid #F0EEEE;
  border-radius: 50px;
  background: #FAFAFA;
  outline: none;
}

.search-input ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.search-input i {
  position: absolute;
  top: 29px;
  right: 27px;
  padding: 5px;
  background-color: #FAFAFA;
  color: #6F767E;
}

.search-items {
  padding: 17px 25px;
}

.search-items ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.search-items ul li {
  color: #6F767E;
}

.search-input ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 8px 16px;
  gap: 16px;
}

.search-input ul li {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #6F767E;
}

.search-input ul li input {
  width: auto;
}

.search-input ul li ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.search-container .btn-items {
  padding: 5px 17px 25px;
  text-align: end;
}

.search-container .btn-items .btn {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.search-container .btn-items .btn.clear {
  color: #6F767E;
}

.input-default input, .input-date input {
  padding: 16px;
  width: 336px;
  height: 48px;
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  outline: none;
}

.input-default ::placeholder, .input-date ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.input-field-focus {
  width: 360px;
}

.input-field-focus input {
  width: 100%;
  height: 48px;
  padding: 16px;
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  outline: none;
}

.input-field-focus input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010F7;
  outline: none;
}

.input-field-focus ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.input-with-icon {
  position: relative;
  margin-top: 16px;
  width: 336px;
  height: 48px;
}

.input-with-icon input {
  width: 336px;
  height: 48px;
  padding: 16px 45px;
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  outline: none;
}

.input-with-icon input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010F7;
  outline: none;
}

.input-with-icon ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.input-with-icon i {
  position: absolute;
  top: 16px;
  color: #6F767E;
}

.input-with-icon i.fa-user-o {
  left: 20px;
}

.input-with-icon i.fa-search {
  right: 20px;
}

.input-pre-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}

.input-pre-post input {
  padding: 16px 45px;
  width: 296px;
  height: 48px;
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  outline: none;
}

.input-pre-post ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.input-pre-post i {
  position: absolute;
  top: 16px;
  color: #6F767E;
}

.input-pre-post i.fa-user-o {
  left: 20px;
}

.input-pre-post i.fa-search {
  right: 60px;
}

.input-pre-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #F7F7F7;
  border-left: 2px solid #F0EEEE;
  border-radius: 0px 5px 5px 0px;
}

.input-https-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}

.input-https-post input {
  padding: 16px 43px 16px 110px;
  width: 296px;
  height: 48px;
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  outline: none;
}

.input-https-post ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.input-https-post i {
  position: absolute;
  top: 16px;
  color: #6F767E;
}

.input-https-post i.fa-user-o {
  left: 88px;
}

.input-https-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #F7F7F7;
  border-left: 2px solid #F0EEEE;
  border-radius: 0px 5px 5px 0px;
}

.input-https-post .https {
  position: absolute;
  color: #1A1D1F;
  top: 2px;
  left: 1.5px;
  background-color: #F7F7F7;
  padding: 12px;
  border-right: 2px solid #F0EEEE;
  border-radius: 5px 0px 0px 5px;
}

.input-https-post2 {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}

.input-https-post2 input {
  padding: 16px 43px 16px 86px;
  width: 296px;
  height: 48px;
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  outline: none;
}

.input-https-post2 ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.input-https-post2 i {
  position: absolute;
  top: 16px;
  color: #6F767E;
}

.input-https-post2 i.fa-user-o {
  left: 88px;
}

.input-https-post2 i.fa-search {
  right: 20px;
}

.input-https-post2 .https {
  position: absolute;
  color: #1A1D1F;
  top: 2px;
  left: 1.5px;
  background-color: #F7F7F7;
  padding: 12px;
  border-right: 2px solid #F0EEEE;
  border-radius: 5px 0px 0px 5px;
}

textarea {
  width: 280px;
  height: 79px;
  margin-top: 16px;
  padding: 12px 16px;
  background: #FFFFFF;
  border: 1px solid #EAEAEA;
  border-radius: 7px;
  outline: none;
}

textarea::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.text-area {
  width: 280px;
  height: 79px;
}

.text-area .text-count {
  font-family: 'Lexend';
  font-size: 10px;
  font-weight: 500;
  text-align: end;
  color: #B2BEC3;
}

.Input-search-tab {
  position: relative;
  margin-top: 16px;
  width: 273px;
  height: 48px;
}

.Input-search-tab input {
  padding: 16px 43px 16px 16px;
  width: 100%;
  height: 100%;
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  outline: none;
}

.Input-search-tab ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #B2BEC3;
}

.Input-search-tab i {
  position: absolute;
  top: 0;
  color: #6F767E;
  border-left: 2px solid #F0EEEE;
  padding: 16px;
}

.Input-search-tab i.fa-search {
  right: 0px;
}

.Input-search-tab .search {
  position: absolute;
  color: #ffffff;
  top: 0px;
  right: 0px;
  background-color: #645CBB;
  padding: 13px;
  border-radius: 0px 5px 5px 0px;
}

.Input-search-tab i.fa-microphone {
  right: 70px;
  color: #645CBB;
  border: none;
}

.Input-search-tab.search-color>input {
  width: 289px;
}

.Input-search-tab.search-color>i {
  right: -15px;
  color: #FAFAFA;
  background-color: #645CBB;
  border-radius: 0px 5px 5px 0px;
}

.Input-search-tab .search-tab1 {
  width: 237px;
}

.Input-search-tab.microphone>input {
  padding: 16px 110px 16px 16px;
}

.input-date {
  margin-top: 16px;
}

.input-date input {
  width: 272px;
}

.Range-date {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 272px;
  height: 48px;
  flex-direction: row;
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  margin-top: 16px;
  padding: 10px 16px;
  gap: 5px;
}

.Range-date input {
  border: none;
  outline: none;
}

.time-field {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex-direction: row;
  width: 386px;
  gap: 5px;
  margin-top: 16px;
}

.time-field .input-time {
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  width: 107px;
  height: 50px;
}

.time-field .input-time select {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: #1A1D1F;
  border: none;
  outline: none;
  padding: 11px 16px;
}

.time-field .select-time input {
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  width: 272px;
  height: 48px;
  outline: none;
  padding: 16px;
}

.input-group-start-end-time {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  justify-content: flex-end;
  flex-direction: row;
  gap: 10px;
}

.input-group-start-end-time .input-start-end-time {
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  width: 169px;
  height: 44px;
  padding: 12px 16px;
  color: #6F767E;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.input-group-start-end-time .input-start-end-time span {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

option:hover {
  color: #645CBB;
}

.input-time-select {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-top: 16px;
}

.input-time-select .input-start-end-time-select {
  border: 2px solid #F0EEEE;
  border-radius: 5px;
  width: 178px;
  height: 60px;
  padding: 12px 16px;
  color: #6F767E;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.input-time-select .input-start-end-time-select p {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  color: #0010F7;
}

.input-time-select .input-start-end-time-select h6 {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  color: #1A1D1F;
}

/*
    --------------------------------------------------
    Functions for the base stylesheet.
    Here will import all the functions from the functions file.
    --------------------------------------------------
*/
.pa-0 {
  padding: 0px !important;
}

.pl-0 {
  padding-left: 0px !important;
}

.pt-0 {
  padding-top: 0px !important;
}

.pr-0 {
  padding-right: 0px !important;
}

.pb-0 {
  padding-bottom: 0px !important;
}

.pv-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.ph-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.pa-2 {
  padding: 2px !important;
}

.pl-2 {
  padding-left: 2px !important;
}

.pt-2 {
  padding-top: 2px !important;
}

.pr-2 {
  padding-right: 2px !important;
}

.pb-2 {
  padding-bottom: 2px !important;
}

.pv-2 {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}

.ph-2 {
  padding-left: 2px !important;
  padding-right: 2px !important;
}

.pa-4 {
  padding: 4px !important;
}

.pl-4 {
  padding-left: 4px !important;
}

.pt-4 {
  padding-top: 4px !important;
}

.pr-4 {
  padding-right: 4px !important;
}

.pb-4 {
  padding-bottom: 4px !important;
}

.pv-4 {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

.ph-4 {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.pa-6 {
  padding: 6px !important;
}

.pl-6 {
  padding-left: 6px !important;
}

.pt-6 {
  padding-top: 6px !important;
}

.pr-6 {
  padding-right: 6px !important;
}

.pb-6 {
  padding-bottom: 6px !important;
}

.pv-6 {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}

.ph-6 {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.pa-8 {
  padding: 8px !important;
}

.pl-8 {
  padding-left: 8px !important;
}

.pt-8 {
  padding-top: 8px !important;
}

.pr-8 {
  padding-right: 8px !important;
}

.pb-8 {
  padding-bottom: 8px !important;
}

.pv-8 {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

.ph-8 {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.pa-10 {
  padding: 10px !important;
}

.pl-10 {
  padding-left: 10px !important;
}

.pt-10 {
  padding-top: 10px !important;
}

.pr-10 {
  padding-right: 10px !important;
}

.pb-10 {
  padding-bottom: 10px !important;
}

.pv-10 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.ph-10 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.pa-12 {
  padding: 12px !important;
}

.pl-12 {
  padding-left: 12px !important;
}

.pt-12 {
  padding-top: 12px !important;
}

.pr-12 {
  padding-right: 12px !important;
}

.pb-12 {
  padding-bottom: 12px !important;
}

.pv-12 {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.ph-12 {
  padding-left: 12px !important;
  padding-right: 12px !important;
}

.pa-14 {
  padding: 14px !important;
}

.pl-14 {
  padding-left: 14px !important;
}

.pt-14 {
  padding-top: 14px !important;
}

.pr-14 {
  padding-right: 14px !important;
}

.pb-14 {
  padding-bottom: 14px !important;
}

.pv-14 {
  padding-top: 14px !important;
  padding-bottom: 14px !important;
}

.ph-14 {
  padding-left: 14px !important;
  padding-right: 14px !important;
}

.pa-16 {
  padding: 16px !important;
}

.pl-16 {
  padding-left: 16px !important;
}

.pt-16 {
  padding-top: 16px !important;
}

.pr-16 {
  padding-right: 16px !important;
}

.pb-16 {
  padding-bottom: 16px !important;
}

.pv-16 {
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

.ph-16 {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

.pa-18 {
  padding: 18px !important;
}

.pl-18 {
  padding-left: 18px !important;
}

.pt-18 {
  padding-top: 18px !important;
}

.pr-18 {
  padding-right: 18px !important;
}

.pb-18 {
  padding-bottom: 18px !important;
}

.pv-18 {
  padding-top: 18px !important;
  padding-bottom: 18px !important;
}

.ph-18 {
  padding-left: 18px !important;
  padding-right: 18px !important;
}

.pa-20 {
  padding: 20px !important;
}

.pl-20 {
  padding-left: 20px !important;
}

.pt-20 {
  padding-top: 20px !important;
}

.pr-20 {
  padding-right: 20px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.pv-20 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.ph-20 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.pa-22 {
  padding: 22px !important;
}

.pl-22 {
  padding-left: 22px !important;
}

.pt-22 {
  padding-top: 22px !important;
}

.pr-22 {
  padding-right: 22px !important;
}

.pb-22 {
  padding-bottom: 22px !important;
}

.pv-22 {
  padding-top: 22px !important;
  padding-bottom: 22px !important;
}

.ph-22 {
  padding-left: 22px !important;
  padding-right: 22px !important;
}

.pa-24 {
  padding: 24px !important;
}

.pl-24 {
  padding-left: 24px !important;
}

.pt-24 {
  padding-top: 24px !important;
}

.pr-24 {
  padding-right: 24px !important;
}

.pb-24 {
  padding-bottom: 24px !important;
}

.pv-24 {
  padding-top: 24px !important;
  padding-bottom: 24px !important;
}

.ph-24 {
  padding-left: 24px !important;
  padding-right: 24px !important;
}

.pa-26 {
  padding: 26px !important;
}

.pl-26 {
  padding-left: 26px !important;
}

.pt-26 {
  padding-top: 26px !important;
}

.pr-26 {
  padding-right: 26px !important;
}

.pb-26 {
  padding-bottom: 26px !important;
}

.pv-26 {
  padding-top: 26px !important;
  padding-bottom: 26px !important;
}

.ph-26 {
  padding-left: 26px !important;
  padding-right: 26px !important;
}

.pa-28 {
  padding: 28px !important;
}

.pl-28 {
  padding-left: 28px !important;
}

.pt-28 {
  padding-top: 28px !important;
}

.pr-28 {
  padding-right: 28px !important;
}

.pb-28 {
  padding-bottom: 28px !important;
}

.pv-28 {
  padding-top: 28px !important;
  padding-bottom: 28px !important;
}

.ph-28 {
  padding-left: 28px !important;
  padding-right: 28px !important;
}

.pa-30 {
  padding: 30px !important;
}

.pl-30 {
  padding-left: 30px !important;
}

.pt-30 {
  padding-top: 30px !important;
}

.pr-30 {
  padding-right: 30px !important;
}

.pb-30 {
  padding-bottom: 30px !important;
}

.pv-30 {
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

.ph-30 {
  padding-left: 30px !important;
  padding-right: 30px !important;
}

.pa-32 {
  padding: 32px !important;
}

.pl-32 {
  padding-left: 32px !important;
}

.pt-32 {
  padding-top: 32px !important;
}

.pr-32 {
  padding-right: 32px !important;
}

.pb-32 {
  padding-bottom: 32px !important;
}

.pv-32 {
  padding-top: 32px !important;
  padding-bottom: 32px !important;
}

.ph-32 {
  padding-left: 32px !important;
  padding-right: 32px !important;
}

.pa-34 {
  padding: 34px !important;
}

.pl-34 {
  padding-left: 34px !important;
}

.pt-34 {
  padding-top: 34px !important;
}

.pr-34 {
  padding-right: 34px !important;
}

.pb-34 {
  padding-bottom: 34px !important;
}

.pv-34 {
  padding-top: 34px !important;
  padding-bottom: 34px !important;
}

.ph-34 {
  padding-left: 34px !important;
  padding-right: 34px !important;
}

.pa-36 {
  padding: 36px !important;
}

.pl-36 {
  padding-left: 36px !important;
}

.pt-36 {
  padding-top: 36px !important;
}

.pr-36 {
  padding-right: 36px !important;
}

.pb-36 {
  padding-bottom: 36px !important;
}

.pv-36 {
  padding-top: 36px !important;
  padding-bottom: 36px !important;
}

.ph-36 {
  padding-left: 36px !important;
  padding-right: 36px !important;
}

.pa-38 {
  padding: 38px !important;
}

.pl-38 {
  padding-left: 38px !important;
}

.pt-38 {
  padding-top: 38px !important;
}

.pr-38 {
  padding-right: 38px !important;
}

.pb-38 {
  padding-bottom: 38px !important;
}

.pv-38 {
  padding-top: 38px !important;
  padding-bottom: 38px !important;
}

.ph-38 {
  padding-left: 38px !important;
  padding-right: 38px !important;
}

.pa-40 {
  padding: 40px !important;
}

.pl-40 {
  padding-left: 40px !important;
}

.pt-40 {
  padding-top: 40px !important;
}

.pr-40 {
  padding-right: 40px !important;
}

.pb-40 {
  padding-bottom: 40px !important;
}

.pv-40 {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.ph-40 {
  padding-left: 40px !important;
  padding-right: 40px !important;
}

.pa-42 {
  padding: 42px !important;
}

.pl-42 {
  padding-left: 42px !important;
}

.pt-42 {
  padding-top: 42px !important;
}

.pr-42 {
  padding-right: 42px !important;
}

.pb-42 {
  padding-bottom: 42px !important;
}

.pv-42 {
  padding-top: 42px !important;
  padding-bottom: 42px !important;
}

.ph-42 {
  padding-left: 42px !important;
  padding-right: 42px !important;
}

.pa-44 {
  padding: 44px !important;
}

.pl-44 {
  padding-left: 44px !important;
}

.pt-44 {
  padding-top: 44px !important;
}

.pr-44 {
  padding-right: 44px !important;
}

.pb-44 {
  padding-bottom: 44px !important;
}

.pv-44 {
  padding-top: 44px !important;
  padding-bottom: 44px !important;
}

.ph-44 {
  padding-left: 44px !important;
  padding-right: 44px !important;
}

.pa-46 {
  padding: 46px !important;
}

.pl-46 {
  padding-left: 46px !important;
}

.pt-46 {
  padding-top: 46px !important;
}

.pr-46 {
  padding-right: 46px !important;
}

.pb-46 {
  padding-bottom: 46px !important;
}

.pv-46 {
  padding-top: 46px !important;
  padding-bottom: 46px !important;
}

.ph-46 {
  padding-left: 46px !important;
  padding-right: 46px !important;
}

.pa-48 {
  padding: 48px !important;
}

.pl-48 {
  padding-left: 48px !important;
}

.pt-48 {
  padding-top: 48px !important;
}

.pr-48 {
  padding-right: 48px !important;
}

.pb-48 {
  padding-bottom: 48px !important;
}

.pv-48 {
  padding-top: 48px !important;
  padding-bottom: 48px !important;
}

.ph-48 {
  padding-left: 48px !important;
  padding-right: 48px !important;
}

.pa-50 {
  padding: 50px !important;
}

.pl-50 {
  padding-left: 50px !important;
}

.pt-50 {
  padding-top: 50px !important;
}

.pr-50 {
  padding-right: 50px !important;
}

.pb-50 {
  padding-bottom: 50px !important;
}

.pv-50 {
  padding-top: 50px !important;
  padding-bottom: 50px !important;
}

.ph-50 {
  padding-left: 50px !important;
  padding-right: 50px !important;
}

.pa-52 {
  padding: 52px !important;
}

.pl-52 {
  padding-left: 52px !important;
}

.pt-52 {
  padding-top: 52px !important;
}

.pr-52 {
  padding-right: 52px !important;
}

.pb-52 {
  padding-bottom: 52px !important;
}

.pv-52 {
  padding-top: 52px !important;
  padding-bottom: 52px !important;
}

.ph-52 {
  padding-left: 52px !important;
  padding-right: 52px !important;
}

.pa-54 {
  padding: 54px !important;
}

.pl-54 {
  padding-left: 54px !important;
}

.pt-54 {
  padding-top: 54px !important;
}

.pr-54 {
  padding-right: 54px !important;
}

.pb-54 {
  padding-bottom: 54px !important;
}

.pv-54 {
  padding-top: 54px !important;
  padding-bottom: 54px !important;
}

.ph-54 {
  padding-left: 54px !important;
  padding-right: 54px !important;
}

.pa-56 {
  padding: 56px !important;
}

.pl-56 {
  padding-left: 56px !important;
}

.pt-56 {
  padding-top: 56px !important;
}

.pr-56 {
  padding-right: 56px !important;
}

.pb-56 {
  padding-bottom: 56px !important;
}

.pv-56 {
  padding-top: 56px !important;
  padding-bottom: 56px !important;
}

.ph-56 {
  padding-left: 56px !important;
  padding-right: 56px !important;
}

.pa-58 {
  padding: 58px !important;
}

.pl-58 {
  padding-left: 58px !important;
}

.pt-58 {
  padding-top: 58px !important;
}

.pr-58 {
  padding-right: 58px !important;
}

.pb-58 {
  padding-bottom: 58px !important;
}

.pv-58 {
  padding-top: 58px !important;
  padding-bottom: 58px !important;
}

.ph-58 {
  padding-left: 58px !important;
  padding-right: 58px !important;
}

.pa-60 {
  padding: 60px !important;
}

.pl-60 {
  padding-left: 60px !important;
}

.pt-60 {
  padding-top: 60px !important;
}

.pr-60 {
  padding-right: 60px !important;
}

.pb-60 {
  padding-bottom: 60px !important;
}

.pv-60 {
  padding-top: 60px !important;
  padding-bottom: 60px !important;
}

.ph-60 {
  padding-left: 60px !important;
  padding-right: 60px !important;
}

.pa-62 {
  padding: 62px !important;
}

.pl-62 {
  padding-left: 62px !important;
}

.pt-62 {
  padding-top: 62px !important;
}

.pr-62 {
  padding-right: 62px !important;
}

.pb-62 {
  padding-bottom: 62px !important;
}

.pv-62 {
  padding-top: 62px !important;
  padding-bottom: 62px !important;
}

.ph-62 {
  padding-left: 62px !important;
  padding-right: 62px !important;
}

.pa-64 {
  padding: 64px !important;
}

.pl-64 {
  padding-left: 64px !important;
}

.pt-64 {
  padding-top: 64px !important;
}

.pr-64 {
  padding-right: 64px !important;
}

.pb-64 {
  padding-bottom: 64px !important;
}

.pv-64 {
  padding-top: 64px !important;
  padding-bottom: 64px !important;
}

.ph-64 {
  padding-left: 64px !important;
  padding-right: 64px !important;
}

.pa-66 {
  padding: 66px !important;
}

.pl-66 {
  padding-left: 66px !important;
}

.pt-66 {
  padding-top: 66px !important;
}

.pr-66 {
  padding-right: 66px !important;
}

.pb-66 {
  padding-bottom: 66px !important;
}

.pv-66 {
  padding-top: 66px !important;
  padding-bottom: 66px !important;
}

.ph-66 {
  padding-left: 66px !important;
  padding-right: 66px !important;
}

.pa-68 {
  padding: 68px !important;
}

.pl-68 {
  padding-left: 68px !important;
}

.pt-68 {
  padding-top: 68px !important;
}

.pr-68 {
  padding-right: 68px !important;
}

.pb-68 {
  padding-bottom: 68px !important;
}

.pv-68 {
  padding-top: 68px !important;
  padding-bottom: 68px !important;
}

.ph-68 {
  padding-left: 68px !important;
  padding-right: 68px !important;
}

.pa-70 {
  padding: 70px !important;
}

.pl-70 {
  padding-left: 70px !important;
}

.pt-70 {
  padding-top: 70px !important;
}

.pr-70 {
  padding-right: 70px !important;
}

.pb-70 {
  padding-bottom: 70px !important;
}

.pv-70 {
  padding-top: 70px !important;
  padding-bottom: 70px !important;
}

.ph-70 {
  padding-left: 70px !important;
  padding-right: 70px !important;
}

.pa-72 {
  padding: 72px !important;
}

.pl-72 {
  padding-left: 72px !important;
}

.pt-72 {
  padding-top: 72px !important;
}

.pr-72 {
  padding-right: 72px !important;
}

.pb-72 {
  padding-bottom: 72px !important;
}

.pv-72 {
  padding-top: 72px !important;
  padding-bottom: 72px !important;
}

.ph-72 {
  padding-left: 72px !important;
  padding-right: 72px !important;
}

.pa-74 {
  padding: 74px !important;
}

.pl-74 {
  padding-left: 74px !important;
}

.pt-74 {
  padding-top: 74px !important;
}

.pr-74 {
  padding-right: 74px !important;
}

.pb-74 {
  padding-bottom: 74px !important;
}

.pv-74 {
  padding-top: 74px !important;
  padding-bottom: 74px !important;
}

.ph-74 {
  padding-left: 74px !important;
  padding-right: 74px !important;
}

.pa-76 {
  padding: 76px !important;
}

.pl-76 {
  padding-left: 76px !important;
}

.pt-76 {
  padding-top: 76px !important;
}

.pr-76 {
  padding-right: 76px !important;
}

.pb-76 {
  padding-bottom: 76px !important;
}

.pv-76 {
  padding-top: 76px !important;
  padding-bottom: 76px !important;
}

.ph-76 {
  padding-left: 76px !important;
  padding-right: 76px !important;
}

.pa-78 {
  padding: 78px !important;
}

.pl-78 {
  padding-left: 78px !important;
}

.pt-78 {
  padding-top: 78px !important;
}

.pr-78 {
  padding-right: 78px !important;
}

.pb-78 {
  padding-bottom: 78px !important;
}

.pv-78 {
  padding-top: 78px !important;
  padding-bottom: 78px !important;
}

.ph-78 {
  padding-left: 78px !important;
  padding-right: 78px !important;
}

.pa-80 {
  padding: 80px !important;
}

.pl-80 {
  padding-left: 80px !important;
}

.pt-80 {
  padding-top: 80px !important;
}

.pr-80 {
  padding-right: 80px !important;
}

.pb-80 {
  padding-bottom: 80px !important;
}

.pv-80 {
  padding-top: 80px !important;
  padding-bottom: 80px !important;
}

.ph-80 {
  padding-left: 80px !important;
  padding-right: 80px !important;
}

.pa-82 {
  padding: 82px !important;
}

.pl-82 {
  padding-left: 82px !important;
}

.pt-82 {
  padding-top: 82px !important;
}

.pr-82 {
  padding-right: 82px !important;
}

.pb-82 {
  padding-bottom: 82px !important;
}

.pv-82 {
  padding-top: 82px !important;
  padding-bottom: 82px !important;
}

.ph-82 {
  padding-left: 82px !important;
  padding-right: 82px !important;
}

.pa-84 {
  padding: 84px !important;
}

.pl-84 {
  padding-left: 84px !important;
}

.pt-84 {
  padding-top: 84px !important;
}

.pr-84 {
  padding-right: 84px !important;
}

.pb-84 {
  padding-bottom: 84px !important;
}

.pv-84 {
  padding-top: 84px !important;
  padding-bottom: 84px !important;
}

.ph-84 {
  padding-left: 84px !important;
  padding-right: 84px !important;
}

.pa-86 {
  padding: 86px !important;
}

.pl-86 {
  padding-left: 86px !important;
}

.pt-86 {
  padding-top: 86px !important;
}

.pr-86 {
  padding-right: 86px !important;
}

.pb-86 {
  padding-bottom: 86px !important;
}

.pv-86 {
  padding-top: 86px !important;
  padding-bottom: 86px !important;
}

.ph-86 {
  padding-left: 86px !important;
  padding-right: 86px !important;
}

.pa-88 {
  padding: 88px !important;
}

.pl-88 {
  padding-left: 88px !important;
}

.pt-88 {
  padding-top: 88px !important;
}

.pr-88 {
  padding-right: 88px !important;
}

.pb-88 {
  padding-bottom: 88px !important;
}

.pv-88 {
  padding-top: 88px !important;
  padding-bottom: 88px !important;
}

.ph-88 {
  padding-left: 88px !important;
  padding-right: 88px !important;
}

.pa-90 {
  padding: 90px !important;
}

.pl-90 {
  padding-left: 90px !important;
}

.pt-90 {
  padding-top: 90px !important;
}

.pr-90 {
  padding-right: 90px !important;
}

.pb-90 {
  padding-bottom: 90px !important;
}

.pv-90 {
  padding-top: 90px !important;
  padding-bottom: 90px !important;
}

.ph-90 {
  padding-left: 90px !important;
  padding-right: 90px !important;
}

.pa-92 {
  padding: 92px !important;
}

.pl-92 {
  padding-left: 92px !important;
}

.pt-92 {
  padding-top: 92px !important;
}

.pr-92 {
  padding-right: 92px !important;
}

.pb-92 {
  padding-bottom: 92px !important;
}

.pv-92 {
  padding-top: 92px !important;
  padding-bottom: 92px !important;
}

.ph-92 {
  padding-left: 92px !important;
  padding-right: 92px !important;
}

.pa-94 {
  padding: 94px !important;
}

.pl-94 {
  padding-left: 94px !important;
}

.pt-94 {
  padding-top: 94px !important;
}

.pr-94 {
  padding-right: 94px !important;
}

.pb-94 {
  padding-bottom: 94px !important;
}

.pv-94 {
  padding-top: 94px !important;
  padding-bottom: 94px !important;
}

.ph-94 {
  padding-left: 94px !important;
  padding-right: 94px !important;
}

.pa-96 {
  padding: 96px !important;
}

.pl-96 {
  padding-left: 96px !important;
}

.pt-96 {
  padding-top: 96px !important;
}

.pr-96 {
  padding-right: 96px !important;
}

.pb-96 {
  padding-bottom: 96px !important;
}

.pv-96 {
  padding-top: 96px !important;
  padding-bottom: 96px !important;
}

.ph-96 {
  padding-left: 96px !important;
  padding-right: 96px !important;
}

.pa-98 {
  padding: 98px !important;
}

.pl-98 {
  padding-left: 98px !important;
}

.pt-98 {
  padding-top: 98px !important;
}

.pr-98 {
  padding-right: 98px !important;
}

.pb-98 {
  padding-bottom: 98px !important;
}

.pv-98 {
  padding-top: 98px !important;
  padding-bottom: 98px !important;
}

.ph-98 {
  padding-left: 98px !important;
  padding-right: 98px !important;
}

.pa-100 {
  padding: 100px !important;
}

.pl-100 {
  padding-left: 100px !important;
}

.pt-100 {
  padding-top: 100px !important;
}

.pr-100 {
  padding-right: 100px !important;
}

.pb-100 {
  padding-bottom: 100px !important;
}

.pv-100 {
  padding-top: 100px !important;
  padding-bottom: 100px !important;
}

.ph-100 {
  padding-left: 100px !important;
  padding-right: 100px !important;
}

.ma-0 {
  margin: 0px !important;
}

.ml-0 {
  margin-left: 0px !important;
}

.mt-0 {
  margin-top: 0px !important;
}

.mr-0 {
  margin-right: 0px !important;
}

.mb-0 {
  margin-bottom: 0px !important;
}

.mv-0 {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}

.mh-0 {
  margin-left: 0px !important;
  margin-right: 0px !important;
}

.ma-2 {
  margin: 2px !important;
}

.ml-2 {
  margin-left: 2px !important;
}

.mt-2 {
  margin-top: 2px !important;
}

.mr-2 {
  margin-right: 2px !important;
}

.mb-2 {
  margin-bottom: 2px !important;
}

.mv-2 {
  margin-top: 2px !important;
  margin-bottom: 2px !important;
}

.mh-2 {
  margin-left: 2px !important;
  margin-right: 2px !important;
}

.ma-4 {
  margin: 4px !important;
}

.ml-4 {
  margin-left: 4px !important;
}

.mt-4 {
  margin-top: 4px !important;
}

.mr-4 {
  margin-right: 4px !important;
}

.mb-4 {
  margin-bottom: 4px !important;
}

.mv-4 {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
}

.mh-4 {
  margin-left: 4px !important;
  margin-right: 4px !important;
}

.ma-6 {
  margin: 6px !important;
}

.ml-6 {
  margin-left: 6px !important;
}

.mt-6 {
  margin-top: 6px !important;
}

.mr-6 {
  margin-right: 6px !important;
}

.mb-6 {
  margin-bottom: 6px !important;
}

.mv-6 {
  margin-top: 6px !important;
  margin-bottom: 6px !important;
}

.mh-6 {
  margin-left: 6px !important;
  margin-right: 6px !important;
}

.ma-8 {
  margin: 8px !important;
}

.ml-8 {
  margin-left: 8px !important;
}

.mt-8 {
  margin-top: 8px !important;
}

.mr-8 {
  margin-right: 8px !important;
}

.mb-8 {
  margin-bottom: 8px !important;
}

.mv-8 {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

.mh-8 {
  margin-left: 8px !important;
  margin-right: 8px !important;
}

.ma-10 {
  margin: 10px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mv-10 {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}

.mh-10 {
  margin-left: 10px !important;
  margin-right: 10px !important;
}

.ma-12 {
  margin: 12px !important;
}

.ml-12 {
  margin-left: 12px !important;
}

.mt-12 {
  margin-top: 12px !important;
}

.mr-12 {
  margin-right: 12px !important;
}

.mb-12 {
  margin-bottom: 12px !important;
}

.mv-12 {
  margin-top: 12px !important;
  margin-bottom: 12px !important;
}

.mh-12 {
  margin-left: 12px !important;
  margin-right: 12px !important;
}

.ma-14 {
  margin: 14px !important;
}

.ml-14 {
  margin-left: 14px !important;
}

.mt-14 {
  margin-top: 14px !important;
}

.mr-14 {
  margin-right: 14px !important;
}

.mb-14 {
  margin-bottom: 14px !important;
}

.mv-14 {
  margin-top: 14px !important;
  margin-bottom: 14px !important;
}

.mh-14 {
  margin-left: 14px !important;
  margin-right: 14px !important;
}

.ma-16 {
  margin: 16px !important;
}

.ml-16 {
  margin-left: 16px !important;
}

.mt-16 {
  margin-top: 16px !important;
}

.mr-16 {
  margin-right: 16px !important;
}

.mb-16 {
  margin-bottom: 16px !important;
}

.mv-16 {
  margin-top: 16px !important;
  margin-bottom: 16px !important;
}

.mh-16 {
  margin-left: 16px !important;
  margin-right: 16px !important;
}

.ma-18 {
  margin: 18px !important;
}

.ml-18 {
  margin-left: 18px !important;
}

.mt-18 {
  margin-top: 18px !important;
}

.mr-18 {
  margin-right: 18px !important;
}

.mb-18 {
  margin-bottom: 18px !important;
}

.mv-18 {
  margin-top: 18px !important;
  margin-bottom: 18px !important;
}

.mh-18 {
  margin-left: 18px !important;
  margin-right: 18px !important;
}

.ma-20 {
  margin: 20px !important;
}

.ml-20 {
  margin-left: 20px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mr-20 {
  margin-right: 20px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mv-20 {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

.mh-20 {
  margin-left: 20px !important;
  margin-right: 20px !important;
}

.ma-22 {
  margin: 22px !important;
}

.ml-22 {
  margin-left: 22px !important;
}

.mt-22 {
  margin-top: 22px !important;
}

.mr-22 {
  margin-right: 22px !important;
}

.mb-22 {
  margin-bottom: 22px !important;
}

.mv-22 {
  margin-top: 22px !important;
  margin-bottom: 22px !important;
}

.mh-22 {
  margin-left: 22px !important;
  margin-right: 22px !important;
}

.ma-24 {
  margin: 24px !important;
}

.ml-24 {
  margin-left: 24px !important;
}

.mt-24 {
  margin-top: 24px !important;
}

.mr-24 {
  margin-right: 24px !important;
}

.mb-24 {
  margin-bottom: 24px !important;
}

.mv-24 {
  margin-top: 24px !important;
  margin-bottom: 24px !important;
}

.mh-24 {
  margin-left: 24px !important;
  margin-right: 24px !important;
}

.ma-26 {
  margin: 26px !important;
}

.ml-26 {
  margin-left: 26px !important;
}

.mt-26 {
  margin-top: 26px !important;
}

.mr-26 {
  margin-right: 26px !important;
}

.mb-26 {
  margin-bottom: 26px !important;
}

.mv-26 {
  margin-top: 26px !important;
  margin-bottom: 26px !important;
}

.mh-26 {
  margin-left: 26px !important;
  margin-right: 26px !important;
}

.ma-28 {
  margin: 28px !important;
}

.ml-28 {
  margin-left: 28px !important;
}

.mt-28 {
  margin-top: 28px !important;
}

.mr-28 {
  margin-right: 28px !important;
}

.mb-28 {
  margin-bottom: 28px !important;
}

.mv-28 {
  margin-top: 28px !important;
  margin-bottom: 28px !important;
}

.mh-28 {
  margin-left: 28px !important;
  margin-right: 28px !important;
}

.ma-30 {
  margin: 30px !important;
}

.ml-30 {
  margin-left: 30px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mr-30 {
  margin-right: 30px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mv-30 {
  margin-top: 30px !important;
  margin-bottom: 30px !important;
}

.mh-30 {
  margin-left: 30px !important;
  margin-right: 30px !important;
}

.ma-32 {
  margin: 32px !important;
}

.ml-32 {
  margin-left: 32px !important;
}

.mt-32 {
  margin-top: 32px !important;
}

.mr-32 {
  margin-right: 32px !important;
}

.mb-32 {
  margin-bottom: 32px !important;
}

.mv-32 {
  margin-top: 32px !important;
  margin-bottom: 32px !important;
}

.mh-32 {
  margin-left: 32px !important;
  margin-right: 32px !important;
}

.ma-34 {
  margin: 34px !important;
}

.ml-34 {
  margin-left: 34px !important;
}

.mt-34 {
  margin-top: 34px !important;
}

.mr-34 {
  margin-right: 34px !important;
}

.mb-34 {
  margin-bottom: 34px !important;
}

.mv-34 {
  margin-top: 34px !important;
  margin-bottom: 34px !important;
}

.mh-34 {
  margin-left: 34px !important;
  margin-right: 34px !important;
}

.ma-36 {
  margin: 36px !important;
}

.ml-36 {
  margin-left: 36px !important;
}

.mt-36 {
  margin-top: 36px !important;
}

.mr-36 {
  margin-right: 36px !important;
}

.mb-36 {
  margin-bottom: 36px !important;
}

.mv-36 {
  margin-top: 36px !important;
  margin-bottom: 36px !important;
}

.mh-36 {
  margin-left: 36px !important;
  margin-right: 36px !important;
}

.ma-38 {
  margin: 38px !important;
}

.ml-38 {
  margin-left: 38px !important;
}

.mt-38 {
  margin-top: 38px !important;
}

.mr-38 {
  margin-right: 38px !important;
}

.mb-38 {
  margin-bottom: 38px !important;
}

.mv-38 {
  margin-top: 38px !important;
  margin-bottom: 38px !important;
}

.mh-38 {
  margin-left: 38px !important;
  margin-right: 38px !important;
}

.ma-40 {
  margin: 40px !important;
}

.ml-40 {
  margin-left: 40px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

.mr-40 {
  margin-right: 40px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.mv-40 {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}

.mh-40 {
  margin-left: 40px !important;
  margin-right: 40px !important;
}

.ma-42 {
  margin: 42px !important;
}

.ml-42 {
  margin-left: 42px !important;
}

.mt-42 {
  margin-top: 42px !important;
}

.mr-42 {
  margin-right: 42px !important;
}

.mb-42 {
  margin-bottom: 42px !important;
}

.mv-42 {
  margin-top: 42px !important;
  margin-bottom: 42px !important;
}

.mh-42 {
  margin-left: 42px !important;
  margin-right: 42px !important;
}

.ma-44 {
  margin: 44px !important;
}

.ml-44 {
  margin-left: 44px !important;
}

.mt-44 {
  margin-top: 44px !important;
}

.mr-44 {
  margin-right: 44px !important;
}

.mb-44 {
  margin-bottom: 44px !important;
}

.mv-44 {
  margin-top: 44px !important;
  margin-bottom: 44px !important;
}

.mh-44 {
  margin-left: 44px !important;
  margin-right: 44px !important;
}

.ma-46 {
  margin: 46px !important;
}

.ml-46 {
  margin-left: 46px !important;
}

.mt-46 {
  margin-top: 46px !important;
}

.mr-46 {
  margin-right: 46px !important;
}

.mb-46 {
  margin-bottom: 46px !important;
}

.mv-46 {
  margin-top: 46px !important;
  margin-bottom: 46px !important;
}

.mh-46 {
  margin-left: 46px !important;
  margin-right: 46px !important;
}

.ma-48 {
  margin: 48px !important;
}

.ml-48 {
  margin-left: 48px !important;
}

.mt-48 {
  margin-top: 48px !important;
}

.mr-48 {
  margin-right: 48px !important;
}

.mb-48 {
  margin-bottom: 48px !important;
}

.mv-48 {
  margin-top: 48px !important;
  margin-bottom: 48px !important;
}

.mh-48 {
  margin-left: 48px !important;
  margin-right: 48px !important;
}

.ma-50 {
  margin: 50px !important;
}

.ml-50 {
  margin-left: 50px !important;
}

.mt-50 {
  margin-top: 50px !important;
}

.mr-50 {
  margin-right: 50px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.mv-50 {
  margin-top: 50px !important;
  margin-bottom: 50px !important;
}

.mh-50 {
  margin-left: 50px !important;
  margin-right: 50px !important;
}

.ma-52 {
  margin: 52px !important;
}

.ml-52 {
  margin-left: 52px !important;
}

.mt-52 {
  margin-top: 52px !important;
}

.mr-52 {
  margin-right: 52px !important;
}

.mb-52 {
  margin-bottom: 52px !important;
}

.mv-52 {
  margin-top: 52px !important;
  margin-bottom: 52px !important;
}

.mh-52 {
  margin-left: 52px !important;
  margin-right: 52px !important;
}

.ma-54 {
  margin: 54px !important;
}

.ml-54 {
  margin-left: 54px !important;
}

.mt-54 {
  margin-top: 54px !important;
}

.mr-54 {
  margin-right: 54px !important;
}

.mb-54 {
  margin-bottom: 54px !important;
}

.mv-54 {
  margin-top: 54px !important;
  margin-bottom: 54px !important;
}

.mh-54 {
  margin-left: 54px !important;
  margin-right: 54px !important;
}

.ma-56 {
  margin: 56px !important;
}

.ml-56 {
  margin-left: 56px !important;
}

.mt-56 {
  margin-top: 56px !important;
}

.mr-56 {
  margin-right: 56px !important;
}

.mb-56 {
  margin-bottom: 56px !important;
}

.mv-56 {
  margin-top: 56px !important;
  margin-bottom: 56px !important;
}

.mh-56 {
  margin-left: 56px !important;
  margin-right: 56px !important;
}

.ma-58 {
  margin: 58px !important;
}

.ml-58 {
  margin-left: 58px !important;
}

.mt-58 {
  margin-top: 58px !important;
}

.mr-58 {
  margin-right: 58px !important;
}

.mb-58 {
  margin-bottom: 58px !important;
}

.mv-58 {
  margin-top: 58px !important;
  margin-bottom: 58px !important;
}

.mh-58 {
  margin-left: 58px !important;
  margin-right: 58px !important;
}

.ma-60 {
  margin: 60px !important;
}

.ml-60 {
  margin-left: 60px !important;
}

.mt-60 {
  margin-top: 60px !important;
}

.mr-60 {
  margin-right: 60px !important;
}

.mb-60 {
  margin-bottom: 60px !important;
}

.mv-60 {
  margin-top: 60px !important;
  margin-bottom: 60px !important;
}

.mh-60 {
  margin-left: 60px !important;
  margin-right: 60px !important;
}

.ma-62 {
  margin: 62px !important;
}

.ml-62 {
  margin-left: 62px !important;
}

.mt-62 {
  margin-top: 62px !important;
}

.mr-62 {
  margin-right: 62px !important;
}

.mb-62 {
  margin-bottom: 62px !important;
}

.mv-62 {
  margin-top: 62px !important;
  margin-bottom: 62px !important;
}

.mh-62 {
  margin-left: 62px !important;
  margin-right: 62px !important;
}

.ma-64 {
  margin: 64px !important;
}

.ml-64 {
  margin-left: 64px !important;
}

.mt-64 {
  margin-top: 64px !important;
}

.mr-64 {
  margin-right: 64px !important;
}

.mb-64 {
  margin-bottom: 64px !important;
}

.mv-64 {
  margin-top: 64px !important;
  margin-bottom: 64px !important;
}

.mh-64 {
  margin-left: 64px !important;
  margin-right: 64px !important;
}

.ma-66 {
  margin: 66px !important;
}

.ml-66 {
  margin-left: 66px !important;
}

.mt-66 {
  margin-top: 66px !important;
}

.mr-66 {
  margin-right: 66px !important;
}

.mb-66 {
  margin-bottom: 66px !important;
}

.mv-66 {
  margin-top: 66px !important;
  margin-bottom: 66px !important;
}

.mh-66 {
  margin-left: 66px !important;
  margin-right: 66px !important;
}

.ma-68 {
  margin: 68px !important;
}

.ml-68 {
  margin-left: 68px !important;
}

.mt-68 {
  margin-top: 68px !important;
}

.mr-68 {
  margin-right: 68px !important;
}

.mb-68 {
  margin-bottom: 68px !important;
}

.mv-68 {
  margin-top: 68px !important;
  margin-bottom: 68px !important;
}

.mh-68 {
  margin-left: 68px !important;
  margin-right: 68px !important;
}

.ma-70 {
  margin: 70px !important;
}

.ml-70 {
  margin-left: 70px !important;
}

.mt-70 {
  margin-top: 70px !important;
}

.mr-70 {
  margin-right: 70px !important;
}

.mb-70 {
  margin-bottom: 70px !important;
}

.mv-70 {
  margin-top: 70px !important;
  margin-bottom: 70px !important;
}

.mh-70 {
  margin-left: 70px !important;
  margin-right: 70px !important;
}

.ma-72 {
  margin: 72px !important;
}

.ml-72 {
  margin-left: 72px !important;
}

.mt-72 {
  margin-top: 72px !important;
}

.mr-72 {
  margin-right: 72px !important;
}

.mb-72 {
  margin-bottom: 72px !important;
}

.mv-72 {
  margin-top: 72px !important;
  margin-bottom: 72px !important;
}

.mh-72 {
  margin-left: 72px !important;
  margin-right: 72px !important;
}

.ma-74 {
  margin: 74px !important;
}

.ml-74 {
  margin-left: 74px !important;
}

.mt-74 {
  margin-top: 74px !important;
}

.mr-74 {
  margin-right: 74px !important;
}

.mb-74 {
  margin-bottom: 74px !important;
}

.mv-74 {
  margin-top: 74px !important;
  margin-bottom: 74px !important;
}

.mh-74 {
  margin-left: 74px !important;
  margin-right: 74px !important;
}

.ma-76 {
  margin: 76px !important;
}

.ml-76 {
  margin-left: 76px !important;
}

.mt-76 {
  margin-top: 76px !important;
}

.mr-76 {
  margin-right: 76px !important;
}

.mb-76 {
  margin-bottom: 76px !important;
}

.mv-76 {
  margin-top: 76px !important;
  margin-bottom: 76px !important;
}

.mh-76 {
  margin-left: 76px !important;
  margin-right: 76px !important;
}

.ma-78 {
  margin: 78px !important;
}

.ml-78 {
  margin-left: 78px !important;
}

.mt-78 {
  margin-top: 78px !important;
}

.mr-78 {
  margin-right: 78px !important;
}

.mb-78 {
  margin-bottom: 78px !important;
}

.mv-78 {
  margin-top: 78px !important;
  margin-bottom: 78px !important;
}

.mh-78 {
  margin-left: 78px !important;
  margin-right: 78px !important;
}

.ma-80 {
  margin: 80px !important;
}

.ml-80 {
  margin-left: 80px !important;
}

.mt-80 {
  margin-top: 80px !important;
}

.mr-80 {
  margin-right: 80px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.mv-80 {
  margin-top: 80px !important;
  margin-bottom: 80px !important;
}

.mh-80 {
  margin-left: 80px !important;
  margin-right: 80px !important;
}

.ma-82 {
  margin: 82px !important;
}

.ml-82 {
  margin-left: 82px !important;
}

.mt-82 {
  margin-top: 82px !important;
}

.mr-82 {
  margin-right: 82px !important;
}

.mb-82 {
  margin-bottom: 82px !important;
}

.mv-82 {
  margin-top: 82px !important;
  margin-bottom: 82px !important;
}

.mh-82 {
  margin-left: 82px !important;
  margin-right: 82px !important;
}

.ma-84 {
  margin: 84px !important;
}

.ml-84 {
  margin-left: 84px !important;
}

.mt-84 {
  margin-top: 84px !important;
}

.mr-84 {
  margin-right: 84px !important;
}

.mb-84 {
  margin-bottom: 84px !important;
}

.mv-84 {
  margin-top: 84px !important;
  margin-bottom: 84px !important;
}

.mh-84 {
  margin-left: 84px !important;
  margin-right: 84px !important;
}

.ma-86 {
  margin: 86px !important;
}

.ml-86 {
  margin-left: 86px !important;
}

.mt-86 {
  margin-top: 86px !important;
}

.mr-86 {
  margin-right: 86px !important;
}

.mb-86 {
  margin-bottom: 86px !important;
}

.mv-86 {
  margin-top: 86px !important;
  margin-bottom: 86px !important;
}

.mh-86 {
  margin-left: 86px !important;
  margin-right: 86px !important;
}

.ma-88 {
  margin: 88px !important;
}

.ml-88 {
  margin-left: 88px !important;
}

.mt-88 {
  margin-top: 88px !important;
}

.mr-88 {
  margin-right: 88px !important;
}

.mb-88 {
  margin-bottom: 88px !important;
}

.mv-88 {
  margin-top: 88px !important;
  margin-bottom: 88px !important;
}

.mh-88 {
  margin-left: 88px !important;
  margin-right: 88px !important;
}

.ma-90 {
  margin: 90px !important;
}

.ml-90 {
  margin-left: 90px !important;
}

.mt-90 {
  margin-top: 90px !important;
}

.mr-90 {
  margin-right: 90px !important;
}

.mb-90 {
  margin-bottom: 90px !important;
}

.mv-90 {
  margin-top: 90px !important;
  margin-bottom: 90px !important;
}

.mh-90 {
  margin-left: 90px !important;
  margin-right: 90px !important;
}

.ma-92 {
  margin: 92px !important;
}

.ml-92 {
  margin-left: 92px !important;
}

.mt-92 {
  margin-top: 92px !important;
}

.mr-92 {
  margin-right: 92px !important;
}

.mb-92 {
  margin-bottom: 92px !important;
}

.mv-92 {
  margin-top: 92px !important;
  margin-bottom: 92px !important;
}

.mh-92 {
  margin-left: 92px !important;
  margin-right: 92px !important;
}

.ma-94 {
  margin: 94px !important;
}

.ml-94 {
  margin-left: 94px !important;
}

.mt-94 {
  margin-top: 94px !important;
}

.mr-94 {
  margin-right: 94px !important;
}

.mb-94 {
  margin-bottom: 94px !important;
}

.mv-94 {
  margin-top: 94px !important;
  margin-bottom: 94px !important;
}

.mh-94 {
  margin-left: 94px !important;
  margin-right: 94px !important;
}

.ma-96 {
  margin: 96px !important;
}

.ml-96 {
  margin-left: 96px !important;
}

.mt-96 {
  margin-top: 96px !important;
}

.mr-96 {
  margin-right: 96px !important;
}

.mb-96 {
  margin-bottom: 96px !important;
}

.mv-96 {
  margin-top: 96px !important;
  margin-bottom: 96px !important;
}

.mh-96 {
  margin-left: 96px !important;
  margin-right: 96px !important;
}

.ma-98 {
  margin: 98px !important;
}

.ml-98 {
  margin-left: 98px !important;
}

.mt-98 {
  margin-top: 98px !important;
}

.mr-98 {
  margin-right: 98px !important;
}

.mb-98 {
  margin-bottom: 98px !important;
}

.mv-98 {
  margin-top: 98px !important;
  margin-bottom: 98px !important;
}

.mh-98 {
  margin-left: 98px !important;
  margin-right: 98px !important;
}

.ma-100 {
  margin: 100px !important;
}

.ml-100 {
  margin-left: 100px !important;
}

.mt-100 {
  margin-top: 100px !important;
}

.mr-100 {
  margin-right: 100px !important;
}

.mb-100 {
  margin-bottom: 100px !important;
}

.mv-100 {
  margin-top: 100px !important;
  margin-bottom: 100px !important;
}

.mh-100 {
  margin-left: 100px !important;
  margin-right: 100px !important;
}

/*
    1)
      Font Name: Lexend
      Font URI: https://fonts.google.com/specimen/Lexend

    2)
      Font Name: Inter
      Font URI: https://fonts.google.com/specimen/Inter

    3)
      Font Name: Lexend
      Font URI: https://fonts.google.com/specimen/Poppins
*/
.default-theme {
  /* text color */
  --ot-text-primary: #6f767e;
  --ot-text-title: #1a1d1f;
  --ot-text-subtitle: #33383f;
  /* background color */
  --ot-bg-primary: #F0F3F5;
  --ot-bg-secondary: #ffffff;
  --ot-bg-secondary-opacity: rgba(255, 255, 255, 0.35);
  --header-bg: #EDFBFF;
  --ot-bg-tertiary: #ffffff;
  /* border color */
  --ot-border-primary: #eaeaea;
  /* badge background color */
  --ot-bg-badge-success: #29d697;
  --ot-bg-badge-danger: #ff6a54;
  --ot-bg-badge-warning: #fdc400;
  --ot-bg-badge-primary: #1890ff;
  /* badge text color */
  --ot-text-badge-success: #ffffff;
  --ot-text-badge-danger: #ffffff;
  --ot-text-badge-warning: #ffffff;
  --ot-text-badge-primary: #ffffff;
  /* badge light background color */
  --ot-bg-badge-light-success: #e9faf4;
  --ot-bg-badge-light-danger: #fff0ed;
  --ot-bg-badge-light-warning: #fef9e5;
  --ot-bg-badge-light-primary: #e6f2fd;
  /* badge light text color */
  --ot-text-badge-light-success: #29d697;
  --ot-text-badge-light-danger: #ff6a54;
  --ot-text-badge-light-warning: #fdc400;
  --ot-text-badge-light-primary: #1890ff;
  /* table color */
  --ot-bg-table-card: #ffffff;
  --ot-border-table-card: #ffffff;
  --ot-bg-table-card-header: #ffffff;
  --ot-border-table-card-header: #eaeaea;
  --ot-border-table: #eaeaea;
  --ot-bg-table-thead: #f7fafc;
  --ot-border-table-thead: #eaeaea;
  --ot-bg-table-tbody: #f7fafc;
  --ot-border-basic-table: #eaeaea;
  --ot-bg-table-basic-thead: #f7f7f7;
  --ot-border-basic-table-thead: #eaeaea;
  --ot-bg-basic-table-tbody: #f7f7f7;
  --ot-bg-table-checkbox: #ffffff;
  --ot-border-table-checkbox: #eaeaea;
  --ot-color-table-icon-sorting-asc-up: #1a1d1f;
  --ot-color-table-icon-sorting-asc-down: #bebfc0;
  --ot-color-table-icon-sorting-desc-up: #bebfc0;
  --ot-color-table-icon-sorting-desc-down: #1a1d1f;
  --ot-bg-table-toolbar-per-page: #ffffff;
  --ot-border-table-toolbar-per-page: #eaeaea;
  --ot-bg-table-toolbar-btn-outline-primary: #ffffff;
  --ot-bg-table-toolbar-search: #ffffff;
  --ot-border-table-toolbar-search: #eaeaea;
  --ot-bg-table-toolbar-btn-action: #fcfcfc;
  --ot-border-table-toolbar-btn-action: #fcfcfc;
  /* table pagination */
  --ot-bg-table-pagination: #ffffff;
  --ot-text-table-pagination: #1a1d1f;
  --ot-border-table-pagination: #eaeaea;
  /* profile */
  --ot-bg-profile-menu: #fcfcfc;
  --ot-bg-profile-body: #fcfcfc;
  --ot-bg-profile-mobile-menu: #fcfcfc;
  /* Timeline */
  --ot-dot-timeline: #ff6b6b;
  /* priging table header background */
  --ot-bg-pricing-table-gradient: linear-gradient(#a6e7fd 0%, #e7f8ff 100%);
  /* hover background color */
  --ot-bg-pricing-table-hover: #b5ebff;
}

.dark-theme {
  /* text color */
  --ot-text-primary: #6f767e;
  --ot-text-title: #ffffff;
  --ot-text-subtitle: #6f767e;
  /* background color */
  --ot-bg-primary: #111415;
  --ot-bg-secondary: #1a1d20;
  --ot-bg-secondary-opacity: #1a1d20;
  --header-bg: #1a1d20;
  --ot-bg-tertiary: #1f2124;
  /* border color */
  --ot-border-primary: #272b30;
  /* badge background color */
  --ot-bg-badge-success: rgba(41, 214, 151, 0.1);
  --ot-bg-badge-danger: rgba(255, 106, 84, 0.1);
  --ot-bg-badge-warning: rgba(253, 196, 0, 0.1);
  --ot-bg-badge-primary: rgba(24, 144, 255, 0.1);
  /* badge text color */
  --ot-text-badge-success: #29d697;
  --ot-text-badge-danger: #ff6a54;
  --ot-text-badge-warning: #fdc400;
  --ot-text-badge-primary: #1890ff;
  /* badge light background color */
  --ot-bg-badge-light-success: rgba(233, 250, 244, 0.1);
  --ot-bg-badge-light-danger: rgba(255, 240, 237, 0.1);
  --ot-bg-badge-light-warning: rgba(254, 249, 229, 0.1);
  --ot-bg-badge-light-primary: rgba(230, 242, 253, 0.1);
  /* badge light text color */
  --ot-text-badge-light-success: #29d697;
  --ot-text-badge-light-danger: #ff6a54;
  --ot-text-badge-light-warning: #fdc400;
  --ot-text-badge-light-primary: #1890ff;
  /* table color */
  --ot-bg-table-card: #1a1d20;
  --ot-border-table-card: #ffffff;
  --ot-bg-table-card-header: #1a1d20;
  --ot-border-table-card-header: transparent;
  --ot-border-table: transparent;
  --ot-bg-table-thead: #1f2124;
  --ot-border-table-thead: #1f2124;
  --ot-bg-table-tbody: #1f2124;
  --ot-border-basic-table: transparent;
  --ot-bg-table-basic-thead: #1f2124;
  --ot-border-basic-table-thead: #1f2124;
  --ot-bg-basic-table-tbody: #1f2124;
  --ot-bg-table-checkbox: #1a1d20;
  --ot-border-table-checkbox: #272b30;
  --ot-color-table-icon-sorting-asc-up: rgba(190, 191, 192, 0.1);
  --ot-color-table-icon-sorting-asc-down: #bebfc0;
  --ot-color-table-icon-sorting-desc-up: #bebfc0;
  --ot-color-table-icon-sorting-desc-down: rgba(190, 191, 192, 0.1);
  --ot-bg-table-toolbar-per-page: #1f2124;
  --ot-border-table-toolbar-per-page: #272b30;
  --ot-bg-table-toolbar-btn-outline-primary: #1a1d20;
  --ot-bg-table-toolbar-search: #1f2124;
  --ot-border-table-toolbar-search: #272b30;
  --ot-bg-table-toolbar-btn-action: #1f2124;
  --ot-border-table-toolbar-btn-action: #272b30;
  /* table pagination */
  --ot-bg-table-pagination: #1a1d20;
  --ot-text-table-pagination: #ffffff;
  --ot-border-table-pagination: #272b30;
  /* profile */
  --ot-bg-profile-menu: var(--ot-bg-secondary);
  --ot-bg-profile-body: var(--ot-bg-secondary);
  --ot-bg-profile-mobile-menu: var(--ot-bg-secondary);
  /* Timeline */
  --ot-dot-timeline: #ffabab;
  /* priging table header background */
  --ot-bg-pricing-table-gradient: linear-gradient(#151515 0%, #212121 100%);
  /* hover background color */
  --ot-bg-pricing-table-hover: #2a2a2a;
}

/* Global text color */
/* Global bg color */
/* border color */
/* Global badge background color */
/* Global badge text color */
/* Global badge light background color */
/* Global badge light text color */
/* Global table color */
/* Global table pagination */
/* profile */
/* Timeline */
/* priging table header background */
/* hover background color */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  box-sizing: border-box;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: "Lexend", sans-serif !important;
  background:#edfbff;
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
}

body.dark-theme {
  background: none;
  background-color: var(--ot-bg-primary);
}

/* Heading Text */
h6,
h5,
h4,
h3,
h2,
h1, strong {
  font-family: "Lexend", sans-serif !important;
  color: var(--ot-text-title);
}

/* Heading Text */
p,
a,
label, small {
  color: var(--ot-text-subtitle);
}

a {
  text-decoration: none;
}

/*
    Base components are imported from the base folder.

    Index
    -----

    1) Scrollbar
    2) Topbar
    3) Topbar Dropdown Menu
    4) Profile Expand
    5) Sidebar
    6) Theme Switch
    7) Main Content
    8) Common Card
    9) Summery Card
    10) Charts
    11) Common Buttons
    11) Tables
    12) Accordion
    13) Signup, Signin
    14) Error Page
    15) Card
    16) Modal
    17) Alert
    18) Color Template
    19) Dropdown
    20) Badges
    21) Global Components
    22) Paginations
    23) Inputs
    24) Breadcrumb
    25) Email Template
    26) Notification
    27) Form Elements
    28) FAQ
    29) FAQ Classic
    30) Pricing Table, 2, 3, 4
    31) Icon page

    30) eCommerce Components
    31) Chat
    32) Carousel
    33) Timeline
    34) Invoice
    35) Footer
*/
/* Scrollbar */
/* width */
::-webkit-scrollbar {
  width: 8px;
  border-radius: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: rgba(35, 180, 220, 0.1) !important;
  border-radius: 8px;
  background-clip: padding-box;
}

/* Topbar */
/*Changes while RTL Start*/
body.rtl .header {
  right: 256px;
  left: 0;
  transition: -webkit- right 0.4s;
  transition: right 0.4s;
  transition: right 0.4s, -webkit- right 0.4s;
}

@media (max-width: 768px) {
  body.rtl .header {
    padding: 10px;
    right: 0;
  }
}

body.rtl .header .header-search .search-icon {
  right: auto;
  left: 16px !important;
}

body.rtl .header .header-controls .header-control-item {
  margin-left: 38px;
  margin-right: auto;
}

body.rtl .header .header-controls .header-control-item:last-child {
  margin-left: 0;
}

body.rtl .half-expand-toggle.sidebar-toggle img {
  transform: rotate(180deg);
}

body.rtl .half-expand .header {
  z-index: 22;
  right: 100px;
  transition: -webkit- right 0.4s;
  transition: right 0.4s;
  transition: right 0.4s, -webkit- right 0.4s;
}

body.rtl .half-expand .half-expand-toggle.sidebar-toggle img {
  transform: rotate(0deg) !important;
}

body.rtl .half-expand-toggle .sidebar-toggle img {
  transform: rotate(180deg);
}

body.rtl .profile-expand-list {
  float: right;
  align-items: baseline;
  width: 100%;
}

body.rtl .profile-expand-list .profile-expand-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
}

body.rtl .item-content .notification,
body.rtl .item-content .message {
  text-align: right;
}

body.rtl .item-avater .item-badge {
  right: auto !important;
  left: 0 !important;
}

body.rtl .topbar-dropdown-menu h1 {
  float: right;
  padding-right: 20px;
  padding-left: 0;
}

body.rtl .language-list h5,
body.rtl .currency-list h5 {
  float: right;
}

/*Changes while RTL End*/
/* Dark Theme Style start*/
.dark-theme .header {
  border-bottom: none;
}

/* Dark Theme Style end*/
.header {
  position: fixed;
  top: 0;
  left: 256px;
  right: 0;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 17px 32px;
  background: var(--header-bg);
  border-bottom: 2px solid #fff;
  transition: -webkit- left 0.4s;
  transition: left 0.4s;
  transition: left 0.4s, -webkit- left 0.4s;
}

.header.on-scroll {
  background: var(--ot-bg-secondary);
}

.default-theme .header.on-scroll {
  background: #edfbff;
}

@media (max-width: 768px) {
  .header {
    width: 100%;
    padding: 10px;
    left: 0;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }
}

.header button {
  border: 0;
  background: transparent;
  outline: none;
}

.header .dropdown .dropdown-menu {
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  max-height: 0;
  display: block;
  overflow: hidden;
  opacity: 0;
}

.header .close-toggle {
  display: none;
  transform: rotate(180deg);
}

.header .header-search {
  position: relative;
  width: 310px;
}

.header .header-search .search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.header .header-search .search-field {
  width: 100%;
  padding: 12px 32px 12px 16px;
  border-radius: 5px;
  border: none;
  background-color: var(--ot-bg-primary);
}

.default-theme .header .header-search .search-field {
  background-color: var(--ot-bg-secondary);
}

.header .header-search .search-field:focus-visible {
  outline-color: #eef0ff;
}

.header .header-controls {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.header .header-controls .header-control-item {
  margin-right: 38px;
}

.header .header-controls .header-control-item:last-child {
  margin-right: 0;
}

.header .header-controls .header-control-item .item-content button span {
  font-size: 14px;
}

.header .header-controls .header-control-item .item-content button span.language-change {
  display: flex;
  align-items: center;
  justify-content: center;
}

.header .header-controls .header-control-item .item-content button.profile-navigate {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header .header-controls .header-control-item .item-content button.profile-navigate.show {
  display: flex !important;
}

.header .header-controls .header-control-item .item-content button.profile-navigate .profile-photo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.header .header-controls .header-control-item .item-content button.profile-navigate .profile-photo img {
  width: 100%;
  height: auto;
  border-radius: 50%;
}

.header .header-controls .header-control-item .item-content button.profile-navigate .profile-info {
  display: flex;
  flex-direction: column;
  align-items: baseline;
}

.header .header-controls .header-control-item .item-content button.profile-navigate .profile-info .user-name {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  margin: 0;
  color: var(--ot-text-title);
}

.header .header-controls .header-control-item .item-content button.profile-navigate .profile-info .designation {
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  margin: 0;
  color: var(--ot-text-primary);
}

.header .header-controls .header-control-item .item-content button.icon {
  width: 24px;
  height: 24px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-btn {
  gap: 12px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-btn h6,
.header .header-controls .header-control-item .language-currceny-container .language-currency-btn span {
  color: var(--ot-text-primary);
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 0;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-btn h6 i,
.header .header-controls .header-control-item .language-currceny-container .language-currency-btn span i {
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-btn.show span i {
  transform: rotate(180deg);
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-btn .vertical-devider {
  height: 10px;
  width: 2px;
  background-color: #d1f2ff;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown {
  width: 392px;
  padding: 28px;
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  color: var(--ot-text-primary);
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown h5 {
  color: var(--ot-text-primary);
  font-size: 16px;
  margin-bottom: 12px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-hidden {
  display: none;
  visibility: hidden;
  padding-right: 10px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select {
  cursor: pointer;
  display: inline-block;
  position: relative;
  font-size: 16px;
  color: var(--ot-text-primary);
  width: 100%;
  height: 48px;
  border: 1px solid #eaeaea;
  border-radius: 5px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-styled {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: white;
  padding: 8px 15px;
  -moz-transition: all 0.2s ease-in;
  -o-transition: all 0.2s ease-in;
  -webkit-transition: all 0.2s ease-in;
  transition: all 0.2s ease-in;
  display: flex;
  align-items: center;
  gap: 15px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-styled:after {
  content: "";
  width: 0;
  height: 0;
  border: 7px solid transparent;
  border-color: #fff transparent transparent transparent;
  position: absolute;
  top: 16px;
  right: 10px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-styled:active,
.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-styled.active {
  box-shadow: 0px 0px 10px rgba(86, 105, 255, 0.35);
  border-radius: 5px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-styled:active:after,
.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-styled.active:after {
  top: 9px;
  border-color: transparent transparent #fff transparent;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-options {
  display: none;
  z-index: 999;
  margin: 0;
  padding: 0;
  list-style: none;
  background-color: #fff;
  border: 1px solid #eaeaea;
  border-radius: 5px;
  margin-top: 55px;
  padding: 20px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-options li {
  margin: 0;
  padding-bottom: 16px;
  -moz-transition: all 0.15s ease-in;
  -o-transition: all 0.15s ease-in;
  -webkit-transition: all 0.15s ease-in;
  transition: all 0.15s ease-in;
  display: flex;
  align-items: center;
  gap: 15px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-options li:last-child {
  padding-bottom: 0;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-options li img {
  width: 30px;
  height: 18px;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-options li:hover,
.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-options li.is-selected {
  color: #6f767e;
  background: #fff;
}

.header .header-controls .header-control-item .language-currceny-container .language-currency-dropdown .select-options li[rel="hide"] {
  display: none;
}

.half-expand .header {
  z-index: 22;
  left: 100px;
  transition: -webkit- left 0.4s;
  transition: left 0.4s;
  transition: left 0.4s, -webkit- left 0.4s;
}

@media (max-width: 768px) {
  .close-toggle {
    display: block !important;
  }
}

@media (max-width: 768px) {

  .md-none,
  .header-search {
    display: none !important;
  }
}

/* Topbar Dropdown Menu */
.topbar-dropdown-menu {
  max-width: 425px;
  /* componet global styling start */
  /* Componet global styling end */
}

.topbar-dropdown-menu h1 {
  font-size: 20px;
  line-height: 32px;
  padding-left: 20px;
  font-weight: 600;
  color: var(--ot-text-title);
}

.topbar-dropdown-menu .topbar-dropdown-content :last-child::before {
  content: none !important;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item {
  padding: 0px;
  padding: 20px;
  border-radius: 5px;
  position: relative;
  white-space: normal;
  gap: 1rem;
  /* dropdown item avater start */
  /* dropdown item avater end */
  /* dropdown item content start */
  /* dropdown item content end */
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item:hover {
  background-color: var(--ot-bg-primary);
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item::before {
  content: "";
  position: absolute;
  bottom: 0;
  height: 1px;
  background: #efefef;
  left: 12px;
  right: 12px;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item:active {
  color: #1e2125;
  background-color: #e9ecef;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-avater {
  min-width: 50px;
  min-height: 50px;
  max-width: 50px;
  max-height: 50px;
  border-radius: 50%;
  position: relative;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-avater img {
  width: 100%;
  height: 100%;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-avater .item-badge {
  position: absolute;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #6f767e;
  width: 10px;
  height: 10px;
  max-width: 10px;
  max-height: 10px;
  border-radius: 50%;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-avater .item-badge.active {
  background-color: #24c087;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-avater .item-badge.item-icon-badge {
  width: 18px;
  height: 18px;
  min-width: 18px;
  min-height: 18px;
  padding: 10px;
  background-color: #645CBB;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-avater .online-status-badge {
  width: 12px;
  height: 12px;
  border: 1px solid #fff;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-content h6 {
  font-size: 14px;
  line-height: 18px;
  color: var(--ot-text-title);
  font-weight: 600;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-content h6 span {
  color: var(--ot-text-primary);
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-content h6.message {
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-weight: 700;
  line-height: 15px;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-content h6.message span {
  font-size: 12px;
  font-weight: 400;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-status {
  color: var(--ot-text-primary);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-status .status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  min-width: 6px;
  min-height: 6px;
  background-color: #6f767e;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item .item-status .status-dot.active {
  background: #645CBB;
}

.topbar-dropdown-menu .topbar-dropdown-footer {
  background-color: #4d5ee5;
  padding: 12px;
  border-radius: 5px;
  margin-top: 20px;
  text-align: center;
}

.topbar-dropdown-menu .topbar-dropdown-footer a {
  color: #fff;
}

.topbar-dropdown-menu .topbar-dropdown-footer:hover {
  background-color: #645CBB;
}

/* Profile Expand */
.profile-expand-dropdown {
  width: 265px;
  max-width: 265px;
}

.profile-expand-dropdown .profile-expand-container .profile-expand-list {
  gap: 20px;
  padding: 20px;
}

.profile-expand-dropdown .profile-expand-container .profile-expand-list .profile-expand-item {
  position: relative;
  text-decoration: none;
}

.profile-expand-dropdown .profile-expand-container .profile-expand-list .profile-expand-item.divider {
  margin-bottom: 20px;
}

.profile-expand-dropdown .profile-expand-container .profile-expand-list .profile-expand-item.divider::after {
  content: "";
  position: absolute;
  bottom: 0;
  height: 1px;
  background: #efefef;
  left: 4px;
  right: 4px;
  height: 2px;
  top: 50px;
}

.profile-expand-dropdown .profile-expand-container .profile-expand-list .profile-expand-item span {
  font-weight: 500;
  font-size: 15px;
  line-height: 28px;
  color: var(--ot-text-primary);
}

.profile-expand-dropdown .profile-expand-container .profile-expand-list .profile-expand-item:hover span {
  color: var(--ot-text-title);
}

.profile-expand-dropdown .profile-expand-container .profile-expand-list .profile-expand-item img {
  width: 22px;
  height: 22px;
  margin-right: 10px;
}

/* Sidebar */
/*
    sidebarn
*/
/*Changes while RTL Start*/
body.rtl .sidebar {
  right: 0;
}

@media (max-width: 768px) {
  body.rtl .sidebar {
    right: -100%;
    transition: right 0.3s;
  }
}

body.rtl .sidebar .sidebar-menu {
  height: calc(100vh - 60px);
  overflow-y: scroll;
  overflow-x: none;
  /* Default State reboot*/
}

body.rtl .sidebar .sidebar-menu ol,
body.rtl .sidebar .sidebar-menu ul {
  padding-right: 0;
  margin-right: 0;
}

body.rtl .sidebar .sidebar-menu ol a,
body.rtl .sidebar .sidebar-menu ul a {
  text-decoration: none;
}

body.rtl .sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-section-heading {
  margin-right: 17px;
}

body.rtl .sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item>ul>li>a {
  padding-right: 47px;
}

body.rtl .sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item>ul>li>ul>li>a {
  padding-right: 64px;
}

body.rtl .sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a {
  padding: 15px 25px 15px 0px;
}

body.rtl .sidebar .sidebar-menu .sidebar-menu-section .mm-active>a {
  border-left: none;
  border-right: 2px solid var(--primary-color);
}

body.rtl .sidebar .sidebar-menu .sidebar-menu-section ul>li>a {
  padding-right: 28px;
}

body.rtl .sidebar-expand .sidebar {
  right: 0;
  transition: right 0.3s;
}

/*Changes while RTL END*/
/* Dark Theme Style start*/
.dark-theme .sidebar {
  border-right: none;
}

/* Dark Theme Style end*/
/* sidebar default design */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 300;
  display: flex;
  flex-direction: column;
  width: 256px;
  background-color: var(--ot-bg-secondary-opacity);
  border-right: 2px solid #fff;
  transition: -webkit- width 0.4s;
  transition: width 0.4s;
  transition: width 0.4s, -webkit- width 0.4s;
  /* Responsive Sidebar */
  /* Sidebar Header */
  /* Sidebar Menu */
}

.sidebar .close-toggle {
  display: none;
}

@media (max-width: 768px) {
  .sidebar .close-toggle {
    display: block;
  }
}

.sidebar .half-expand-toggle {
  display: block;
}

@media (max-width: 768px) {
  .sidebar .half-expand-toggle {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar {
    z-index: 30;
    background-color: var(--ot-bg-secondary);
    z-index: 30;
    left: -100%;
    transition: -webkit-left 0.3s;
    transition: left 0.3s;
    transition: left 0.3s, -webkit-left 0.3s;
  }
}

.sidebar .sidebar-header {
  padding: 28px 14px;
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  /* Sidebar Logo */
  /* Sidebar Half Expand Icon*/
}

.sidebar .sidebar-header .sidebar-logo .full-logo {
  opacity: 1;
  visibility: visible;
  height: auto;
  transition: -webkit- opacity 0.4s;
  transition: opacity 0.4s;
  transition: opacity 0.4s, -webkit- opacity 0.4s;
}

.sidebar .sidebar-header .sidebar-logo .half-logo {
  opacity: 0;
  visibility: hidden;
  height: 0;
  transition: opacity 1s;
}

.sidebar .sidebar-header .sidebar-toggle {
  border: 0;
  outline: 0;
  background: transparent;
  cursor: pointer;
  padding: 0;
  margin: 0;
}

.sidebar .sidebar-header .sidebar-close {
  border: 0;
  outline: 0;
  display: none;
  background: 0;
  padding: 0;
  margin-right: 10px;
  font-size: 28px;
}

@media (max-width: 768px) {
  .sidebar .sidebar-header .sidebar-close {
    display: block;
  }
}

.sidebar .sidebar-header .sidebar-close:hover {
  color: red;
}

.sidebar .sidebar-menu {
  height: calc(100vh - 60px);
  overflow-y: scroll;
  overflow-x: none;
  /* font icon size */
  /* has arrows color */
  /* Default State reboot*/
}

.sidebar .sidebar-menu i {
  font-size: 22px;
}

.sidebar .sidebar-menu ol,
.sidebar .sidebar-menu ul {
  list-style: none;
  padding-left: 0;
  margin-left: 0;
}

.sidebar .sidebar-menu ol a,
.sidebar .sidebar-menu ul a {
  text-decoration: none;
}

.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-section-heading {
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 500;
  line-height: 28px;
  margin-left: 17px;
  color: var(--primary-color);
}

.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item>ul>li>a {
  padding-left: 47px;
  padding-top: 16px;
  padding-bottom: 16px;
}

.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item>ul>li>ul>li>a {
  padding-left: 64px;
  padding-top: 16px;
  padding-bottom: 16px;
}

.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a {
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
  gap: 10px;
  padding: 15px 0px 15px 25px;
  font-weight: 500;
  color: var(--ot-text-primary);
}

.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a::before {
  content: "";
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #6f767e;
}

.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a.has-arrow::after {
  transform: rotate(135deg) translate(0, -50%);
}

.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a.parent-item-content::before {
  content: none;
}

.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a:hover {
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sidebar .sidebar-menu .sidebar-menu-section .mm-active>a {
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  border-left: 2px solid var(--primary-color);
  background-color: rgba(115, 218, 255, 0.3);
}

.sidebar .sidebar-menu .sidebar-menu-section .mm-active>a::before {
 background: #645CBB;
}

.sidebar .sidebar-menu .sidebar-menu-section .mm-active>a.has-arrow::after {
  color: var(--primary-color);
  transform: rotate(225deg) translate(0, -50%);
}

.sidebar .sidebar-menu .sidebar-menu-section ul>li>a {
  padding-left: 28px;
}

.sidebar .sidebar-menu .sidebar-menu-section .mm-active ul {
  background: rgba(115, 218, 255, 0.12);
}

.sidebar-expand .sidebar {
  left: 0;
  transition: left 0.3s;
}

/* Changes while RTL Start */
.rtl .half-expand {
  /* Sidebar Header */
  /* main content on half expand */
}

.rtl .half-expand .sidebar .sidebar-header {
  /* Sidebar Logo */
  /* Sidebar Half Expand Icon*/
}

.rtl .half-expand .sidebar .sidebar-header .half-expand-toggle {
  position: absolute;
  right: 100%;
  margin-right: 10px;
}

.rtl .half-expand .sidebar .sidebar-header .half-expand-toggle img {
  transform: rotate(180deg);
}

.rtl .half-expand .sidebar .sidebar-menu {
  overflow: visible;
}

.rtl .half-expand .sidebar .sidebar-menu .sidebar-menu-section-heading {
  margin-right: 0;
}

.rtl .half-expand .sidebar .sidebar-menu .parent-menu-list .sidebar-menu-item .parent-item-content span {
  display: none;
}

.rtl .half-expand .sidebar .sidebar-menu .parent-menu-list .sidebar-menu-item a {
  border: none;
}

.rtl .half-expand .sidebar .sidebar-menu-item>ul>li>a {
  padding-left: 10px !important;
}

.rtl .half-expand .sidebar .sidebar-menu-item>ul>li>ul>li>a {
  padding-left: 30px !important;
}

.rtl .half-expand .sidebar ul.parent-menu-list>li>ul {
  position: absolute;
  top: 0;
  width: 236px;
  left: 100px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.04);
  border-radius: 10px;
}

.rtl .half-expand .theme-switch {
  background: #edfaff;
  z-index: 3000000;
  position: absolute;
  bottom: 0;
}

.rtl .half-expand .sidebar-header {
  background: #edfaff;
  z-index: 3000000;
}

.rtl .half-expand .parent-menu-list>li>ul {
  right: 85px !important;
}

.rtl .half-expand .sidebar-menu {
  position: absolute;
  left: 50%;
  top: 10%;
  z-index: 2000;
  overflow: hide;
  height: calc(100vh - 100px);
  transform: translateX(-50%);
}

.rtl .half-expand .main-content {
  margin-right: 100px;
  transition: -webkit-margin-right 0.3s;
  transition: margin-right 0.3s;
  transition: margin-right0 0.3s, -webkit-margin-right 0.3s;
}

/*Changes while RTL End*/
.half-expand {
  /* Sidebar Header */
  /* half expand hoverable menu start */
  /* half expand hoverable menu end */
  /* main content on half expand */
}

.half-expand .sidebar {
  width: 100px;
}

.half-expand .sidebar .sidebar-header {
  position: relative;
  /* Sidebar Logo */
  /* Sidebar Half Expand Icon*/
}

.half-expand .sidebar .sidebar-header .sidebar-logo {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.half-expand .sidebar .sidebar-header .sidebar-logo a .half-logo {
  opacity: 1;
  visibility: visible;
  height: auto;
  transition: -webkit- opacity 0.4s;
  transition: opacity 0.4s;
  transition: opacity 0.4s, -webkit- opacity 0.4s;
}

.half-expand .sidebar .sidebar-header .sidebar-logo a .full-logo {
  opacity: 0;
  visibility: hidden;
  height: 0;
  transition: -webkit- opacity 0.4s;
  transition: opacity 0.4s;
  transition: opacity 0.4s, -webkit- opacity 0.4s;
}

.half-expand .sidebar .sidebar-header .half-expand-toggle {
  position: absolute;
  left: 100%;
  margin-left: 10px;
}

.half-expand .sidebar .sidebar-header .half-expand-toggle img {
  transform: rotate(180deg);
}

.half-expand .sidebar .sidebar-header .sidebar-close {
  border: 0;
  outline: 0;
  display: none;
  background: 0;
  padding: 0;
  margin-right: 10px;
  font-size: 28px;
}

@media (max-width: 768px) {
  .half-expand .sidebar .sidebar-header .sidebar-close {
    display: block;
  }
}

.half-expand .sidebar .sidebar-header .sidebar-close:hover {
  color: #edfaff;
}

.half-expand .sidebar .sidebar-menu {
  overflow: visible;
  width: 100% !important;
}

.half-expand .sidebar .sidebar-menu .sidebar-menu-section-heading {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0;
}

.half-expand .sidebar .sidebar-menu .sidebar-menu-section-heading .on-half-expanded {
  display: none;
}

.half-expand .sidebar .sidebar-menu .parent-menu-list .sidebar-menu-item {
  position: relative;
}

.half-expand .sidebar .sidebar-menu .parent-menu-list .sidebar-menu-item .parent-item-content {
  padding: 15px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.half-expand .sidebar .sidebar-menu .parent-menu-list .sidebar-menu-item .parent-item-content::after {
  content: none;
}

.half-expand .sidebar .sidebar-menu .parent-menu-list .sidebar-menu-item .parent-item-content span {
  display: none;
}

.half-expand .sidebar .sidebar-menu .parent-menu-list .sidebar-menu-item a {
  border: none;
}

.half-expand .sidebar .sidebar-menu-item>ul>li>a {
  padding-left: 10px !important;
}

.half-expand .sidebar .sidebar-menu-item>ul>li>ul>li>a {
  padding-left: 30px !important;
}

.half-expand .sidebar ul.parent-menu-list>li>ul {
  position: absolute;
  top: 0;
  width: 236px;
  left: 98px !important;
  background: #fcfcfc !important;
  border: 1px solid #eaeaea;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.04);
  border-radius: 10px;
}

.half-expand .sidebar-dropdown-menu li {
  position: relative;
}

.half-expand .sidebar-dropdown-menu>li ul {
  position: absolute;
  left: 101%;
  top: 0;
  min-width: 200px;
  display: none;
  background: #fcfcfc !important;
  border: 1px solid #eaeaea;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.04);
  border-radius: 10px;
}

.half-expand .sidebar-dropdown-menu li:hover>ul,
.half-expand .sidebar-dropdown-menu li:hover>ul.collapse {
  display: block !important;
  height: auto !important;
  z-index: 1000;
}

.half-expand .theme-switch {
  background: var(--ot-bg-secondary);
  z-index: 3000000;
  position: absolute;
  bottom: 0;
}

.half-expand .sidebar-header {
  background: var(--ot-bg-secondary);
  z-index: 3000000;
}

.half-expand .parent-menu-list>li>ul {
  left: 85px !important;
}

.half-expand .sidebar-menu {
  position: absolute;
  left: 50%;
  top: 10%;
  z-index: 2000;
  overflow: hide;
  height: calc(100vh - 100px);
  transform: translateX(-50%);
}

.half-expand .main-content {
  margin-left: 100px;
  transition: -webkit-margin-left 0.3s;
  transition: margin-left 0.3s;
  transition: margin-left 0.3s, -webkit-margin-left 0.3s;
}

/* Dark Theme start*/
.dark-theme .half-expand ul.parent-menu-list>li>ul,
.dark-theme .half-expand .sidebar-dropdown-menu>li ul {
  background: var(--ot-bg-secondary) !important;
  border: none;
}

/* Dark Theme end*/
/* Dark Theme Start Start*/
.dark-theme .sidebar-menu .sidebar-menu-section .mm-active>a {
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  border-left: 2px solid var(--primary-color);
  background-color: var(--ot-bg-secondary-opacity);
}

.dark-theme .sidebar-menu .sidebar-menu-section .mm-active>a::before {
 background: #645CBB;
}

.dark-theme .sidebar-menu .sidebar-menu-section .mm-active ul {
  background-color: var(--ot-bg-primary);
}

/* Theme Switch */
/* dark theming start */
.dark-theme .theme-switch::before {
  background: #272B30;
}

/* dark theming end */
/* When Half Expand start */
.half-expand .theme-switch .switch-field {
  justify-content: center;
}

.half-expand .theme-switch .switch-field label {
  width: 34px;
  height: 34px;
  justify-content: center;
}

.half-expand .theme-switch .switch-field label span,
.half-expand .theme-switch .switch-field label p {
  display: none;
}

/* When Half Expand start*/
.theme-switch {
  height: 10%;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 5px 10px;
  position: relative;
}

.theme-switch::before {
  content: "";
  width: 80%;
  height: 2px;
  border-radius: 10px;
  background: #fcfcfc;
  margin-right: 10px;
  position: absolute;
  top: 5px;
}

.theme-switch .switch-field {
  overflow: hidden;
  display: flex;
  align-items: center;
  background: var(--ot-bg-secondary);
  padding: 10px;
  border-radius: 2rem;
}

.theme-switch .switch-field input {
  display: none;
}

.theme-switch .switch-field label {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  color: #333;
  font-size: 1em;
  font-weight: normal;
  text-align: center;
  text-shadow: none;
  padding: 0.5em 1em;
  -webkit-transition: all 0.1s ease-in-out;
  -moz-transition: all 0.1s ease-in-out;
  -ms-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
  border-radius: 2rem;
  cursor: pointer;
  color: var(--ot-text-primary);
}

.theme-switch .switch-field label span {
  margin: 0;
  font-weight: 600;
}

.theme-switch .switch-field:hover {
  cursor: pointer;
}

.theme-switch .switch-field input:checked+label {
 background: #645CBB;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.04);
  border-radius: 50px;
}

.theme-switch .switch-field input:checked+label span {
  color: #fff;
}

.dark-theme .theme-switch .switch-field {
  background-color: var(--ot-bg-primary);
}

/* Main Content */
/*Changes while RTL Start*/
body.rtl .main-content {
  margin-right: 256px;
  margin-left: 0;
  transition: -webkit-right 0.3s;
  transition: margin-right 0.3s;
  transition: margin-right 0.3s, -webkit-margin-right 0.3s;
}

@media (max-width: 768px) {
  body.rtl .main-content {
    width: 100%;
    margin-right: 0;
    transition: -webkit-left 0.3s;
    transition: margin-right 0.3s;
    transition: margin-right 0.3s, -webkit-margin-right 0.3s;
  }
}

/*Changes while RTL Start*/
.main-content {
  margin-left: 256px;
  transition: -webkit-left 0.3s;
  transition: margin-left 0.3s;
  transition: margin-left 0.3s, -webkit-margin-left 0.3s;
}

@media (max-width: 768px) {
  .main-content {
    width: 100%;
    margin-left: 0;
    transition: -webkit-left 0.3s;
    transition: margin-left 0.3s;
    transition: margin-left 0.3s, -webkit-margin-left 0.3s;
  }
}

.main-content .dashboard-heading .title {
  font-weight: 700;
  font-size: 24px;
  line-height: 34px;
}

/* Common Card */
/* Common card for layout for dashboard */
.ot-card {
  background-color: var(--ot-bg-secondary);
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  border-radius: 5px;
  padding: 28px;
  border: none;
}

.ot-card .card-header, .ot-card .card-body, .ot-card .card-footer {
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;
}

.ot-card .card-body {
  padding-top: 20px;
}

/* Summery Card */
/* Summery Card */
.summery-card .card-heading .card-icon {
  width: 55px;
  height: 55px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.summery-card .card-heading .card-icon.icon-circle-1 {
  background-color: rgba(172, 224, 195, 0.1);
}

.summery-card .card-heading .card-icon.icon-circle-2 {
  background-color: rgba(194, 183, 252, 0.1);
}

.summery-card .card-heading .card-icon.icon-circle-3 {
  background-color: rgba(254, 179, 145, 0.1);
}

.summery-card .card-heading .card-icon.icon-circle-4 {
  background-color: rgba(168, 225, 249, 0.1);
}

.summery-card .card-heading .card-content {
  margin-left: 16px;
  /* rtl */
}

.rtl .summery-card .card-heading .card-content {
  margin-left: 0;
  margin-right: 16px;
}

.summery-card .card-heading .card-content h4 {
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  margin-bottom: 0px;
  color: var(--ot-text-subtitle);
}

.summery-card .card-heading .card-content h1 {
  font-weight: 600;
  font-size: 38px;
  line-height: 42px;
  margin-top: 0px;
  color: var(--ot-text-title);
}

.summery-card .card-bottom .card-states {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.summery-card .card-bottom .card-states .card-badge {
  border-radius: 50px;
  padding: 4px 7px;
  gap: 10px;
  font-size: 12px;
  font-weight: 500;
  color: var(--ot-text-primary);
}

.summery-card .card-bottom .card-states .card-badge:first-child {
  background-color: #e9faf4;
}

.dark-theme .summery-card .card-bottom .card-states .card-badge:first-child {
  background-color: rgba(233, 250, 244, 0.1);
}

.summery-card .card-bottom .card-states .card-badge:first-child .count {
  font-weight: 600;
  color: #7ab668;
}

.summery-card .card-bottom .card-states .card-badge:last-child {
  background-color: #fff0ed;
}

.dark-theme .summery-card .card-bottom .card-states .card-badge:last-child {
  background-color: rgba(255, 240, 237, 0.1);
}

.summery-card .card-bottom .card-states .card-badge:last-child .count {
  font-weight: 600;
  color: #ff6a54;
}

.chart-custom-content p {
  font-weight: 400;
  font-size: 12px;
  line-height: 21px;
}

.chart-custom-content h2 {
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: var(--ot-text-title);
}

.chart-custom-content .percentage-spike img {
  width: 12px;
  height: 12px;
}

.chart-custom-content .percentage-spike .primary-blue {
  color: #645CBB;
}

.chart-custom-content .percentage-spike .secondary-blue {
  color: #55b1f3;
}

.chart-custom-content .percentage-spike .primary-red {
  color: #ff4f75;
}

.chart-custom-content .percentage-spike .primary-yellow {
  color: #ff991a;
}

.chart-custom-content .percentage-spike:nth-child(1) {
  background-color: #feb391;
}

.chart-header p {
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: var(--ot-text-primary);
}

.statistics-card .card .card-bottom .card-states .btn.active-state-btn {
  background-color: #e9faf4;
}

.statistics-card .card .card-bottom .card-states .btn.active-state-btn .count {
  color: #24c087;
}

.statistics-card .card .card-bottom .card-states .btn.active-state-btn .status {
  color: var(--ot-text-primary);
}

.statistics-card .card .card-bottom .card-states .btn.inactive-state-btn {
  background-color: #fff0ed;
}

.statistics-card .card .card-bottom .card-states .btn.inactive-state-btn .count {
  color: var(#e55f4b);
}

.statistics-card .card .card-bottom .card-states .btn.inactive-state-btn .status {
  color: var(--ot-text-primary);
}

/* statistics-charts  */
.statistics-chart .visited-customer .card .card-top {
  margin-bottom: 15px;
}

.statistics-chart .visited-customer .card .card-top .title h4 {
  color: var(--ot-text-title);
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
}

.statistics-chart .visited-customer .card .card-top .card-content .stats {
  font-weight: 600;
}

.statistics-chart .visited-customer .card .card-top .statistics-chart .visited-customer .card .card-top .card-content .stats span.vs {
  margin-left: 12px;
}

.statistics-chart .visited-customer .card .card-top .card-content .btn.active-state-btn {
  background-color: #e9faf4;
}

.statistics-chart .visited-customer .card .card-top .card-content .btn.active-state-btn .count {
  color: var(--success-dark);
}

.ot-charts .card .card-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: baseline;
}

@media (max-width: 576px) {
  .ot-charts .card .card-header {
    flex-direction: column;
    justify-content: center;
    gap: 10px;
  }
}

.ot-charts .card .card-header h1 {
  font-family: 'Lexend';
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
  color: var(--ot-text-title);
}

/* Datatable */
.table-content .table-toolbar {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  color: #000000;
}

.table-content .table-toolbar .form-select {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  color: var(--ot-text-title);
  width: auto;
  padding: 10px 30px 10px 14px;
  border: 1px solid var(--ot-border-table-toolbar-per-page);
  border-radius: 5px;
  background-image: url("../images/basic-datatable/show/down-icon.svg");
  background-repeat: no-repeat;
  background-position: right 14px center;
  background-size: 12px;
  outline: 0;
  box-shadow: none;
  background-color: var(--ot-bg-table-toolbar-per-page);
}

.table-content .table-toolbar .btn-add {
  text-decoration: none;
  padding: 10px 17px;
  border: 1px solid #645CBB;
 background: #645CBB;
  border-radius: 5px;
  display: block;
  text-align: center;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: #ffffff;
}

.table-content .table-toolbar .btn-daterange, .table-content .table-toolbar .dropdown-designation .btn-designation, .table-content .table-toolbar .dropdown-export .btn-export {
  background-image: linear-gradient(90deg, #0f6aff 0%, #645CBB 100%), linear-gradient(90deg, #0f6aff 0%, #645CBB 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  box-shadow: 2px 1000px 1px var(--ot-bg-table-toolbar-btn-outline-primary) inset;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: var(--ot-text-primary);
  border: 1px solid transparent;
  padding: 10px 12px;
  border-radius: 5px;
  transition: all ease-in-out 0.1s;
}

.table-content .table-toolbar .btn-daterange .icon, .table-content .table-toolbar .dropdown-designation .btn-designation .icon, .table-content .table-toolbar .dropdown-export .btn-export .icon {
  color: #645CBB;
  font-size: 14px;
}

.table-content .table-toolbar .btn-daterange:hover, .table-content .table-toolbar .dropdown-designation .btn-designation:hover, .table-content .table-toolbar .dropdown-export .btn-export:hover {
  box-shadow: none;
 background: #645CBB;
  color: #ffffff;
}

.table-content .table-toolbar .btn-daterange:hover .icon, .table-content .table-toolbar .dropdown-designation .btn-designation:hover .icon, .table-content .table-toolbar .dropdown-export .btn-export:hover .icon {
  color: #ffffff;
}

.table-content .table-toolbar .btn-daterange:focus, .table-content .table-toolbar .dropdown-designation .btn-designation:focus, .table-content .table-toolbar .dropdown-export .btn-export:focus {
  box-shadow: none;
 background: #645CBB;
  color: #ffffff;
}

.table-content .table-toolbar .btn-daterange:focus .icon, .table-content .table-toolbar .dropdown-designation .btn-designation:focus .icon, .table-content .table-toolbar .dropdown-export .btn-export:focus .icon {
  color: #ffffff;
}

.table-content .table-toolbar .search-box {
  position: relative;
  border: 1px solid var(--ot-border-table-toolbar-search);
  border-radius: 5px;
}

.table-content .table-toolbar .search-box .form-control, .table-content .table-toolbar .search-box .profile-content .profile-body-form .form-box .form-select, .profile-content .profile-body-form .form-box .table-content .table-toolbar .search-box .form-select {
  padding: 8px 45px 8px 16px;
  border: none;
  box-shadow: none;
  background-color: var(--ot-bg-table-toolbar-search);
  color: var(--ot-text-title);
}

.table-content .table-toolbar .search-box .form-control::placeholder, .table-content .table-toolbar .search-box .profile-content .profile-body-form .form-box .form-select::placeholder, .profile-content .profile-body-form .form-box .table-content .table-toolbar .search-box .form-select::placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

.table-content .table-toolbar .search-box .icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  color: #6f767e;
}

.table-content .table-toolbar .dropdown-designation .dropdown-menu {
  position: relative;
  background: #ffffff;
  border: 1px solid #eaeaea;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  margin-top: 10px !important;
  padding: 0;
}

.table-content .table-toolbar .dropdown-designation .search-content {
  padding: 20px;
}

.table-content .table-toolbar .dropdown-designation .search-content .search-box {
  position: relative;
  border: 1px solid #eaeaea;
  border-radius: 50px;
}

.table-content .table-toolbar .dropdown-designation .search-content .search-box .form-control, .table-content .table-toolbar .dropdown-designation .search-content .search-box .profile-content .profile-body-form .form-box .form-select, .profile-content .profile-body-form .form-box .table-content .table-toolbar .dropdown-designation .search-content .search-box .form-select {
  width: 232px;
  padding: 8px 45px 8px 16px;
  border: none;
  border-radius: 50px;
  box-shadow: none;
  background-color: #ffffff;
  color: #1a1d1f;
}

.table-content .table-toolbar .dropdown-designation .search-content .search-box .form-control::placeholder, .table-content .table-toolbar .dropdown-designation .search-content .search-box .profile-content .profile-body-form .form-box .form-select::placeholder, .profile-content .profile-body-form .form-box .table-content .table-toolbar .dropdown-designation .search-content .search-box .form-select::placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

.table-content .table-toolbar .dropdown-designation .search-content .search-box .icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  color: #6f767e;
}

.table-content .table-toolbar .dropdown-designation .list {
  padding: 26px;
  margin: 0;
}

.table-content .table-toolbar .dropdown-designation .list .list-item {
  list-style: none;
  margin-bottom: 24px;
}

.table-content .table-toolbar .dropdown-designation .list .list-item:last-child {
  margin-bottom: 0;
}

.table-content .table-toolbar .dropdown-designation .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #6f767e;
}

.table-content .table-toolbar .dropdown-designation .dropdown-item:hover {
  background-color: #ffffff;
}

.table-content .table-toolbar .dropdown-designation .dropdown-item:focus {
  background-color: #ffffff;
}

.table-content .table-toolbar .dropdown-designation .dropdown-item:active {
  background-color: #ffffff;
}

.table-content .table-toolbar .dropdown-designation .dropdown-item .icon {
  color: var(--primary-color);
  font-size: 14px;
}

.table-content .table-toolbar .dropdown-action .btn-dropdown {
  border: 1px solid var(--ot-border-table-toolbar-btn-action);
  width: 40px;
  height: 40px;
  background: var(--ot-bg-table-toolbar-btn-action);
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  border-radius: 50px;
  color: var(--primary-color);
  font-size: 16px;
}

.table-content .table-toolbar .dropdown-action .btn-dropdown:focus {
 background: #645CBB;
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  color: #ffffff;
}

.table-content .table-toolbar .dropdown-action .dropdown-menu {
  position: relative;
  background: #ffffff;
  border: 1px solid #eaeaea;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  padding: 24px;
  margin-top: 10px !important;
}

.table-content .table-toolbar .dropdown-action .dropdown-menu::before {
  content: "";
  position: absolute;
  top: -5px;
  right: 8px;
  background: #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  transform: rotate(45deg);
  padding: 10px;
}

.table-content .table-toolbar .dropdown-action .dropdown-menu::after {
  content: "";
  position: absolute;
  top: 0;
  right: 5px;
  background: #ffffff;
  padding: 10px 15px;
}

.table-content .table-toolbar .dropdown-action .dropdown-menu li {
  margin-bottom: 20px;
}

.table-content .table-toolbar .dropdown-action .dropdown-menu li:last-child {
  margin-bottom: 0;
}

.table-content .table-toolbar .dropdown-action .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: #6f767e;
}

.table-content .table-toolbar .dropdown-action .dropdown-item:hover {
  background-color: #ffffff;
}

.table-content .table-toolbar .dropdown-action .dropdown-item:focus {
  background-color: #ffffff;
}

.table-content .table-toolbar .dropdown-action .dropdown-item:active {
  background-color: #ffffff;
}

.table-content .table-toolbar .dropdown-action .dropdown-item .icon {
  color: var(--primary-color);
  font-size: 14px;
}

.table-content .table-toolbar .dropdown-export .dropdown-menu {
  position: relative;
  background: #ffffff;
  border: 1px solid #eaeaea;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  padding: 24px;
  margin-top: 10px !important;
}

.table-content .table-toolbar .dropdown-export .dropdown-menu li {
  margin-bottom: 20px;
}

.table-content .table-toolbar .dropdown-export .dropdown-menu li:last-child {
  margin-bottom: 0;
}

.table-content .table-toolbar .dropdown-export .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: #6f767e;
}

.table-content .table-toolbar .dropdown-export .dropdown-item:hover {
  background-color: #ffffff;
}

.table-content .table-toolbar .dropdown-export .dropdown-item:focus {
  background-color: #ffffff;
}

.table-content .table-toolbar .dropdown-export .dropdown-item:active {
  background-color: #ffffff;
}

.table-content .table-toolbar .dropdown-export .dropdown-item .icon {
  color: var(--primary-color);
  font-size: 14px;
}

.table-content.table-basic .card {
  background-color: var(--ot-bg-table-card);
  border: 1px solid transparent;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  border-radius: 0;
}

.table-content.table-basic .card-body {
  padding: 40px 40px;
}

.table-content.table-basic .card-header {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 18px;
  font-weight: 700;
  line-height: 30px;
  color: var(--ot-text-title);
  padding: 15px 40px;
  border-bottom: 1px solid var(--ot-border-table-card-header);
  background-color: var(--ot-bg-table-card-header);
  border-radius: 0;
}

.table-content.table-basic .table {
  border-color: var(--ot-border-table);
  vertical-align: middle;
  margin: 0;
}

.table-content.table-basic .table .sorting_asc, .table-content.table-basic .table .sorting_desc {
  position: relative;
}

.table-content.table-basic .table .sorting_asc::before, .table-content.table-basic .table .sorting_desc::before {
  position: absolute;
  left: auto;
  right: 16px;
  content: "\f106";
  font-family: "Font Awesome 6 Free";
  font-size: 12px;
  font-weight: 900;
  top: 42%;
  color: var(--ot-color-table-icon-sorting-asc-up);
  cursor: pointer;
  transform: translateY(-42%);
}

.table-content.table-basic .table .sorting_asc::after, .table-content.table-basic .table .sorting_desc::after {
  position: absolute;
  left: auto;
  right: 16px;
  content: "\f107";
  font-family: "Font Awesome 6 Free";
  font-size: 12px;
  font-weight: 900;
  top: 62%;
  color: var(--ot-color-table-icon-sorting-asc-down);
  cursor: pointer;
  transform: translateY(-62%);
}

.table-content.table-basic .table .sorting_desc::before {
  color: var(--ot-color-table-icon-sorting-desc-up);
}

.table-content.table-basic .table .sorting_desc::after {
  color: var(--ot-color-table-icon-sorting-desc-down);
}

.table-content.table-basic .table .check-box .form-check-input {
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 1px solid var(--ot-border-table-checkbox);
  background-color: var(--ot-bg-table-checkbox);
}

.table-content.table-basic .table .check-box .form-check-input:focus {
  box-shadow: none;
}

.table-content.table-basic .table .check-box .form-check-input:checked {
  background: url("../images/basic-datatable/check/ok.svg"), linear-gradient(90deg, #0f6aff 0%, #645CBB 100%);
  background-repeat: no-repeat;
  background-position: center;
}

.table-content.table-basic .table .thead {
  background: var(--ot-bg-table-thead);
  border-bottom-color: var(--ot-border-table-thead);
}

.table-content.table-basic .table .thead tr th {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: var(--ot-text-title);
  padding: 16px 32px 16px 16px;
  vertical-align: middle;
  white-space: nowrap;
  border-color: var(--ot-border-table-thead);
}

.table-content.table-basic .table .tbody tr:nth-of-type(odd) {
  background: var(--ot-bg-table-tbody);
}

.table-content.table-basic .table .tbody tr td {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 12px;
  font-weight: 500;
  line-height: 21px;
  color: var(--ot-text-primary);
  padding: 16px;
  vertical-align: middle;
  white-space: nowrap;
}

.table-content.table-basic .table .dropdown-action .btn-dropdown {
  border: 1px solid var(--ot-border-table-toolbar-btn-action);
  width: 28px;
  height: 28px;
  background: var(--ot-bg-table-toolbar-btn-action);
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  border-radius: 50px;
  color: var(--primary-color);
  font-size: 10px;
}

.table-content.table-basic .table .dropdown-action .btn-dropdown:focus {
 background: #645CBB;
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  color: #ffffff;
}

.table-content.table-basic .table .dropdown-action .dropdown-menu {
  background: #ffffff;
  border: 1px solid #eaeaea;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  padding: 24px;
  margin-top: 10px !important;
}

.table-content.table-basic .table .dropdown-action .dropdown-menu li {
  margin-bottom: 20px;
}

.table-content.table-basic .table .dropdown-action .dropdown-menu li:last-child {
  margin-bottom: 0;
}

.table-content.table-basic .table .dropdown-action .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: #6f767e;
}

.table-content.table-basic .table .dropdown-action .dropdown-item:hover {
  background-color: #ffffff;
}

.table-content.table-basic .table .dropdown-action .dropdown-item:focus {
  background-color: #ffffff;
}

.table-content.table-basic .table .dropdown-action .dropdown-item:active {
  background-color: #ffffff;
}

.table-content.table-basic .table .dropdown-action .dropdown-item .icon {
  color: var(--primary-color);
  font-size: 14px;
}

/* Charts */
.statistic-card .card-heading .card-title {
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
  color: #1a1d1f;
  margin: 0;
  padding: 0;
}

.ot-visit-chart .card-header {
  flex-wrap: wrap;
}

.ot-visit-chart p {
  font-family: "Lexend";
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: var(--ot-text-primary);
  margin-top: 8px;
}

.chart-custom-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
}

@media (max-width: 576px) {
  .chart-custom-content {
    flex-direction: column;
  }
}

/* Common Buttons */
/* Common Buttons */
.ot-btn-common, .ot-dropdown-btn, .ot-btn-primary, .ot-btn-success, .ot-btn-danger, .ot-btn-warning, .ot-btn-info {
 background: #645CBB;
  color: #ffffff !important;
  border-radius: 5px;
  border: none;
  font-weight: 600;
  font-size: 16px;
  font-family: "Lexend", sans-serif;
  line-height: 20px;
  padding: 10px 24px;
  list-style: none;
  outline: 0;
}

.ot-btn-common.btn-lg, .btn-lg.ot-dropdown-btn, .btn-lg.ot-btn-primary, .btn-lg.ot-btn-success, .btn-lg.ot-btn-danger, .btn-lg.ot-btn-warning, .btn-lg.ot-btn-info {
  padding: 15px 24px;
}

.ot-btn-common.btn-md, .btn-md.ot-dropdown-btn, .btn-md.ot-btn-primary, .btn-md.ot-btn-success, .btn-md.ot-btn-danger, .btn-md.ot-btn-warning, .btn-md.ot-btn-info {
  padding: 10px 24px;
}

.ot-btn-common.btn-sm, .btn-sm.ot-dropdown-btn, .btn-sm.ot-btn-primary, .btn-sm.ot-btn-success, .btn-sm.ot-btn-danger, .btn-sm.ot-btn-warning, .btn-sm.ot-btn-info {
  padding: 6px 24px;
}

.ot-dropdown-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 13px;
}
.ot-dropdown-btn:focus{
  box-shadow: none !important;
}
.ot-dropdown-btn::after {
  content: none;
}

.ot-btn-primary {
 background: #645CBB;
}

.ot-btn-primary:hover {
  background: #F99417 !important;
}

.ot-btn-success {
  background: linear-gradient(90deg, #29d697 0%, #00ffa2 100%);
}

.ot-btn-success:hover {
  background: linear-gradient(90deg, #29d697 0%, #00ffa2 90%);
}

.ot-btn-danger {
  background: linear-gradient(90deg, #ff6a54 0%, #fd2201 100%);
}

.ot-btn-danger:hover {
  background: linear-gradient(90deg, #ff6a54 0%, #fd2201 90%);
}

.ot-btn-warning {
  background: linear-gradient(90deg, #fdc400 0%, #fec403 100%);
}

.ot-btn-warning:hover {
  background: linear-gradient(90deg, #fdc400 0%, #fec403 90%);
}

.ot-btn-info {
  background: linear-gradient(90deg, #138496 0%, #00ddff 100%);
}

.ot-btn-info:hover {
  background: linear-gradient(90deg, #00ddff 0%, #00ddff 90%);
}

/* Tables */
.badge-basic-success-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: #e9faf4;
  color: #29d697;
}

.badge-basic-danger-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: #fff0ed;
  color: #ff6a54;
}

.badge-basic-warning-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: #fff1e0;
  color: #ff991a;
}

.badge-basic-info-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: #e5feff;
  color: #00dfe5;
}

.badge-basic-primary-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: #eef0ff;
  color: #645CBB;
}

.page-heading h1 {
  font-size: 30px;
  line-height: 38px;
  color: #1a1d1f;
}

.component-page-heading h1 {
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
}

.basic-table, .ot-table-bg, .table-color-col {
  width: 100%;
  background: var(--ot-bg-table-card);
}

.basic-table thead th, .ot-table-bg thead th, .table-color-col thead th {
  background: var(--ot-bg-table-basic-thead);
  height: 56px;
  border-bottom: 1px solid var(--ot-border-basic-table-thead);
  padding-left: 16px;
  color: var(--ot-text-title);
}

.basic-table thead th:first-child, .ot-table-bg thead th:first-child, .table-color-col thead th:first-child {
  border-radius: 10px 0 0 0;
}

.basic-table thead th:last-child, .ot-table-bg thead th:last-child, .table-color-col thead th:last-child {
  border-radius: 0 10px 0 0;
}

.basic-table tbody td, .ot-table-bg tbody td, .table-color-col tbody td {
  font-weight: 400;
  padding-left: 16px;
  height: 56px;
  color: var(--ot-text-primary);
}

.ot-table-bg th,
.ot-table-bg tr:nth-child(even) {
  background-color: var(--ot-bg-basic-table-tbody);
}

.table-color-col th:nth-child(odd),
.table-color-col td:nth-child(odd) {
  background-color: var(--ot-bg-basic-table-tbody);
}

.table-color-col td {
  border-bottom: 1px solid var(--ot-border-basic-table);
}

.table-color-col tr:last-child td {
  border: none;
}

.progress-size, .progress-darkblue, .progress-green, .progress-violet, .progress-skyblue, .progress-darkviolet {
  width: 100%;
  height: 8px;
}

.progress-darkblue {
  background-color: #dde1ff;
}

.progress-darkblue .progress-bar-darkblue {
  border-radius: 5px;
  background-color: #0010f7;
}

.progress-green {
  background-color: #e9faf4;
}

.progress-green .progress-bar-green {
  border-radius: 5px;
  background-color: #29d697;
}

.progress-violet {
  background-color: #ebe7ff;
}

.progress-violet .progress-bar-violet {
  border-radius: 5px;
  background-color: #c8bbfb;
}

.progress-skyblue {
  background-color: #d5e8fe;
}

.progress-skyblue .progress-bar-skyblue {
  border-radius: 5px;
  background-color: #4797ff;
}

.progress-darkviolet {
  background-color: #e8defe;
}

.progress-darkviolet .progress-bar-darkviolet {
  border-radius: 5px;
  background-color: #9d6fff;
}

.ot-badge.primary {
  background-color: var(--ot-bg-primary);
  color: #7ab668;
  font-weight: 600;
  font-size: 12px;
  line-height: 15px;
  padding: 4px 10px;
  border-radius: 50px;
}

.btn-select-content {
  width: 305px;
  height: 50px;
}

.btn-select-content .btn-select {
  width: 100%;
  height: 50px;
  border: 1px solid var(--ot-border-primary);
  border-radius: 5px;
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  background-color: var(--ot-bg-secondary);
  color: var(--ot-text-primary);
}

.btn-select-content .btn-select:hover {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.btn-select-content ul {
  width: 305px;
  padding: 20px;
}

.btn-select-content .sub-menu {
  position: relative !important;
  width: 100% !important;
  transform: translate(0px, 0px) !important;
}

.input-Range-date {
  height: 50px;
  flex-direction: row;
  border: 1px solid #eaeaea;
  border-radius: 5px;
  padding: 0px 10px;
  gap: 25px;
}

.input-Range-date input {
  border: none;
  outline: none;
}

.input-Range-date:hover {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.btn-primary-custom {
  font-weight: 600;
  height: 50px;
 background: #645CBB;
  border: none;
}

.input-search-with-icon {
  position: relative;
  width: 320px;
  height: 40px;
}

@media (max-width: 768px) {
  .input-search-with-icon {
    width: 200px;
  }
}

.input-search-with-icon input {
  background: var(--ot-bg-secondary);
  width: 100%;
  height: 100%;
  padding: 10px 45px 10px 16px;
  border: 1px solid #eaeaea;
  border-radius: 5px;
  outline: none;
}

.input-search-with-icon input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.input-search-with-icon ::placeholder {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-search-with-icon i {
  position: absolute;
  top: 12px;
  color: #6f767e;
}

.input-search-with-icon i.fa-search {
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.button-group {
  border-color: var(--ot-text-primary) !important;
}

.button-group:hover {
  background-color: #a1a9ff34 !important;
  border-color: #645CBB !important;
}

.basic-table.pagination-content .pagination, .pagination-content.ot-table-bg .pagination, .pagination-content.table-color-col .pagination {
  width: 368px;
}

.basic-table.pagination-content .pagination li, .pagination-content.ot-table-bg .pagination li, .pagination-content.table-color-col .pagination li {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px;
  width: 32px;
  border: 1px solid #eaeaea;
  border-radius: 7px;
}

.basic-table.pagination-content .pagination li a, .pagination-content.ot-table-bg .pagination li a, .pagination-content.table-color-col .pagination li a {
  color: var(--ot-text-title);
}

.basic-table.pagination-content .pagination li a i, .pagination-content.ot-table-bg .pagination li a i, .pagination-content.table-color-col .pagination li a i {
  color: var(--ot-text-primary);
}

.basic-table.pagination-content .pagination li:hover a, .pagination-content.ot-table-bg .pagination li:hover a, .pagination-content.table-color-col .pagination li:hover a {
  color: #645CBB;
}

.basic-table.pagination-content .pagination li:hover, .pagination-content.ot-table-bg .pagination li:hover, .pagination-content.table-color-col .pagination li:hover {
  border-color: #645CBB;
}

.selected {
 background: #645CBB;
}

.selected>a {
  color: white !important;
}

.table-top-componet .left-component {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

@media (max-width: 768px) {
  .table-top-componet .left-component {
    flex-direction: column;
  }
}

.table-top-componet .right-component a {
  float: right;
}

@media (max-width: 768px) {
  .table-top-componet .right-component a {
    float: left;
    margin-top: 20px;
  }
}

.ot-search-with-btns {
  display: flex;
  justify-content: space-between;
}

@media (max-width: 768px) {
  .ot-search-with-btns {
    flex-direction: column;
    justify-content: center;
    gap: 10px;
  }
}

.ot-search-with-btns .input-search-with-icon {
  position: relative;
  width: 200px;
  height: 40px;
}

.ot-table-head {
  display: flex;
  justify-content: space-between;
}

@media (max-width: 576px) {
  .ot-table-head {
    flex-direction: column;
    justify-content: center;
    gap: 10px;
  }
}

/* Accordion */


/* Signup, Signin */
.col-custom-height {
  min-height: 100vh;
}

.left-box .left-box-image img {
  max-width: 100%;
  max-height: 100%;
}

.left-box .title {
  font-weight: 700;
  font-size: 30px;
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.left-box .text {
  color: var(--ot-text-primary);
  font-weight: 400;
  line-height: 28px;
}

.form-wrapper .form-content {
  width: 360px;
  margin-bottom: 40px;
}

.form-wrapper .form-content .form-heading-text .title {
  font-weight: 700;
  font-size: 38px;
  line-height: 54px;
  color: var(--ot-text-title);
}

.form-wrapper .form-content .form-heading-text .text {
  color: var(--ot-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
}

.form-wrapper .form-content .form .input-field-focus {
  width: 360px;
}

.form-wrapper .form-content .form .input-field-focus label {
  color: var(--ot-text-title);
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  margin-bottom: 8px;
}

.form-wrapper .form-content .form .input-field-focus .password-input {
  position: relative;
}

.form-wrapper .form-content .form .input-field-focus .password-input i {
  position: absolute;
  display: flex;
  align-content: center;
  top: 50%;
  transform: translateY(-50%);
  right: 16px;
  color: var(--ot-text-primary);
  cursor: pointer;
}

.form-wrapper .form-content .form .input-field-focus input {
  width: 100%;
  height: 48px;
  padding: 16px;
  border: 1px solid #EAEAEA;
  border-radius: 7px;
  outline: none;
  color: #111314;
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
}

.form-wrapper .form-content .form .input-field-focus input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.form-wrapper .form-content .form .input-field-focus ::placeholder {
  color: #b2bec3;
  margin-top: 2px;
}

.form-wrapper .form-content .form .input-field-focus p {
  font-weight: 400;
  font-size: 10px;
  line-height: 14px;
}

.form-wrapper .form-content .form .remember-me .form-check-input {
  border: 2px solid #EAEAEA;
}

.form-wrapper .form-content .form .remember-me label {
  font-family: 'Lexend';
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  color: #1A1D1F;
  margin-left: 4px;
}

.form-wrapper .form-content .text-font {
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 24px;
  color: var(--ot-text-primary);
}

.form-wrapper .form-content .link-text {
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.form-wrapper .form-content .text-privacy-policy {
  font-weight: 500;
  font-size: 12px;
  line-height: 21px;
  color: var(--ot-text-primary);
}

.submit-button {
  width: 100%;
 background: #645CBB;
  border-radius: 5px;
  color: white;
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
}

.submit-button:hover {
  background: #F99417;
}

@media (max-width: 768px) {
  .frame {
    display: none !important;
  }

  .form-wrapper .form-content {
    width: 340px;
    padding: 20px;
  }

  .form-wrapper .form-content .form-heading-text .title {
    font-size: 30px;
  }

  .form-wrapper .form-content .form {
    width: 100%;
    gap: 10px;
  }

  .form-wrapper .form-content .form .input-field-focus {
    width: 100%;
  }

  .error-content {
    width: 410px !important;
    padding: 20px !important;
  }

  .error-content h1 {
    font-size: 28px !important;
  }
}

@media (max-width: 576px) {
  .form-wrapper .form-content {
    width: 320px;
    gap: 10px;
  }

  .form-wrapper .form-content .form-heading-text .title {
    font-size: 28px;
  }

  .form-wrapper .form-content .form-heading-text .text {
    font-size: 13px;
  }

  .form-wrapper .form-content .form .input-field-focus input {
    height: 40px;
    padding: 10px;
  }

  .form-wrapper .form-content .form label {
    font-size: 13px;
  }

  .social-media-content label {
    font-size: 13px;
  }

  .error-content {
    width: 320px !important;
    padding: 20px !important;
  }

  .error-content h1 {
    font-size: 24px !important;
  }
}

.fogotPassword {
  font-family: 'Lexend';
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  color: var(--ot-text-primary);
}

.social-media-content {
  width: 100%;
}

.social-media-content .social-media-items {
  width: 100%;
  height: 48px;
  padding: 16px;
  border: 1px solid #EAEAEA;
  border-radius: 5px;
  outline: none;
}

@media (max-width: 768px) {
  .social-media-content .social-media-items {
    font-family: 'Lexend';
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    text-align: center;
    color: #1A1D1F;
  }
}

.social-media-content .social-media-items:hover {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010F7;
}

.or-line {
  width: 45%;
  height: 1px;
  background-color: #EAEAEA;
  margin-top: 8px;
}

.or-text {
  color: var(--ot-text-primary);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 26px;
}

/* Error Page */
.error-wrapper {
  width: 100%;
  height: 100vh;
}

.error-wrapper .error-content {
  width: 486px;
}

.error-wrapper .error-content h1 {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 42px;
  margin-bottom: 0px;
}

.error-wrapper .error-content img {
  width: 100%;
}

.error-wrapper .error-content p {
  font-family: "Lexend";
  font-weight: 500;
  margin-bottom: 0px;
  color: #6f767e;
}

.error-wrapper .error-content .btn-group {
  margin-top: 28px;
  gap: 12px;
}

.error-wrapper .error-content .btn-group .time-count {
  padding: 2px;
  border-radius: 12px;
 background: #645CBB;
}

.error-wrapper .error-content .btn-group .time-count .count-btn {
  padding: 16px 13px;
  border-radius: 10px;
  background: #ffffff;
}

.error-wrapper .error-content .btn-group .time-count .count-btn h1 {
  font-family: "Lexend";
  font-weight: 700;
  font-size: 32px;
  line-height: 40px;
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.btn-back-to-homepage {
  width: 215px;
}

/* Card */
.basic-card .list-group {
  gap: 10px;
}

.basic-card .card-title {
  margin-bottom: 0;
}

.ot-card .ot-card-header {
  color: var(--ot-text-title);
}

.ot-card-b {
  background: var(--ot-bg-secondary);
}

.ot-card-b .card-header {
  color: var(--ot-text-title);
}

.customized .card-body {
  border-bottom: 1px solid #F0EEEE;
  border-left: 1px solid #F0EEEE;
  border-right: 1px solid #F0EEEE;
  border-radius: 0px 0px 5px 5px;
}

.customized .card:hover {
  box-shadow: 0px 5px 10px 0px lightgrey;
}

.customized-card .list-group {
  padding: 20px;
}

.customized-card .list-group li {
  background: var(--ot-bg-secondary);
  color: var(--ot-text-subtitle);
  list-style: none;
}

.no-border-card .list-group {
  gap: 10px;
}

.no-border-card .card {
  box-shadow: 0px 10px 20px 0px lightgrey;
}

.simple-card .list-group {
  gap: 10px;
}

/* Modal */
.information-modal .fa,
.confirmation-modal .fa {
  font-size: 28px;
}

.information-modal .modal-footer,
.confirmation-modal .modal-footer {
  border-top: none;
}

.modal-header-image {
  background-size: cover;
  background-image: url(../../assets/images/ModalHeader/Small.jpg);
  background-color: transparent;
  background-repeat: no-repeat;
}

.modal-content {
  border: none;
}

.btn-close {
  background: #ffffff52;
  border-radius: 50%;
}

.btn-gradian {
  color: white;
  border: none;
  font-weight: 500;
 background: #645CBB;
}

.ot-card-body {
  display: flex;
  flex-direction: row;
}

@media (max-width: 768px) {
  .ot-card-body {
    flex-direction: column;
    justify-content: center;
    gap: 10px;
  }
}

/* Alert */
.alert-section {
  background: #fff;
  padding: 80px;
  background: #FCFCFC;
  margin-top: 8px;
}

.alert-section h1 {
  font-weight: 700;
  font-size: 32px;
  text-transform: uppercase;
  color: #120D26;
}

.alert-section p {
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #4B4B4B;
}

.close-icon {
  float: right;
}

.basic-success-alert {
  padding: 15px;
  background-color: var(--ot-bg-badge-light-success);
  color: #29d697;
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.basic-success-alert .success-icon {
  color: #29d697;
}

.basic-secondary-alert {
  padding: 15px;
  background-color: var(--ot-bg-badge-light-primary);
  color: #4d5ee5;
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.basic-secondary-alert .secondary-icon {
  color: #4d5ee5;
}

.basic-warning-alert {
  padding: 15px;
  background-color: var(--ot-bg-badge-light-warning);
  color: #fdc400;
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.basic-warning-alert .warning-icon {
  color: #fdc400;
}

.basic-danger-alert {
  padding: 15px;
  background-color: var(--ot-bg-badge-light-danger);
  color: #ff6a54;
  font-family: 'Lexend';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.basic-danger-alert .danger-icon {
  color: #ff6a54;
}

.alert-success-describe h4, .alert-success-describe p {
  color: #29d697;
}

.alert-secondary-describe h4, .alert-secondary-describe p {
  color: #4d5ee5;
}

.alert-warning-describe h4, .alert-warning-describe p {
  color: #fdc400;
}

.alert-danger-describe h4, .alert-danger-describe p {
  color: #ff6a54;
}

/* Color Template */
.color-title {
  background-color: transparent;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
}

.color-title h4 {
  font-size: 24px;
}

.color-background {
  width: 100%;
  height: 150px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  /* Primary Blue */
  /* Secondary Cyan  */
  /* Success */
  /* Danger */
  /* Warning */
  /* Accent */
}

.color-background.bg-blue-primary {
  background: #4d5ee5;
}

.color-background.bg-blue-dark {
  background: #645CBB;
}

.color-background.bg-blue-tint {
  background: #8896ff;
}

.color-background.bg-blue-tint-light {
  background: #eef0ff;
}

.color-background.bg-cyan-primary {
  background: #00f8ff;
}

.color-background.bg-cyan-dark {
  background: #00dfe5;
}

.color-background.bg-cyan-tint {
  background: #4cfaff;
}

.color-background.bg-cyan-tint-light {
  background: #e5feff;
}

.color-background.bg-success-primary {
  background: #29d697;
}

.color-background.bg-success-dark {
  background: #24c087;
}

.color-background.bg-success-tint {
  background: #69e2b6;
}

.color-background.bg-success-tint-light {
  background: #e9faf4;
}

.color-background.bg-danger-primary {
  background: #ff6a54;
}

.color-background.bg-danger-dark {
  background: #e55f4b;
}

.color-background.bg-danger-tint {
  background: #ff9687;
}

.color-background.bg-danger-tint-light {
  background: #fff0ed;
}

.color-background.bg-warning-primary {
  background: #fdc400;
}

.color-background.bg-warning-dark {
  background: #e3b000;
}

.color-background.bg-warning-tint {
  background: #fdd54c;
}

.color-background.bg-warning-tint-light {
  background: #fef9e5;
}

.color-background.bg-accent-green {
  background: #93c782;
}

.color-background.bg-accent-blue {
  background: #4797ff;
}

.color-background.bg-accent-purple {
  background: #9d6fff;
}

.color-background.bg-accent-light-green {
  background: #b5e4ca;
}

.color-background.bg-accent-light-blue {
  background: #b1e5fc;
}

.color-background.bg-accent-light-purple {
  background: #cabdff;
}

.color-background.bg-accent-light-orange {
  background: #ffbc99;
}

.color-background.bg-info-dark {
  background: var(--info-dark);
}

.color-background.bg-info-main {
  background: var(--info-main);
}

.color-background.bg-info-tin1 {
  background: var(--info-tint-1);
}

.color-background.bg-info-tin2 {
  background: var(--info-tint-2);
}

.color-background.bg-warn-dark {
  background: var(--warn-dark);
}

.color-background.bg-warn-main {
  background: var(--warn-main);
}

.color-background.bg-warn-tin1 {
  background: var(--warn-tint-1);
}

.color-background.bg-warn-tin2 {
  background: var(--warn-tint-2);
}

.color-name-bg {
  background: var(--ot-bg-primary);
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.color-name-bg.color-primary-main {
  color: var(--blue-primary);
}

.color-name-bg span {
  color: var(--ot-text-primary);
}

.accent {
  width: 100px;
  height: 100px;
  border-radius: 5px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  position: relative;
}

.accent.accent-light-green {
  background: var(--accent-light-green);
}

.accent.accent-light-purple {
  background: var(--accent-light-purple);
}

.accent.accent-light-blue {
  background: var(--accent-light-blue);
}

.accent.accent-light-orange {
  background: var(--accent-light-orange);
}

.accent.accent-green {
  background: var(--accent-green);
}

.accent.accent-blue {
  background: var(--accent-blue);
}

.accent.accent-purple {
  background: var(--accent-purple);
}

.accent span {
  position: absolute;
  bottom: 10px;
  padding-left: 5px;
}

.typo {
  width: 100px;
  height: 100px;
  border-radius: 5px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  position: relative;
}

.typo.typo-black-dark {
  background: var(--typo-black-dark);
}

.typo.typo-black-main {
  background: var(--typo-black-main);
}

.typo.typo-black-bold {
  background: var(--typo-black-bold);
}

.typo.typo-black-light {
  background: var(--typo-black-light);
}

.typo.typo-black-lighter {
  background: var(--typo-black-lighter);
}

.typo span {
  position: absolute;
  bottom: 10px;
  padding-left: 5px;
}

.bgwhite {
  width: 100px;
  height: 100px;
  border-radius: 5px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  position: relative;
}

.bgwhite.bgwhite-dark {
  background: var(--bgwhite-dark);
}

.bgwhite.bgwhite-main {
  background: var(--bgwhite-main);
}

.bgwhite.bgwhite-tin1 {
  background: var(--bgwhite-tin1);
}

.bgwhite.bgwhite-tin2 {
  background: var(--bgwhite-tin2);
}

.bgwhite.typo-black-lighter {
  background: var(--typo-black-lighter);
}

.bgwhite span {
  position: absolute;
  bottom: 10px;
  padding-left: 5px;
}

/* Dropdown */
.dropdown-text-light {
  color: #6f767e;
}

.dropdown-text-dark {
  color: #1a1d1f;
}

.dropdown-text-red {
  color: #ff0022;
}

.dropdown-text-disable {
  color: #b2bec3;
}

.dropdown-text-blue {
  color: #645CBB;
}

.dropdown-content {
  margin-top: 24px;
  margin-left: 32px;
}

.dropdown-section {
  padding: 80px;
  background: #fcfcfc;
  margin-top: 8px;
}

.dropdown-section h1 {
  font-weight: 700;
  font-size: 32px;
  text-transform: uppercase;
  color: #120d26;
}

.dropdown-section p {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #4b4b4b;
}

.button-title {
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  color: #120d26;
}

.dropdown-with-down-arrow, .dropdown2-with-down-arrow {
  margin-top: 12px;
  background-color: #fcfcfc;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
  border: 2px solid #f0eeee;
  border-radius: 5px;
}

.dropdown-with-down-arrow i, .dropdown2-with-down-arrow i {
  margin-left: 12px;
  font-weight: bold;
}

.dropdown-with-down-arrow:hover, .dropdown2-with-down-arrow:hover {
  border: 2px solid #645CBB;
  color: #645CBB;
}

.dropdown-with-down-arrow:hover i, .dropdown2-with-down-arrow:hover i {
  color: #645CBB;
}

.dropdown-with-down-arrow:focus, .dropdown2-with-down-arrow:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

.dropdown-with-down-arrow:focus i, .dropdown2-with-down-arrow:focus i {
  color: #066ecf;
}

.dropdwon-btn-section {
  margin-top: 40px;
}

.dropdown-with-dots {
  margin-top: 12px;
}

button {
  border: none;
  outline: none;
}

.dropdown-menu {
  margin-top: 10px !important;
}

#dropdown {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 5px 0px 0px 5px;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}

#dropdown:hover {
  border: 2px solid #645CBB;
  color: #645CBB;
}

#dropdown:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

#three-dots {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 0px 5px 5px 0px;
  padding: 8px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}

#three-dots:hover {
  border: 2px solid #645CBB;
  color: #645CBB;
}

#three-dots:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

.dropdown-items, .second-item, .third-item {
  position: relative;
  width: 191px;
  background: #ffffff;
  border: 1px solid #f0eeee;
  border-radius: 7px;
}

.dropdown-items ul, .second-item ul, .third-item ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 16px;
  padding-right: 0px;
  gap: 16px;
}

.dropdown-items ul .text-secondary>i, .second-item ul .text-secondary>i, .third-item ul .text-secondary>i {
  padding-right: 16px;
}

.dropdown-items ul li, .second-item ul li, .third-item ul li {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  padding: 8px;
  padding-right: 24px;
}

.dropdown-items ul .dropdown-text-dark>i, .second-item ul .dropdown-text-dark>i, .third-item ul .dropdown-text-dark>i {
  padding-left: 16px;
}

.second-item {
  display: block;
  width: 180px;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  left: 100%;
  margin-top: -40px;
  transition: all ease-in-out 0.4s;
}

.third-item {
  display: none;
  width: 180px;
  position: absolute;
  left: 112%;
  margin-top: -66px;
}

ul li.dropdown-text-dark:hover .second-item {
  visibility: visible;
  opacity: 1;
  transition-delay: 0s;
  margin-left: 10px;
}

.dropdown-search-checkbox {
  margin-top: 12px;
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.02em;
  padding: 8px 16px;
  color: #645CBB;
}

.search-container {
  margin-top: 12px;
  width: 272px;
  background: #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
}

.search-input-checkbox input {
  width: 20px !important;
  height: 20px;
  border: 1px solid #eaeaea;
  border-radius: 5px;
}

.search-input-checkbox label {
  color: #6f767e;
  margin-left: 12px;
}

.search-input-checkbox ul li {
  display: flex;
}

.search-input {
  position: relative;
  text-align: center;
  padding: 15px 16px 15px 16px;
  border-bottom: 2px solid #f0eeee;
}

.search-input input {
  padding: 12px;
  width: 100%;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  background: #fafafa;
  outline: none;
}

.search-input ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-input i {
  position: absolute;
  top: 29px;
  right: 27px;
  padding: 5px;
  background-color: #fafafa;
  color: #6f767e;
}

.search-items {
  padding: 17px 25px;
}

.search-items ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.search-items ul li {
  color: #6f767e;
}

.search-input ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 8px 16px;
  gap: 16px;
}

.search-input ul li {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #6f767e;
}

.search-input ul li input {
  width: auto;
}

.search-input ul li ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-container .btn-items {
  padding: 5px 17px 25px;
  text-align: end;
}

.search-container .btn-items .btn {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.search-container .btn-items .btn.clear {
  color: #6f767e;
}

.input-default input, .input-date input {
  padding: 16px;
  width: 336px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  background: var(--ot-bg-secondary);
  color: var(--ot-text-title);
  outline: none;
}

.input-default ::placeholder, .input-date ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-field-focus {
  width: 360px;
}

.input-field-focus input {
  width: 100%;
  height: 48px;
  padding: 16px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  background: var(--ot-bg-secondary);
  color: var(--ot-text-title);
  outline: none;
}

.input-field-focus input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010f7;
  outline: none;
}

.input-field-focus ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-with-icon {
  position: relative;
  margin-top: 16px;
  width: 336px;
  height: 48px;
}

.input-with-icon input {
  width: 336px;
  height: 48px;
  padding: 16px 45px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}

.input-with-icon input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010f7;
  outline: none;
}

.input-with-icon ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-with-icon i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-with-icon i.fa-user-o {
  left: 20px;
}

.input-with-icon i.fa-search {
  right: 20px;
}

.input-pre-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}

.input-pre-post input {
  padding: 16px 45px;
  width: 296px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}

.input-pre-post ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-pre-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-pre-post i.fa-user-o {
  left: 20px;
}

.input-pre-post i.fa-search {
  right: 60px;
}

.input-pre-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0px 5px 5px 0px;
}

.input-https-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}

.input-https-post input {
  padding: 16px 43px 16px 110px;
  width: 296px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}

.input-https-post ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-https-post i.fa-user-o {
  left: 88px;
}

.input-https-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0px 5px 5px 0px;
}

.input-https-post .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0px 0px 5px;
}

.input-https-post2 {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}

.input-https-post2 input {
  padding: 16px 43px 16px 86px;
  width: 296px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}

.input-https-post2 ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post2 i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-https-post2 i.fa-user-o {
  left: 88px;
}

.input-https-post2 i.fa-search {
  right: 20px;
}

.input-https-post2 .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0px 0px 5px;
}

textarea {
  width: 280px;
  height: 79px;
  margin-top: 16px;
  padding: 12px 16px;
  background: #ffffff;
  border: 1px solid #eaeaea;
  border-radius: 7px;
  outline: none;
}

textarea::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.text-area {
  width: 280px;
  height: 79px;
}

.text-area .text-count {
  font-family: "Lexend";
  font-size: 10px;
  font-weight: 500;
  text-align: end;
  color: #b2bec3;
}

.Input-search-tab {
  position: relative;
  margin-top: 16px;
  width: 273px;
  height: 48px;
}

.Input-search-tab input {
  padding: 16px 43px 16px 16px;
  width: 100%;
  height: 100%;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}

.Input-search-tab ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.Input-search-tab i {
  position: absolute;
  top: 0;
  color: #6f767e;
  border-left: 2px solid #f0eeee;
  padding: 16px;
}

.Input-search-tab i.fa-search {
  right: 0px;
}

.Input-search-tab .search {
  position: absolute;
  color: #ffffff;
  top: 0px;
  right: 0px;
  background-color: #645CBB;
  padding: 13px;
  border-radius: 0px 5px 5px 0px;
}

.Input-search-tab i.fa-microphone {
  right: 70px;
  color: #645CBB;
  border: none;
}

.Input-search-tab.search-color>input {
  width: 289px;
}

.Input-search-tab.search-color>i {
  right: -15px;
  color: #fafafa;
  background-color: #645CBB;
  border-radius: 0px 5px 5px 0px;
}

.Input-search-tab .search-tab1 {
  width: 237px;
}

.Input-search-tab.microphone>input {
  padding: 16px 110px 16px 16px;
}

.input-date {
  margin-top: 16px;
}

.input-date input {
  width: 272px;
}

.Range-date {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 272px;
  height: 48px;
  flex-direction: row;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  margin-top: 16px;
  padding: 10px 16px;
  gap: 5px;
}

.Range-date input {
  border: none;
  outline: none;
}

.time-field {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex-direction: row;
  width: 386px;
  gap: 5px;
  margin-top: 16px;
}

.time-field .input-time {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 107px;
  height: 50px;
}

.time-field .input-time select {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: #1a1d1f;
  border: none;
  outline: none;
  padding: 11px 16px;
}

.time-field .select-time input {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 272px;
  height: 48px;
  outline: none;
  padding: 16px;
}

.input-group-start-end-time {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  justify-content: flex-end;
  flex-direction: row;
  gap: 10px;
}

.input-group-start-end-time .input-start-end-time {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 169px;
  height: 44px;
  padding: 12px 16px;
  color: #6f767e;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.input-group-start-end-time .input-start-end-time span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

option:hover {
  color: #645CBB;
}

.input-time-select {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-top: 16px;
}

.input-time-select .input-start-end-time-select {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 178px;
  height: 60px;
  padding: 12px 16px;
  color: #6f767e;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.input-time-select .input-start-end-time-select p {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  color: #0010f7;
}

.input-time-select .input-start-end-time-select h6 {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  color: #1a1d1f;
}

/* Badges */
.badge-success, .badge-danger, .badge-light-danger, .badge-warning, .badge-light-warning, .badge-primary, .badge-light-primary, .badge-light-success {
  padding: 4px 10px;
  background: var(--ot-bg-badge-success);
  border-radius: 5px;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 12px;
  font-weight: 600;
  line-height: 15px;
  color: var(--ot-text-badge-success);
}

.badge-success.ot-badge-circle, .ot-badge-circle.badge-danger, .ot-badge-circle.badge-light-danger, .ot-badge-circle.badge-warning, .ot-badge-circle.badge-light-warning, .ot-badge-circle.badge-primary, .ot-badge-circle.badge-light-primary, .ot-badge-circle.badge-light-success {
  width: 30px;
  height: 30px;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-danger, .badge-light-danger {
  background: var(--ot-bg-badge-danger);
  color: var(--ot-text-badge-danger);
}

.badge-warning, .badge-light-warning {
  background: var(--ot-bg-badge-warning);
  color: var(--ot-text-badge-warning);
}

.badge-primary, .badge-light-primary {
  background: var(--ot-bg-badge-primary);
  color: var(--ot-text-badge-primary);
}

.badge-light-success {
  background: var(--ot-bg-badge-light-success);
  color: var(--ot-text-badge-light-success);
}

.badge-light-danger {
  background: var(--ot-bg-badge-light-danger);
  color: var(--ot-text-badge-light-danger);
}

.badge-light-warning {
  background: var(--ot-bg-badge-light-warning);
  color: var(--ot-text-badge-light-warning);
}

.badge-light-primary {
  background: var(--ot-bg-badge-light-primary);
  color: var(--ot-text-badge-light-primary);
}

/* Global Components */
/* Reborting Default Bootstrap carousel */
.ot-carousel .carousel-control-prev-icon {
  background-image: url(../../../assets/images/icons/slider-left-arrow.svg);
}

.ot-carousel .carousel-control-next-icon {
  background-image: url(../../../assets/images/icons/slider-right-arrow.svg);
}

.ot-carousel .carousel-indicators button {
  background-color: var(--primary-color);
}

/* custom tooltip */
.custom-tooltip {
  --bs-tooltip-bg: var(--primary-color);
}

#rtl_change {
  position: fixed;
  top: 50%;
  right: 0px;
  transform: rotate(90deg);
}

.rtl #rtl_change {
  left: 0;
  right: auto;
}

/* Components */
.ot-pagination {
  margin-top: 40px;
}

.ot-pagination .pagination {
  margin: 0;
}

.ot-pagination .pagination .page-item {
  margin-right: 10px;
}

.ot-pagination .pagination .page-item:last-child {
  margin-right: 0;
}

.ot-pagination .pagination .page-item .page-link {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: var(--ot-text-table-pagination);
  border: 1px solid var(--ot-border-table-pagination);
  border-radius: 7px;
  padding: 8px 12px;
  background-color: var(--ot-bg-table-pagination);
}

.ot-pagination .pagination .page-item .page-link:hover {
  background-color: transparent;
}

.ot-pagination .pagination .page-item .page-link:focus {
  box-shadow: none;
  background-color: transparent;
}

.ot-pagination .pagination .page-item .page-link.active {
  background-image: linear-gradient(90deg, #0f6aff 0%, #645CBB 100%), linear-gradient(90deg, #0f6aff 0%, #645CBB 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  box-shadow: 2px 1000px 1px var(--ot-bg-table-pagination) inset;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #645CBB;
  border: 1px solid transparent;
}

.ot-pagination-b .pagination .page-item .page-link {
  background: var(--ot-bg-table-pagination);
  color: var(--ot-text-table-pagination);
}

.ot-pagination-b .pagination.active .page-link {
 background: #645CBB;
}

/* profile */
.profile-content .profile-menu-mobile {
  background-color: var(--ot-bg-profile-mobile-menu);
}

@media (min-width: 992px) {
  .profile-content .profile-menu-mobile {
    display: none;
  }
}

.profile-content .profile-menu-mobile .btn-menu-mobile {
  background-color: var(--ot-bg-profile-mobile-menu);
  padding: 16px 24px;
  width: 100%;
  display: block;
  text-align: left;
}

.profile-content .profile-menu-mobile .btn-menu-mobile .icon {
  font-size: 18px;
  color: var(--ot-text-title);
}

.profile-content .profile-menu-mobile .offcanvas {
  width: 100%;
  max-width: 300px;
  background-color: var(--ot-bg-profile-mobile-menu);
}

.profile-content .profile-menu-mobile .offcanvas .offcanvas-body {
  padding: 0;
}

.profile-content .profile-menu-mobile .offcanvas .offcanvas-header {
  justify-content: end;
}

.profile-content .profile-menu-mobile .offcanvas .offcanvas-header .btn-close {
  width: 20px;
  height: 20px;
  line-height: 20px;
}

.profile-content .profile-menu-mobile .offcanvas .offcanvas-header .btn-close:focus {
  box-shadow: none;
}

.profile-content .profile-menu-mobile .offcanvas .offcanvas-header .icon {
  font-size: 18px;
  color: var(--ot-text-title);
}

.profile-content .profile-menu-mobile .profile-menu {
  background-color: var(--ot-bg-profile-mobile-menu);
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 40px;
  border-right: 1px solid #eaeaea;
  flex: 0 0 300px;
  min-height: 900px;
}

.profile-content .profile-menu-mobile .profile-menu-head {
  padding: 40px 28px 0px 40px;
}

.profile-content .profile-menu-mobile .profile-menu-head .image-box {
  width: 60px;
  height: 60px;
}

.profile-content .profile-menu-mobile .profile-menu-head .body {
  margin-left: 12px;
}

.profile-content .profile-menu-mobile .profile-menu-head .body .title {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 18px;
  font-weight: 600;
  line-height: 16px;
  color: #1a1d1f;
  margin-bottom: 8px;
}

.profile-content .profile-menu-mobile .profile-menu-head .body .paragraph {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  color: #6f767e;
}

.profile-content .profile-menu-mobile .profile-menu-body .nav-item {
  margin-bottom: 35px;
  border-right: 2px solid transparent;
  border-radius: 2px 0px 0px 2px;
}

.profile-content .profile-menu-mobile .profile-menu-body .nav-item:last-child {
  margin-bottom: 0;
}

.profile-content .profile-menu-mobile .profile-menu-body .nav-link {
  padding: 0px 0px 0px 40px;
  border-right: 2px solid transparent;
  border-radius: 2px 0px 0px 2px;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 15px;
  font-weight: 500;
  line-height: 16px;
  color: #6f767e;
}

.profile-content .profile-menu-mobile .profile-menu-body .nav-link.active {
  color: #645CBB;
  border-right: 2px solid #645CBB;
}

.profile-content .profile-menu-mobile .profile-menu-body .nav-link:hover {
  color: #645CBB;
  border-right: 2px solid #645CBB;
}

.profile-content .profile-menu {
  background-color: var(--ot-bg-profile-menu);
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 40px;
  border-right: 1px solid var(--ot-border-primary);
  min-height: 900px;
  flex: 0 0 300px;
}

@media (max-width: 1399.98px) {
  .profile-content .profile-menu {
    flex: 0 0 240px;
  }
}

@media (max-width: 991.98px) {
  .profile-content .profile-menu {
    display: none;
  }
}

.profile-content .profile-menu-head {
  padding: 60px 28px 0px 60px;
}

@media (max-width: 1399.98px) {
  .profile-content .profile-menu-head {
    padding: 60px 16px 0px 16px;
  }
}

.profile-content .profile-menu-head .image-box {
  width: 60px;
  height: 60px;
}

.profile-content .profile-menu-head .body {
  margin-left: 12px;
}

@media (max-width: 1399.98px) {
  .profile-content .profile-menu-head .body {
    margin-left: 4px;
  }
}

.profile-content .profile-menu-head .body .title {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 18px;
  font-weight: 600;
  line-height: 16px;
  color: var(--ot-text-title);
  margin-bottom: 8px;
}

.profile-content .profile-menu-head .body .paragraph {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  color: #6f767e;
}

.profile-content .profile-menu-body .nav-item {
  margin-bottom: 35px;
  border-right: 2px solid transparent;
  border-radius: 2px 0px 0px 2px;
}

.profile-content .profile-menu-body .nav-item:last-child {
  margin-bottom: 0;
}

.profile-content .profile-menu-body .nav-link {
  padding: 0px 0px 0px 60px;
  border-right: 2px solid transparent;
  border-radius: 2px 0px 0px 2px;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 15px;
  font-weight: 500;
  line-height: 16px;
  color: #6f767e;
}

@media (max-width: 1399.98px) {
  .profile-content .profile-menu-body .nav-link {
    padding: 0px 0px 0px 16px;
  }
}

.profile-content .profile-menu-body .nav-link.active {
  color: #645CBB;
  border-right: 2px solid #645CBB;
}

.profile-content .profile-menu-body .nav-link:hover {
  color: #645CBB;
  border-right: 2px solid #645CBB;
}

.profile-content .profile-body {
  width: 0%;
  flex: 1;
  background-color: var(--ot-bg-profile-body);
  padding: 80px;
}

@media (max-width: 991.98px) {
  .profile-content .profile-body {
    width: unset;
  }
}

@media (max-width: 1399.98px) {
  .profile-content .profile-body {
    padding: 80px 24px 80px 24px;
  }
}

.profile-content .profile-body .btn-back {
  text-decoration: none;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: #ffffff;
  padding: 11px 16px;
  background: #645CBB;
  border-radius: 5px;
}

.profile-content .profile-body .title {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 30px;
  font-weight: 700;
  line-height: 38px;
  color: var(--ot-text-title);
  margin-bottom: 40px;
}

.profile-content .profile-body .nav {
  width: 100%;
  border-bottom: 1px solid var(--ot-border-primary);
}

.profile-content .profile-body .nav-item {
  margin-right: 40px;
}

@media (max-width: 1399.98px) {
  .profile-content .profile-body .nav-item {
    margin-right: 12px;
  }
}

.profile-content .profile-body .nav-item:last-child {
  margin-right: 0;
}

.profile-content .profile-body .nav-link {
  padding: 16px 0;
  border-radius: 0;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  color: #6f767e;
  border-bottom: 1px solid transparent;
}

.profile-content .profile-body .nav-link.active {
  background-color: transparent;
  color: #645CBB;
  border-bottom: 1px solid #645CBB;
}

.profile-content .profile-body .nav-link:hover {
  background-color: transparent;
  color: #645CBB;
  border-bottom: 1px solid #645CBB;
}

.profile-content .profile-body-form .form-item {
  padding: 32px 0px;
  border-bottom: 1px solid var(--ot-border-primary);
}

.profile-content .profile-body-form .image-box {
  width: 100px;
  height: 100px;
  position: relative;
}

.profile-content .profile-body-form .image-box .icon {
  position: absolute;
  top: unset;
  right: 0;
  bottom: 0;
  left: unset;
  width: 32px;
  height: 32px;
  font-size: 12px;
  line-height: 32px;
  border: none;
  border-radius: 200px;
  background-color: #ffffff;
  color: #645CBB;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
  cursor: pointer;
}

.profile-content .profile-body-form .image-box #input-button {
  position: abosulte;
  width: 1px;
  height: 1px;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px);
}

.profile-content .profile-body-form .title {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  color: var(--ot-text-title);
  margin-bottom: 8px;
}

.profile-content .profile-body-form .title .icon-required {
  font-size: 8px;
  color: #ff6a54;
}

.profile-content .profile-body-form .paragraph {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  color: #6f767e;
}

.profile-content .profile-body-form .btn-edit {
  background-color: transparent;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  color: #6f767e;
}

.profile-content .profile-body-form .form-box {
  margin-top: 12px;
}

.profile-content .profile-body-form .form-box .form-control, .profile-content .profile-body-form .form-box .form-select {
  max-width: 336px;
  max-height: 48px;
  border: 1px solid #eaeaea;
  border-radius: 5px;
}

.profile-content .profile-body-form .form-box .form-control::placeholder, .profile-content .profile-body-form .form-box .form-select::placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #6f767e;
}

.profile-content .profile-body-form .form-box .form-control:focus, .profile-content .profile-body-form .form-box .form-select:focus {
  border-color: #eaeaea;
  box-shadow: none;
}

.profile-content .profile-body-form .form-box .unset-size {
  max-width: unset;
  max-height: unset;
}

.profile-content .profile-body-form .form-box .btn-update {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: #ffffff;
  padding: 11px 20px;
  background: #645CBB;
  border-radius: 5px;
  margin-top: 12px;
}

.profile-content .profile-body-form .form-box .daterange {
  cursor: pointer;
  max-width: 336px;
  max-height: 48px;
  border: 1px solid #eaeaea;
}

.profile-content .profile-body-form .form-box .file-box {
  padding: 28px;
  background: #ffffff;
  border: 1px dashed #eaeaea;
  border-radius: 10px;
}

.profile-content .profile-body-form .form-box .file-box .icon {
  font-size: 28px;
  color: #645CBB;
}

.profile-content .profile-body-form .form-box .file-box .file-upload-text {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  color: #000000;
}

.profile-content .profile-body-form .form-box .file-box .file-info-text {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  color: #6f767e;
}

.profile-content .profile-body-form .form-box .file-box #open-file-uploader {
  color: #645CBB;
}

.profile-content .profile-body-form .form-box .file-box #input-button {
  position: abosulte;
  width: 1px;
  height: 1px;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px);
}

.profile-content .profile-body-form .form-box .file-show-box {
  margin-top: 16px;
}

.profile-content .profile-body-form .form-box .file-show-box .img-box {
  width: 64px;
  height: 64px;
}

.profile-content .profile-body-form .form-box .file-show-box .img-box img {
  width: 100%;
  height: 100%;
}

.profile-content .profile-body.padding-reduce {
  padding: 80px 40px;
}

@media (max-width: 1399.98px) {
  .profile-content .profile-body.padding-reduce {
    padding: 80px 24px;
  }
}

.profile-table-content .table-toolbar {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  color: #000000;
}

.profile-table-content .table-toolbar .form-select {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  color: var(--ot-text-title);
  width: auto;
  padding: 10px 30px 10px 14px;
  border: 1px solid var(--ot-border-table-toolbar-per-page);
  border-radius: 5px;
  background-image: url("../images/basic-datatable/show/down-icon.svg");
  background-repeat: no-repeat;
  background-position: right 14px center;
  background-size: 12px;
  outline: 0;
  box-shadow: none;
  background-color: var(--ot-bg-table-toolbar-per-page);
}

.profile-table-content .table-toolbar .btn-add {
  text-decoration: none;
  padding: 10px 17px;
  border: 1px solid #645CBB;
 background: #645CBB;
  border-radius: 5px;
  display: block;
  text-align: center;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: #ffffff;
}

.profile-table-content .table-toolbar .btn-daterange, .profile-table-content .table-toolbar .dropdown-designation .btn-designation, .profile-table-content .table-toolbar .dropdown-export .btn-export {
  background-image: linear-gradient(90deg, #0f6aff 0%, #645CBB 100%), linear-gradient(90deg, #0f6aff 0%, #645CBB 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  box-shadow: 2px 1000px 1px var(--ot-bg-table-toolbar-btn-outline-primary) inset;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: var(--ot-text-primary);
  border: 1px solid transparent;
  padding: 10px 12px;
  border-radius: 5px;
  transition: all ease-in-out 0.1s;
}

.profile-table-content .table-toolbar .btn-daterange .icon, .profile-table-content .table-toolbar .dropdown-designation .btn-designation .icon, .profile-table-content .table-toolbar .dropdown-export .btn-export .icon {
  color: #645CBB;
  font-size: 14px;
}

.profile-table-content .table-toolbar .btn-daterange:hover, .profile-table-content .table-toolbar .dropdown-designation .btn-designation:hover, .profile-table-content .table-toolbar .dropdown-export .btn-export:hover {
  box-shadow: none;
 background: #645CBB;
  color: #ffffff;
}

.profile-table-content .table-toolbar .btn-daterange:hover .icon, .profile-table-content .table-toolbar .dropdown-designation .btn-designation:hover .icon, .profile-table-content .table-toolbar .dropdown-export .btn-export:hover .icon {
  color: #ffffff;
}

.profile-table-content .table-toolbar .btn-daterange:focus, .profile-table-content .table-toolbar .dropdown-designation .btn-designation:focus, .profile-table-content .table-toolbar .dropdown-export .btn-export:focus {
  box-shadow: none;
 background: #645CBB;
  color: #ffffff;
}

.profile-table-content .table-toolbar .btn-daterange:focus .icon, .profile-table-content .table-toolbar .dropdown-designation .btn-designation:focus .icon, .profile-table-content .table-toolbar .dropdown-export .btn-export:focus .icon {
  color: #ffffff;
}

.profile-table-content .table-toolbar .search-box {
  position: relative;
  border: 1px solid var(--ot-border-table-toolbar-search);
  border-radius: 5px;
}

.profile-table-content .table-toolbar .search-box .form-control, .profile-table-content .table-toolbar .search-box .profile-content .profile-body-form .form-box .form-select, .profile-content .profile-body-form .form-box .profile-table-content .table-toolbar .search-box .form-select {
  padding: 8px 45px 8px 16px;
  border: none;
  box-shadow: none;
  background-color: var(--ot-bg-table-toolbar-search);
  color: var(--ot-text-title);
}

.profile-table-content .table-toolbar .search-box .form-control::placeholder, .profile-table-content .table-toolbar .search-box .profile-content .profile-body-form .form-box .form-select::placeholder, .profile-content .profile-body-form .form-box .profile-table-content .table-toolbar .search-box .form-select::placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

.profile-table-content .table-toolbar .search-box .icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  color: #6f767e;
}

.profile-table-content .table-toolbar .dropdown-designation .dropdown-menu {
  position: relative;
  background: #ffffff;
  border: 1px solid #eaeaea;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  margin-top: 10px !important;
  padding: 0;
}

.profile-table-content .table-toolbar .dropdown-designation .search-content {
  padding: 20px;
}

.profile-table-content .table-toolbar .dropdown-designation .search-content .search-box {
  position: relative;
  border: 1px solid #eaeaea;
  border-radius: 50px;
}

.profile-table-content .table-toolbar .dropdown-designation .search-content .search-box .form-control, .profile-table-content .table-toolbar .dropdown-designation .search-content .search-box .profile-content .profile-body-form .form-box .form-select, .profile-content .profile-body-form .form-box .profile-table-content .table-toolbar .dropdown-designation .search-content .search-box .form-select {
  width: 232px;
  padding: 8px 45px 8px 16px;
  border: none;
  border-radius: 50px;
  box-shadow: none;
  background-color: #ffffff;
  color: #1a1d1f;
}

.profile-table-content .table-toolbar .dropdown-designation .search-content .search-box .form-control::placeholder, .profile-table-content .table-toolbar .dropdown-designation .search-content .search-box .profile-content .profile-body-form .form-box .form-select::placeholder, .profile-content .profile-body-form .form-box .profile-table-content .table-toolbar .dropdown-designation .search-content .search-box .form-select::placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

.profile-table-content .table-toolbar .dropdown-designation .search-content .search-box .icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  color: #6f767e;
}

.profile-table-content .table-toolbar .dropdown-designation .list {
  padding: 26px;
  margin: 0;
}

.profile-table-content .table-toolbar .dropdown-designation .list .list-item {
  list-style: none;
  margin-bottom: 24px;
}

.profile-table-content .table-toolbar .dropdown-designation .list .list-item:last-child {
  margin-bottom: 0;
}

.profile-table-content .table-toolbar .dropdown-designation .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #6f767e;
}

.profile-table-content .table-toolbar .dropdown-designation .dropdown-item:hover {
  background-color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-designation .dropdown-item:focus {
  background-color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-designation .dropdown-item:active {
  background-color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-designation .dropdown-item .icon {
  color: var(--primary-color);
  font-size: 14px;
}

.profile-table-content .table-toolbar .dropdown-action .btn-dropdown {
  border: 1px solid var(--ot-border-table-toolbar-btn-action);
  width: 40px;
  height: 40px;
  background: var(--ot-bg-table-toolbar-btn-action);
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  border-radius: 50px;
  color: var(--primary-color);
  font-size: 16px;
}

.profile-table-content .table-toolbar .dropdown-action .btn-dropdown:focus {
 background: #645CBB;
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-menu {
  position: relative;
  background: #ffffff;
  border: 1px solid #eaeaea;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  padding: 24px;
  margin-top: 10px !important;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-menu::before {
  content: "";
  position: absolute;
  top: -5px;
  right: 8px;
  background: #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  transform: rotate(45deg);
  padding: 10px;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-menu::after {
  content: "";
  position: absolute;
  top: 0;
  right: 5px;
  background: #ffffff;
  padding: 10px 15px;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-menu li {
  margin-bottom: 20px;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-menu li:last-child {
  margin-bottom: 0;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: #6f767e;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-item:hover {
  background-color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-item:focus {
  background-color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-item:active {
  background-color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-action .dropdown-item .icon {
  color: var(--primary-color);
  font-size: 14px;
}

.profile-table-content .table-toolbar .dropdown-export .dropdown-menu {
  position: relative;
  background: #ffffff;
  border: 1px solid #eaeaea;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  padding: 24px;
  margin-top: 10px !important;
}

.profile-table-content .table-toolbar .dropdown-export .dropdown-menu li {
  margin-bottom: 20px;
}

.profile-table-content .table-toolbar .dropdown-export .dropdown-menu li:last-child {
  margin-bottom: 0;
}

.profile-table-content .table-toolbar .dropdown-export .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: #6f767e;
}

.profile-table-content .table-toolbar .dropdown-export .dropdown-item:hover {
  background-color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-export .dropdown-item:focus {
  background-color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-export .dropdown-item:active {
  background-color: #ffffff;
}

.profile-table-content .table-toolbar .dropdown-export .dropdown-item .icon {
  color: var(--primary-color);
  font-size: 14px;
}

.profile-table-content .table {
  border-color: var(--ot-border-table);
  vertical-align: middle;
  margin: 0;
}

.profile-table-content .table .sorting_asc, .profile-table-content .table-content.table-basic .table .sorting_desc, .table-content.table-basic .profile-table-content .table .sorting_desc, .profile-table-content .table .sorting_desc {
  position: relative;
}

.profile-table-content .table .sorting_asc::before, .profile-table-content .table-content.table-basic .table .sorting_desc::before, .table-content.table-basic .profile-table-content .table .sorting_desc::before, .profile-table-content .table .sorting_desc::before {
  position: absolute;
  left: auto;
  right: 16px;
  content: "\f106";
  font-family: "Font Awesome 6 Free";
  font-size: 12px;
  font-weight: 900;
  top: 42%;
  color: var(--ot-color-table-icon-sorting-asc-up);
  cursor: pointer;
  transform: translateY(-42%);
}

.profile-table-content .table .sorting_asc::after, .profile-table-content .table-content.table-basic .table .sorting_desc::after, .table-content.table-basic .profile-table-content .table .sorting_desc::after, .profile-table-content .table .sorting_desc::after {
  position: absolute;
  left: auto;
  right: 16px;
  content: "\f107";
  font-family: "Font Awesome 6 Free";
  font-size: 12px;
  font-weight: 900;
  top: 62%;
  color: var(--ot-color-table-icon-sorting-asc-down);
  cursor: pointer;
  transform: translateY(-62%);
}

.profile-table-content .table .sorting_desc::before {
  color: var(--ot-color-table-icon-sorting-desc-up);
}

.profile-table-content .table .sorting_desc::after {
  color: var(--ot-color-table-icon-sorting-desc-down);
}

.profile-table-content .table .check-box .form-check-input {
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 1px solid var(--ot-border-table-checkbox);
  background-color: var(--ot-bg-table-checkbox);
}

.profile-table-content .table .check-box .form-check-input:focus {
  box-shadow: none;
}

.profile-table-content .table .check-box .form-check-input:checked {
  background: url("../images/basic-datatable/check/ok.svg"), linear-gradient(90deg, #0f6aff 0%, #645CBB 100%);
  background-repeat: no-repeat;
  background-position: center;
}

.profile-table-content .table .thead {
  background: var(--ot-bg-table-thead);
  border-bottom-color: var(--ot-border-table-thead);
}

.profile-table-content .table .thead tr th {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: var(--ot-text-title);
  padding: 16px 32px 16px 16px;
  vertical-align: middle;
  white-space: nowrap;
  border-color: var(--ot-border-table-thead);
}

.profile-table-content .table .tbody tr:nth-of-type(odd) {
  background: var(--ot-bg-table-tbody);
}

.profile-table-content .table .tbody tr td {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 12px;
  font-weight: 500;
  line-height: 21px;
  color: var(--ot-text-primary);
  padding: 16px;
  vertical-align: middle;
  white-space: nowrap;
}

.profile-table-content .table .dropdown-action .btn-dropdown {
  border: 1px solid var(--ot-border-table-toolbar-btn-action);
  width: 28px;
  height: 28px;
  background: var(--ot-bg-table-toolbar-btn-action);
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  border-radius: 50px;
  color: var(--primary-color);
  font-size: 10px;
}

.profile-table-content .table .dropdown-action .btn-dropdown:focus {
 background: #645CBB;
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  color: #ffffff;
}

.profile-table-content .table .dropdown-action .dropdown-menu {
  background: #ffffff;
  border: 1px solid #eaeaea;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  padding: 24px;
  margin-top: 10px !important;
}

.profile-table-content .table .dropdown-action .dropdown-menu li {
  margin-bottom: 20px;
}

.profile-table-content .table .dropdown-action .dropdown-menu li:last-child {
  margin-bottom: 0;
}

.profile-table-content .table .dropdown-action .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: #6f767e;
}

.profile-table-content .table .dropdown-action .dropdown-item:hover {
  background-color: #ffffff;
}

.profile-table-content .table .dropdown-action .dropdown-item:focus {
  background-color: #ffffff;
}

.profile-table-content .table .dropdown-action .dropdown-item:active {
  background-color: #ffffff;
}

.profile-table-content .table .dropdown-action .dropdown-item .icon {
  color: var(--primary-color);
  font-size: 14px;
}

/* Inputs */
/* Custom Input field */
.ot-input {
  background-color: var(--ot-bg-secondary) !important;
  border: 1px solid var(--ot-border-primary) !important;
  outline: none !important;
  color: var(--ot-text-title) !important;
  /* padding: 16px !important; */
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
}

.ot-input:focus-visible, .ot-input:focus {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #645CBB !important;
  outline: none;
}

.ot-input::placeholder {
  color: #b2bec3;
}


/* Email Template */
.email-template {
  font-family: "Lexend", sans-serif;
  text-align: center;
  padding: 56px 60px;
  background: white;
  border-radius: 5px;
  max-width: 600px;
  /* Template Buttton start */
  /* Template Buttton end */
}

.email-template .template-heading h1 {
  font-family: "Lexend", sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 34px;
  margin-top: 20px;
}

.email-template .template-heading p {
  font-family: "Lexend", sans-serif;
  font-size: 16px;
  line-height: 24px;
  color: #6f767e;
  margin-top: 20px;
}

.email-template .template-heading .color-black {
  color: #1a1d1f;
}

.email-template .template-body {
  font-family: "Lexend", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #6f767e;
  padding: 14px;
}

.email-template .template-body .content-part {
  text-align: left;
  margin-bottom: 28px;
}

.email-template .template-body .content-part p a {
  font-family: "Lexend", sans-serif;
  color: var(--primary-color);
}

.email-template .template-body .content-part h5 {
  font-family: "Lexend", sans-serif;
  color: #1a1d1f;
  margin-top: 28px;
  padding: 0;
}

.email-template .template-body .content-details p {
  font-family: "Lexend", sans-serif;
  padding: 0 14px;
  margin-bottom: 28px;
}

.email-template .template-body .content-details p .link {
  color: var(--primary-color);
}

.email-template .template-body .ot-primary-text {
  font-family: "Lexend", sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: var(--primary-color);
  margin-top: 26px;
}

.email-template .template-body h4 {
  font-family: "Lexend", sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #29d697;
}

.email-template .template-body h5 {
  font-family: "Lexend", sans-serif;
  padding: 0 14px;
}

.email-template .template-button-group {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 14px;
  gap: 10px;
}

.email-template .template-button-group .template-btn {
  padding: 9px 2px;
  border-radius: 7px;
 background: #645CBB;
}

.email-template .template-button-group .template-btn span {
  font-family: "Lexend", sans-serif;
  padding: 10px 16px;
  font-weight: 600;
  color: white;
 background: #645CBB;
}

.email-template .template-button-group .template-btn span:hover {
  outline: none;
  border: none;
  color: var(--primary-color);
  border-radius: 5px;
  background: white;
}

.email-template .template-btn-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.email-template .template-btn-container .template-btn {
  padding: 9px 2px;
  border-radius: 7px;
 background: #645CBB;
}

.email-template .template-btn-container .template-btn span {
  font-family: "Lexend", sans-serif;
  padding: 10px 16px;
  font-weight: 600;
  color: white;
 background: #645CBB;
}

.email-template .template-btn-container .template-btn span:hover {
  outline: none;
  border: none;
  color: var(--primary-color);
  border-radius: 5px;
  background: white;
}

.email-template .template-footer {
  font-family: "Lexend", sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #6f767e;
  border-top: 1px solid #dfe6e9;
  margin-top: 26px;
}

.email-template .template-footer p>a {
  color: var(--primary-color);
}

.email-template .template-footer .social-media-button {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 26px;
  gap: 8px;
}

.email-template .template-footer .social-media-button a {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8.5px;
  border-radius: 50%;
 background: #645CBB;
}

.email-template .template-footer .social-media-button a:hover {
  background: #645CBB;
}

.email-template .template-footer .template-footer-image {
  margin-top: 28px;
  margin-bottom: 8px;
}

@media (max-width: 576px) {
  .email-template {
    padding: 26px 30px;
  }

  .email-template .template-heading h1 {
    font-size: 20px;
    padding: 0 10px;
  }

  .email-template .template-heading p {
    font-size: 16px;
    padding: 0 8px;
  }

  .email-template .template-body {
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #6f767e;
  }

  .email-template .template-body p {
    padding: 0;
  }

  .email-template .template-body .template-content-image img {
    width: 100%;
    height: 100%;
  }

  .email-template .template-body h5 {
    padding: 0;
  }

  .email-template .template-button-group {
    flex-direction: column;
    padding: 0;
  }

  .email-template .template-button-group button {
    width: 100%;
  }

  .email-template .template-footer {
    font-size: 7px;
  }
}

/* Notification */
.ot-notification-contents {
  display: flex;
  gap: 10px;
}

.notification-container {
  width: calc(100% - 340px);
}

.notification-container .notification-col .card {
  padding: 24px;
  background: var(--ot-bg-secondary);
  border-radius: 8px;
  border: none;
}

.notification-container .notification-col .card .card-head .card-title {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
  color: var(--ot-text-title);
}

.notification-container .notification-col .card .card-head .item-selector .select-items {
  width: 98px;
  height: 34px;
  border: 2px solid #efefef;
  border-radius: 5px;
  background: var(--ot-bg-secondary);
  color: var(--ot-text-title);
  outline: none;
}

.notification-container .notification-col .card .card-head .item-selector .select-items:hover {
  border: 2px solid #645CBB;
}

.notification-container .notification-col .card .card-head .item-selector .action .btn-action {
  padding: 8px 10px;
  background: var(--ot-bg-secondary);
  border: 2px solid var(--ot-bg-secondary);
  border-radius: 50px;
}

.notification-container .notification-col .card .card-head .item-selector .action .btn-action .fa {
  color: var(--ot-text-primary);
}

.notification-container .notification-col .card .card-head .item-selector .action .btn-action:hover {
  border: 2px solid #645CBB;
}

.notification-container .notification-col .card .notification-list {
  padding: 24px 16px;
}

.notification-container .notification-col .card .notification-list .notification-img {
  position: relative;
}

.notification-container .notification-col .card .notification-list .notification-img img {
  width: 100%;
}

.notification-container .notification-col .card .notification-list .notification-img .item-icon-badge {
  position: absolute;
  top: 41px;
  right: 0;
  padding: 5px;
  border-radius: 50%;
  background: #645CBB;
  color: var(--ot-bg-secondary);
  display: flex;
  justify-content: center;
  align-items: center;
}

.notification-container .notification-col .card .notification-list .notification-img .item-icon-badge .fa {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.notification-container .notification-col .card .notification-list .notification-details .notification-line .notification-subtitle {
  color: var(--ot-text-title);
}

.notification-container .notification-col .card .notification-list .notification-details .notification-line .notification-login {
  color: var(--ot-text-primary);
}

.notification-container .notification-col .card .notification-list .notification-details .notification-line .notification-time {
  color: var(--ot-text-primary);
}

.notification-container .notification-col .card .notification-list .notification-details .notification-comment {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 500;
  color: var(--ot-text-title);
}

.notification-container .notification-col .card .notification-list .notification-details .notification-content {
  color: var(--ot-text-primary);
}

.notification-container .notification-col .card .notification-list .notification-details .notification-content strong {
  color: var(--ot-text-title);
}

.notification-container .notification-col .card .notification-list .notification-details .notification-control .notification-action {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 19px;
  background: none;
  margin-top: 16px;
  color: var(--ot-text-title);
}

.notification-container .notification-col .card .notification-list .notification-details .liked-action {
  color: #645CBB !important;
}

.notification-container .notification-col .card .notification-list .notification-details .notification-line .notification-time .status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  min-width: 10px;
  min-height: 10px;
  background-color: #645CBB;
}

.notification-container .notification-col .card .notification-list .notification-details .answer .user-answer img {
  margin-top: 16px;
  margin-right: 16px;
}

.notification-container .notification-col .card .notification-list .notification-details .answer .user-answer textarea {
  width: 75%;
  color: var(--ot-text-title);
  background: var(--ot-bg-secondary);
}

.notification-container .notification-col .card .notification-list .notification-details .answer .btn-answer {
  margin-left: 66px;
}

.notification-container .notification-col .card .notification-list .notification-details .answer .btn-answer .btn.btn-reply {
  font-family: "Lexend";
  font-weight: 600;
  line-height: 18px;
  padding: 9px 18px;
 background: #645CBB;
  color: white;
}

.notification-container .notification-col .card .notification-list .notification-details .answer .btn-answer .btn.btn-replyhover {
  opacity: var(--ot-bg-secondary-opacity);
}

.notification-container .notification-col .card .notification-list .notification-details .answer .btn-answer .btn.btn-cancel {
  font-family: "Lexend";
  font-weight: 600;
  line-height: 18px;
  padding: 9px 14px;
  background: var(--ot-bg-secondary);
  color: var(--ot-text-title);
  border: 2px solid #efefef;
}

.notification-container .notification-col .card .notification-list .notification-details .answer .btn-answer .btn.btn-cancelhover {
  opacity: var(--ot-bg-secondary-opacity);
}

.notification-container .notification-col .card .notification-list:hover {
  border-radius: 5px;
  background: var(--ot-bg-primary);
}

.notification-container .notification-col .card .load-more {
  border: 2px solid #f4f4f4;
  border-radius: 5px;
  padding: 8px 14px;
}

.notification-container .notification-col .card .load-more img {
  width: 14px;
  height: 14px;
}

.notification-container .notification-col .card .load-more button {
  color: var(--ot-text-title);
  background: none;
}

.notification-container .notification-col .card .load-more:hover {
  border: 2px solid #645CBB;
}

.filter-container {
  width: 340px;
}

.filter-container .filter {
  position: fixed;
  padding: 24px;
  background: var(--ot-bg-secondary);
  border-radius: 8px;
  border: none;
}

.filter-container .filter .title-filter {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
  color: var(--ot-text-title);
}

.filter-container .filter .btn-select {
  display: flex;
  gap: 16px;
  justify-content: center;
  align-items: center;
}

.filter-container .filter .input-checkbox .form-check {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 0;
}

.filter-container .filter .input-checkbox .form-check label {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  color: var(--ot-text-title);
  margin-left: 5px;
}

.filter-container .filter .input-user .form-check .form-check-label {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  color: var(--ot-text-title);
  margin-left: 5px;
}

@media (max-width: 1200px) {
  .notification-container {
    width: 100%;
  }

  .notification-container .notification-col .card .notification-list {
    padding: 24px 10px;
  }

  .filter-container {
    display: none;
  }
}

@media (max-width: 768px) {
  .notification-container {
    width: 100%;
  }

  .notification-container .notification-col .card {
    padding: 10px;
  }

  .notification-container .notification-col .card .card-head .card-title {
    font-size: 16px;
  }

  .notification-container .notification-col .card .notification-list .notification-details .notification-control .notification-action {
    font-size: 10px;
  }

  .notification-container .notification-col .card .notification-list .notification-img .item-icon-badge {
    top: 41px;
    padding: 3px;
  }

  .notification-container .notification-col .card .notification-list .notification-img .item-icon-badge .fa {
    font-size: 10px;
    color: #ffffff;
  }
}

@media (max-width: 576px) {
  .notification-container {
    width: 100%;
  }

  .notification-container .notification-col .card {
    padding: 10px;
  }

  .notification-container .notification-col .card .notification-list .notification-details {
    font-size: 11px;
  }

  .notification-container .notification-col .card .notification-list .notification-details .answer .user-answer img {
    width: 30px;
    margin-right: 5px;
  }

  .notification-container .notification-col .card .notification-list .notification-details .answer .user-answer textarea::placeholder {
    font-size: 10px !important;
  }

  .notification-container .notification-col .card .notification-list .notification-details .answer .btn-answer {
    display: flex;
    gap: 5px;
    margin-left: 0px;
  }

  .notification-container .notification-col .card .notification-list .notification-details .answer .btn-answer .btn {
    font-size: 10px;
    height: 30px;
  }

  .notification-container .notification-col .card .notification-list .notification-details .answer .btn-answer .btn.btn-cancel,
  .notification-container .notification-col .card .notification-list .notification-details .answer .btn-answer .btn.btn-reply {
    line-height: 0px;
    padding: 0 10px;
  }

  .notification-container .notification-col .card .notification-list .notification-img img {
    width: 45px;
  }

  .notification-container .notification-col .card .notification-list .notification-img .item-icon-badge {
    top: 34px;
    padding: 3px;
    display: flex;
  }
}

/* Form Elements */
/* Form Elements */
.input-check-radio .form-check .form-check-input {
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 1px solid var(--ot-border-table-checkbox);
  background-color: var(--ot-bg-table-checkbox);
  margin-top: 0;
}

.input-check-radio .form-check .form-check-input:focus {
  box-shadow: none;
}

.input-check-radio .form-check .form-check-input:checked {
  background: url("../images/basic-datatable/check/ok.svg"), linear-gradient(90deg, var(--primary-color) 0%, var(--primary-color) 100%);
  background-repeat: no-repeat;
  background-position: center;
}

.bootstrap-tagsinput {
  background: var(--ot-bg-secondary);
}

.bootstrap-tagsinput span.badge.input-bg-primary {
 background: #645CBB;
}

.bootstrap-tagsinput .badge [data-role="remove"]:after {
  display: inline-block;
}

/* Pricing table */

/* FAQ */
.faq .faq-card {
  background: var(--ot-bg-tertiary);
  margin-bottom: 10px;
}

.faq .faq-card .card {
  margin-bottom: 10px;
  border: 1px solid #eeeeee;
  border-radius: 2px;
}

.faq .faq-card .card .card svg {
  position: absolute;
  left: 25px;
}

.faq .faq-card .card .card-header {
  background: var(--ot-bg-secondary);
}

.faq .faq-card .card .card-header h5 button {
  width: 100%;
  background: var(--ot-bg-secondary);
  padding: 10px 20px;
  text-align: left;
  font-family: "Lexend";
  font-size: 16px;
  text-decoration: none;
  font-weight: 500;
  color: var(--ot-text-title);
}

.faq .faq-card .card .card-body {
  background: var(--ot-bg-secondary);
  border-top: 1px solid #eeeeee;
}

.faq .faq-card .card .card-body p {
  font-family: "Lexend";
  font-size: 14px;
  padding: 10px 20px;
  color: var(--ot-text-primary);
}

.faq .faq-card .card:hover {
  border: 1px solid blue;
}

.faq .faq-card .faq-title h6 {
  font-size: 20px;
  margin: 30px 0px 30px 0px;
}

.typography-content .table thead {
  color: var(--ot-text-title);
}

.typography-content .table tbody {
  color: var(--ot-text-subtitle);
}

.typography-content .highlight,
.typography-content .bd-example {
  color: var(--ot-text-subtitle);
}

.typography-content .vertical-alignment .row {
  min-height: 50px;
  background-color: rgba(15, 106, 255, 0.1);
  margin-bottom: 6px;
}

.typography-content .vertical-alignment .col {
  background-color: rgba(15, 106, 255, 0.1);
  border: 1px solid rgba(15, 106, 255, 0.25);
}

/* FAQ classic*/
.faq-card .ot-accordion-cust .accordion-item {
  margin-bottom: 11px;
  border: 1px solid var(--ot-bg-badge-primary);
  border-radius: 2px;
}

.faq-card .ot-accordion-cust .accordion-item .accordion-header {
 background: #645CBB;
}

.faq-card .ot-accordion-cust .accordion-item .accordion-header .accordion-button {
 background: #645CBB;
  color: #ffffff;
}

.faq-card .ot-accordion-cust .accordion-item .accordion-header .accordion-button:focus {
  box-shadow: none;
}

.faq-card .ot-accordion-cust .accordion-item .accordion-body {
  background: var(--ot-bg-secondary);
  color: var(--ot-text-primary);
}

.faq-card .ot-accordion-border .accordion-item {
  margin-bottom: 10px;
  border: 1px solid var(--ot-bg-badge-primary);
}

.faq-card .ot-accordion-border .accordion-item .accordion-header .accordion-button {
  background: var(--ot-bg-secondary);
  color: var(--ot-text-badge-light-primary);
}

.faq-card .ot-accordion-border .accordion-item .accordion-header .accordion-button:focus {
  box-shadow: none;
}

.faq-card .ot-accordion-border .accordion-item .accordion-body {
  background: var(--ot-bg-secondary);
  color: var(--ot-text-primary);
}

.faq-card .accordion-no-border .accordion-item {
  margin-bottom: 10px;
  border: 1px solid #ffffff;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.1);
}

.faq-card .accordion-no-border .accordion-item .accordion-header {
  background: var(--ot-bg-tertiary);
}

.faq-card .accordion-no-border .accordion-item .accordion-header .accordion-button {
  background: var(--ot-bg-secondary);
  color: var(--ot-text-subtitle);
}

.faq-card .accordion-no-border .accordion-item .accordion-header .accordion-button:focus {
  box-shadow: none;
}

.faq-card .accordion-no-border .accordion-item .accordion-body {
  background: var(--ot-bg-secondary);
  color: var(--ot-text-primary);
}

/* Pricing Table*/
.pricing-table-container {
  background: var(--ot-bg-secondary);
  padding: 100px 136px;
}

@media (max-width: 1200px) {
  .pricing-table-container {
    padding: 10%;
  }
}

@media (max-width: 576px) {
  .pricing-table-container {
    padding: 5%;
  }
}

.pricing-table-container .pricing-header {
  background: var(--ot-bg-pricing-table-gradient);
  padding: 81px 0px;
}

.pricing-table-container .pricing-header p {
  font-family: "Lexend";
  font-size: 14px;
  font-weight: 600;
  color: var(--ot-text-primary);
}

.pricing-table-container .pricing-header h1 {
  font-family: "Lexend";
  margin-top: 0px;
  text-align: center;
  font-size: 48px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 1200px) {
  .pricing-table-container .pricing-header h1 {
    font-size: 32px;
  }
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-header h1 {
    font-size: 24px;
  }
}

.pricing-table-container .pricing-header .btn-switch-content {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 44px 0px;
  width: 230px;
  height: 49px;
  border-radius: 100px;
 background: #645CBB;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-header .btn-switch-content {
    width: 193px;
    height: 39px;
  }
}

.pricing-table-container .pricing-header .btn-switch-content .btn-switch {
  font-family: "Lexend";
  background: var(--ot-bg-pricing-table-gradient);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 225px;
  height: 44px;
  padding: 2px;
  border-radius: 102px;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-header .btn-switch-content .btn-switch {
    width: 190px;
    height: 35px;
  }
}

.pricing-table-container .pricing-header .btn-switch-content .btn-switch input {
  display: none;
}

.pricing-table-container .pricing-header .btn-switch-content .btn-switch label {
  padding: 0px 34px;
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-header .btn-switch-content .btn-switch label {
    padding: 0px 17px;
  }
}

.pricing-table-container .pricing-header .btn-switch-content .btn-switch label:hover {
  cursor: pointer;
}

.pricing-table-container .pricing-header .btn-switch-content .btn-switch label:first-of-type {
  border-radius: 4px 0 0 4px;
}

.pricing-table-container .pricing-header .btn-switch-content .btn-switch label:last-of-type {
  border-radius: 0 4px 4px 0;
}

.pricing-table-container .pricing-header .btn-switch-content .btn-switch input:checked+label {
  padding: 10px 28px;
  color: #ffffff;
  -webkit-text-fill-color: white;
  border-radius: 100px;
 background: #645CBB;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-header .btn-switch-content .btn-switch input:checked+label {
    padding: 6px 28px;
  }
}

.pricing-table-container .pricing-table-body {
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (max-width: 1200px) {
  .pricing-table-container .pricing-table-body {
    flex-direction: column;
  }
}

.pricing-table-container .pricing-table-body .basic {
  margin-top: -75px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
}

@media (max-width: 1200px) {
  .pricing-table-container .pricing-table-body .basic {
    margin-top: 0px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list {
  background: var(--ot-bg-secondary);
}

.pricing-table-container .pricing-table-body .pricing-list:hover {
  background: var(--ot-bg-pricing-table-hover);
  box-shadow: 0px 4px 20px rgba(39, 39, 39, 0.05);
}

.pricing-table-container .pricing-table-body .pricing-list ul {
  padding: 40px;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul {
    padding: 10%;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul .popular-title {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  color: #e9bc7a;
  background: #fffaf3;
  border-radius: 50px;
  padding: 10px 0;
  margin-bottom: 32px;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul .popular-title {
    font-size: 10px;
    padding: 5px 0;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li {
  list-style: none;
}

.pricing-table-container .pricing-table-body .pricing-list ul li h1 {
  font-family: "Lexend";
  font-size: 38px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li h1 {
    font-size: 24px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li p {
  font-family: "Lexend";
  font-size: 14px;
  font-weight: 500;
  color: var(--ot-text-primary);
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li p {
    font-size: 12px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li .btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
 background: #645CBB;
  border-radius: 50px;
  padding: 3px;
  margin: 28px 0px 32px 0px;
}

.pricing-table-container .pricing-table-body .pricing-list ul li .btn-container:hover {
  background: #645CBB;
}

.pricing-table-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-list {
  width: 100%;
  padding: 16px;
  border-radius: 50px;
  background: var(--ot-bg-secondary);
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-list {
    padding: 10px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-list span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-list span {
    font-size: 12px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-start {
  width: 100%;
  padding: 16px;
  border-radius: 50px;
  background: transparent;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-start {
    padding: 10px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-start span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  color: #ffffff;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-start span {
    font-size: 12px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li h2 {
  font-family: "Lexend";
  font-size: 20px;
  font-weight: 600;
  color: var(--ot-text-primary);
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li h2 {
    font-size: 16px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li h2 span {
  font-family: "Lexend";
  font-size: 38px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li h2 span {
    font-size: 24px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li h5 {
  font-family: "Lexend";
  font-size: 16px;
  font-weight: 600;
  color: var(--ot-text-title);
  margin-top: 12px;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li h5 {
    font-size: 14px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li h6 {
  font-family: "Lexend";
  font-weight: 500;
  font-size: 14px;
  color: var(--ot-text-primary);
  margin-top: 8px;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li h6 {
    font-size: 12px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li .facility-detail ul {
  padding: 0px 40px;
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li .facility-detail ul {
    padding: 0 10%;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li .facility-detail ul li {
  font-family: "Lexend";
  font-weight: 500;
  font-size: 14px;
  margin-top: 20px;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .pricing-table-container .pricing-table-body .pricing-list ul li .facility-detail ul li {
    font-size: 12px;
  }
}

.pricing-table-container .pricing-table-body .pricing-list ul li .facility-detail ul li::before {
  content: "\2022";
  background: linear-gradient(90deg, #0f6aff 0%, #645CBB 30%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  display: inline-block;
  width: 1em;
  /* margin-left: -1em; */
}



/* Pricing Table 2*/
.pricing-container {
  background: var(--ot-bg-secondary);
  padding: 120px 136px;
}

@media (max-width: 1200px) {
  .pricing-container {
    padding: 10%;
  }
}

@media (max-width: 576px) {
  .pricing-container {
    padding: 5%;
  }
}

.pricing-container .pricing-header {
  background: var(--ot-bg-secondary);
}

.pricing-container .pricing-header p {
  font-family: "Lexend";
  font-size: 14px;
  font-weight: 600;
  color: var(--ot-text-primary);
}

.pricing-container .pricing-header h1 {
  font-family: "Lexend";
  margin-top: 0px;
  text-align: center;
  font-size: 48px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 1200px) {
  .pricing-container .pricing-header h1 {
    font-size: 32px;
  }
}

@media (max-width: 576px) {
  .pricing-container .pricing-header h1 {
    font-size: 24px;
  }
}

.pricing-container .pricing-header .btn-switch-content {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 26px 0px;
  width: 230px;
  height: 49px;
  border-radius: 100px;
  background: #f8f7fc;
}

@media (max-width: 576px) {
  .pricing-container .pricing-header .btn-switch-content {
    width: 229px;
    height: 39px;
  }
}

.pricing-container .pricing-header .btn-switch-content .btn-switch {
  font-family: "Lexend";
  background: #f8f7fc;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 225px;
  height: 44px;
  padding: 2px;
  cursor: pointer;
  border-radius: 102px;
}

@media (max-width: 576px) {
  .pricing-container .pricing-header .btn-switch-content .btn-switch {
    width: 225px;
    height: 35px;
  }
}

.pricing-container .pricing-header .btn-switch-content .btn-switch input {
  display: none;
}

.pricing-container .pricing-header .btn-switch-content .btn-switch label {
  padding: 0px 29px;
  color: var(--ot-text-primary);
  transition: all 0.1s ease-in-out;
}

.pricing-container .pricing-header .btn-switch-content .btn-switch label:hover {
  cursor: pointer;
}

.pricing-container .pricing-header .btn-switch-content .btn-switch label:first-of-type {
  border-radius: 4px 0 0 4px;
}

.pricing-container .pricing-header .btn-switch-content .btn-switch label:last-of-type {
  border-radius: 0 4px 4px 0;
}

.pricing-container .pricing-header .btn-switch-content .btn-switch input:checked+label {
  padding: 12px 33px;
  color: #0f6aff !important;
  background: #ffffff;
  border-radius: 100px;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.151);
}

@media (max-width: 576px) {
  .pricing-container .pricing-header .btn-switch-content .btn-switch input:checked+label {
    padding: 7px 33px;
  }
}

.pricing-container .pricing-table-body {
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (max-width: 1200px) {
  .pricing-container .pricing-table-body {
    flex-direction: column;
  }
}

.pricing-container .pricing-table-body .pricing-list {
  background: var(--ot-bg-secondary);
}

.pricing-container .pricing-table-body .pricing-list ul {
  padding: 40px 20px;
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul {
    padding: 10% 5%;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li {
  list-style: none;
}

.pricing-container .pricing-table-body .pricing-list ul li .top-box {
  border: 1px solid #eaeaea;
  border-radius: 7px;
}

.pricing-container .pricing-table-body .pricing-list ul li .top-box ul {
  padding: 40px;
}

.pricing-container .pricing-table-body .pricing-list ul li h1 {
  font-family: "Lexend";
  font-size: 24px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li h1 {
    font-size: 24px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li p {
  font-family: "Lexend";
  font-size: 14px;
  font-weight: 500;
  color: var(--ot-text-primary);
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li p {
    font-size: 12px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li .btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
 background: #645CBB;
  border-radius: 50px;
  padding: 3px;
  margin: 28px 0px 0px 0px;
}

.pricing-container .pricing-table-body .pricing-list ul li .btn-container:hover {
  background: #645CBB;
}

.pricing-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-list {
  width: 100%;
  padding: 14px;
  border-radius: 50px;
  background: var(--ot-bg-secondary);
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-list {
    padding: 10px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-list span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-list span {
    font-size: 12px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-start {
  width: 100%;
  padding: 16px;
  border-radius: 50px;
  background: transparent;
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-start {
    padding: 10px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-start span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  color: #ffffff;
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li .btn-container .btn-pricing-start span {
    font-size: 12px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li h2 {
  font-family: "Lexend";
  font-size: 20px;
  font-weight: 600;
  color: var(--ot-text-primary);
  margin-top: 20px;
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li h2 {
    font-size: 16px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li h2 span {
  font-family: "Lexend";
  font-size: 38px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li h2 span {
    font-size: 24px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li h5 {
  font-family: "Lexend";
  font-size: 16px;
  font-weight: 600;
  color: var(--ot-text-title);
  margin-top: 12px;
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li h5 {
    font-size: 14px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li h6 {
  font-family: "Lexend";
  font-weight: 500;
  font-size: 14px;
  color: var(--ot-text-primary);
  margin-top: 8px;
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li h6 {
    font-size: 12px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li .facility-detail ul {
  padding: 0px;
}

.pricing-container .pricing-table-body .pricing-list ul li .facility-detail ul li {
  font-family: "Lexend";
  font-weight: 500;
  font-size: 14px;
  margin-top: 20px;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .pricing-container .pricing-table-body .pricing-list ul li .facility-detail ul li {
    font-size: 12px;
  }
}

.pricing-container .pricing-table-body .pricing-list ul li .facility-detail ul li i {
  background: linear-gradient(90deg, #0f6aff 0%, #645CBB 30%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-right: 13px;
  font-size: 16px;
}


.rtl .pricing-container .pricing-table-body .pricing-list ul li .facility-detail ul li i{
  margin-left: 13px;
  margin-right: auto !important;
}


/* Pricing Table 3*/
.simple-pricing-container {
  background: var(--ot-bg-secondary);
  padding: 100px 10%;
}

@media (max-width: 1400px) {
  .simple-pricing-container {
    padding: 10%;
  }
}

@media (max-width: 576px) {
  .simple-pricing-container {
    padding: 5%;
  }
}

.simple-pricing-container .pricing-header {
  padding: 20px 0px 60px 0px;
}

.simple-pricing-container .pricing-header p {
  font-family: "Lexend";
  font-size: 14px;
  font-weight: 600;
  color: var(--ot-text-primary);
}

.simple-pricing-container .pricing-header h1 {
  font-family: "Lexend";
  margin-top: 0px;
  text-align: center;
  font-size: 48px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 1200px) {
  .simple-pricing-container .pricing-header h1 {
    font-size: 32px;
  }
}

@media (max-width: 576px) {
  .simple-pricing-container .pricing-header h1 {
    font-size: 24px;
  }
}

.simple-pricing-container .pricing-table-body {
  width: 100%;
  padding: 0px 56px;
}

@media (max-width: 1200px) {
  .simple-pricing-container .pricing-table-body {
    flex-direction: column;
    padding: 0px;
  }
}

.simple-pricing-container .pricing-table-body .top-box {
  display: flex;
  border: 1px solid #eaeaea;
  border-radius: 7px;
}

@media (max-width: 768px) {
  .simple-pricing-container .pricing-table-body .top-box {
    flex-direction: column;
  }
}

.simple-pricing-container .pricing-table-body .top-box .product-details {
  background: var(--ot-bg-secondary);
  padding: 40px;
  border-right: 1px solid #eaeaea;
  border-radius: 6px 0px 0px 6px;
}

.simple-pricing-container .pricing-table-body .top-box .product-details h1 {
  font-family: "Lexend";
  font-size: 24px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .simple-pricing-container .pricing-table-body .top-box .product-details h1 {
    font-size: 18px;
  }
}

.simple-pricing-container .pricing-table-body .top-box .product-details p {
  font-family: "Lexend";
  font-size: 14px;
  font-weight: 500;
  color: var(--ot-text-primary);
}

@media (max-width: 576px) {
  .simple-pricing-container .pricing-table-body .top-box .product-details p {
    font-size: 12px;
  }
}

.simple-pricing-container .pricing-table-body .top-box .product-details h3 {
  font-family: 'Lexend';
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: var(--ot-text-title);
}

.simple-pricing-container .pricing-table-body .top-box .product-details .facility-detail {
  display: flex;
  flex-direction: row;
  gap: 30px;
}

@media (max-width: 1200px) {
  .simple-pricing-container .pricing-table-body .top-box .product-details .facility-detail {
    flex-direction: column;
    gap: 0px;
  }

  .simple-pricing-container .pricing-table-body .top-box .product-details .facility-detail ul {
    margin-bottom: 0px;
  }
}

.simple-pricing-container .pricing-table-body .top-box .product-details .facility-detail ul {
  padding: 0px;
}

.simple-pricing-container .pricing-table-body .top-box .product-details .facility-detail ul li {
  list-style: none;
  font-family: "Lexend";
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  margin-top: 20px;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .simple-pricing-container .pricing-table-body .top-box .product-details .facility-detail ul li {
    font-size: 12px;
  }
}

.simple-pricing-container .pricing-table-body .top-box .product-details .facility-detail ul li i {
  background: linear-gradient(90deg, #0f6aff 0%, #645CBB 30%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-right: 13px;
  font-size: 16px;
}

.rtl .simple-pricing-container .pricing-table-body .top-box .product-details .facility-detail ul li i {
  margin-left: 13px;
  margin-right: auto !important;
}

.simple-pricing-container .pricing-table-body .top-box .btn-container {
  padding: 50px;
  background: var(--ot-bg-pricing-table-gradient);
  border-radius: 0px 6px 6px 0px;
}

.simple-pricing-container .pricing-table-body .top-box .btn-container h2 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  height: 60px;
  font-family: "Lexend";
  font-size: 16px;
  font-weight: 500;
  color: var(--ot-text-primary);
}

@media (max-width: 576px) {
  .simple-pricing-container .pricing-table-body .top-box .btn-container h2 {
    font-size: 16px;
  }
}

.simple-pricing-container .pricing-table-body .top-box .btn-container h2 .price {
  font-family: "Lexend";
  font-size: 48px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .simple-pricing-container .pricing-table-body .top-box .btn-container h2 .price {
    font-size: 24px;
  }
}

.simple-pricing-container .pricing-table-body .top-box .btn-container p {
  font-family: "Lexend";
  font-size: 14px;
  font-weight: 500;
  color: var(--ot-text-primary);
  text-align: center;
}

@media (max-width: 576px) {
  .simple-pricing-container .pricing-table-body .top-box .btn-container p {
    font-size: 12px;
  }
}

.simple-pricing-container .pricing-table-body .top-box .btn-container .btn-content {
  display: flex;
  justify-content: center;
  align-items: center;
 background: #645CBB;
  border-radius: 50px;
  padding: 3px;
  margin-top: 40px;
}

.simple-pricing-container .pricing-table-body .top-box .btn-container .btn-content:hover {
  background: #645CBB;
}

.simple-pricing-container .pricing-table-body .top-box .btn-container .btn-content .btn-pricing-start {
  width: 100%;
  padding: 16px;
  border-radius: 50px;
  background: transparent;
}

@media (max-width: 576px) {
  .simple-pricing-container .pricing-table-body .top-box .btn-container .btn-content .btn-pricing-start {
    padding: 10px;
  }
}

.simple-pricing-container .pricing-table-body .top-box .btn-container .btn-content .btn-pricing-start span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  color: #ffffff;
}

@media (max-width: 576px) {
  .simple-pricing-container .pricing-table-body .top-box .btn-container .btn-content .btn-pricing-start span {
    font-size: 12px;
  }
}

/* Pricing Table 4*/
.ot-pricing-table {
  display: block;
  margin-top: 50px;
}

.ot-pricing-table .table tbody tr .data-title {
  font-family: 'Lexend';
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
  color: var(--ot-text-title);
  background: var(--ot-bg-table-tbody);
}

.ot-pricing-table .table tbody tr td {
  font-family: 'Lexend';
  font-weight: 400;
  font-size: 12px;
  line-height: 21px;
  color: var(--ot-text-primary);
}

.ot-pricing-table .table tbody tr td i.la-check {
  color: #29D697;
  font-size: 24px;
}

.ot-pricing-table .table tbody tr td i.la-minus {
  color: #FF6A54;
  font-size: 24px;
}

.ot-pricing-table .table tbody tr td .pricing-list ul {
  padding: 40px;
  margin: 0px;
}

.ot-pricing-table .table tbody tr td .pricing-list ul li {
  list-style: none;
}

.ot-pricing-table .table tbody tr td .pricing-list ul li h1 {
  font-family: "Lexend";
  font-size: 24px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .ot-pricing-table .table tbody tr td .pricing-list ul li h1 {
    font-size: 24px;
  }
}

.ot-pricing-table .table tbody tr td .pricing-list ul li p {
  font-family: "Lexend";
  font-size: 14px;
  font-weight: 500;
  color: var(--ot-text-primary);
}

@media (max-width: 576px) {
  .ot-pricing-table .table tbody tr td .pricing-list ul li p {
    font-size: 12px;
  }
}

.ot-pricing-table .table tbody tr td .pricing-list ul li h4 {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: var(--ot-text-primary);
}

.ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
 background: #645CBB;
  border-radius: 50px;
  padding: 3px;
  margin: 28px 0px 0px 0px;
}

.ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container:hover {
  background: #645CBB;
}

.ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container .btn-pricing-list {
  width: 100%;
  padding: 14px;
  border-radius: 50px;
  background: var(--ot-bg-secondary);
}

@media (max-width: 576px) {
  .ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container .btn-pricing-list {
    padding: 10px;
  }
}

.ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container .btn-pricing-list span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (max-width: 576px) {
  .ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container .btn-pricing-list span {
    font-size: 12px;
  }
}

.ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container .btn-pricing-start {
  width: 100%;
  padding: 16px;
  border-radius: 50px;
  background: transparent;
}

@media (max-width: 576px) {
  .ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container .btn-pricing-start {
    padding: 10px;
  }
}

.ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container .btn-pricing-start span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  color: #ffffff;
}

@media (max-width: 576px) {
  .ot-pricing-table .table tbody tr td .pricing-list ul li .btn-container .btn-pricing-start span {
    font-size: 12px;
  }
}

.ot-pricing-table .table tbody tr td .pricing-list ul li h2 {
  font-family: "Lexend";
  font-size: 20px;
  font-weight: 600;
  color: var(--ot-text-primary);
  margin: 30px 0px 4px 0px;
}

@media (max-width: 576px) {
  .ot-pricing-table .table tbody tr td .pricing-list ul li h2 {
    font-size: 16px;
  }
}

.ot-pricing-table .table tbody tr td .pricing-list ul li h2 span {
  font-family: "Lexend";
  font-size: 38px;
  font-weight: 700;
  color: var(--ot-text-title);
}

@media (max-width: 576px) {
  .ot-pricing-table .table tbody tr td .pricing-list ul li h2 span {
    font-size: 24px;
  }
}

/* Icon page*/
.icon-container .row .col:hover .theme-icons {
 background: #645CBB;
  color: #ffffff;
}

.cursor-pointer {
  cursor: pointer;
}

.theme-icons {
  background-color: var(--ot-bg-secondary);
  color: var(--primary-color);
}

.font-22 {
  font-size: 22px;
}

/* eCommerce Components */
/*
    eCommerce Component

    1) Product List
    2) Product Grid

*/
.ecommerce-components {
  /* Product List Start */
  /* Product List End */
  /* Product Grid Start */
  /* Product Grid End */
}

.ecommerce-components.product-list .product-image {
  width: 80px;
  max-height: 80px;
  background-color: var(--ot-bg-primary);
}

.ecommerce-components.product-list .product-image img {
  width: 100%;
  height: 100%;
  padding: 10px;
}

.ecommerce-components.product-grid .product-card img {
  max-height: 95px;
}

.ecommerce-components.order-details .icon-box {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 1px solid #c7c8cb;
  font-size: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.ecommerce-components.order-details .icon-box.customer {
  background-color: rgba(52, 96, 255, 0.15) !important;
}

.ecommerce-components.order-details .icon-box.order-info {
  background-color: rgba(18, 191, 35, 0.15) !important;
}

.ecommerce-components.order-details .icon-box.deliver-to {
  background-color: rgba(231, 46, 46, 0.15) !important;
}

.dark-theme .ecommerce-components.order-details .icon-box {
  background-color: #fff !important;
}

/* Chat */
.chat-container .card {
  background: var(--ot-bg-tertiary);
  transition: 0.5s;
  border: 0;
  margin-bottom: 30px;
  border-radius: 0.55rem;
  position: relative;
  width: 100%;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
}

.chat-container .chat-app .people-list {
  width: 280px;
  position: absolute;
  left: 0;
  top: 0;
  padding: 20px;
  z-index: 7;
  -moz-transition: 0.5s;
  -o-transition: 0.5s;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

.chat-container .chat-app .people-list img {
  float: left;
  border-radius: 50%;
}

.chat-container .chat-app .people-list .about {
  float: left;
  padding-left: 8px;
}

.chat-container .chat-app .people-list .status {
  color: #999;
  font-size: 13px;
}

.chat-container .chat-app .people-list .chat-list li {
  padding: 10px 15px;
  list-style: none;
  border-radius: 3px;
}

.chat-container .chat-app .people-list .chat-list li:hover {
  background-color: var(--ot-bg-primary);
  cursor: pointer;
}

.chat-container .chat-app .people-list .chat-list li.active {
  background-color: var(--ot-bg-primary);
}

.chat-container .chat-app .people-list .chat-list li .name {
  font-size: 15px;
}

.chat-container .chat-app .people-list .chat-list img {
  width: 45px;
  border-radius: 50%;
}

.chat-container .chat-app .chat {
  margin-left: 280px;
  border-left: 1px solid #eaeaea;
}

.chat-container .chat-app .chat .chat-header {
  padding: 15px 20px;
  border-bottom: 2px solid #f4f7f6;
}

.chat-container .chat-app .chat .chat-header img {
  float: left;
  border-radius: 40px;
  width: 40px;
}

.chat-container .chat-app .chat .chat-header .dropdown button {
  background-color: transparent;
  padding: 0;
  border: none;
  outline: none;
  font-size: 22px;
  color: var(--ot-text-title);
}

.chat-container .chat-app .chat .chat-header .dropdown button:after {
  content: none;
}

.chat-container .chat-app .chat .chat-header .chat-about {
  float: left;
  padding-left: 10px;
}

.chat-container .chat-app .chat .chat-history {
  padding: 20px;
  border-bottom: 2px solid #fff;
}

.chat-container .chat-app .chat .chat-history ul {
  padding: 0;
}

@media (max-width: 576px) {
  .chat-container .chat-app .chat .chat-history ul {
    max-height: 400px;
    overflow-y: scroll;
  }
}

.chat-container .chat-app .chat .chat-history ul li {
  list-style: none;
  margin-bottom: 30px;
}

.chat-container .chat-app .chat .chat-history ul li:last-child {
  margin-bottom: 0px;
}

.chat-container .chat-app .chat .chat-history .message-data {
  margin-bottom: 15px;
}

.chat-container .chat-app .chat .chat-history .message-data img {
  border-radius: 40px;
  width: 40px;
}

.chat-container .chat-app .chat .chat-history .message-data-time {
  color: #434651;
  padding-left: 6px;
}

.chat-container .chat-app .chat .chat-history .message {
  color: #444;
  padding: 18px 20px;
  line-height: 26px;
  font-size: 16px;
  border-radius: 7px;
  display: inline-block;
  position: relative;
}

.chat-container .chat-app .chat .chat-history .message:after {
  bottom: 100%;
  left: 7%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-bottom-color: var(--ot-bg-primary);
  border-width: 10px;
  margin-left: -10px;
}

.chat-container .chat-app .chat .chat-history .my-message {
  color: var(--ot-text-title);
  background: var(--ot-bg-primary);
}

.chat-container .chat-app .chat .chat-history .my-message:after {
  bottom: 100%;
  left: 30px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-bottom-color: var(--ot-bg-primary);
  border-width: 10px;
  margin-left: -10px;
}

.chat-container .chat-app .chat .chat-history .other-message {
  background: var(--ot-bg-primary);
  color: var(--ot-text-title);
  text-align: right;
}

.chat-container .chat-app .chat .chat-history .other-message:after {
  border-bottom-color: var(--ot-bg-primary);
  left: 93%;
}

.chat-container .chat .chat-message-container {
  padding: 20px;
  width: 100%;
}

.chat-container .chat .chat-message-container .chat-message {
  background-color: var(--ot-bg-primary) !important;
  border: 1px solid #f0eeee !important;
  outline: none !important;
  color: var(--ot-text-primary) !important;
  border-radius: 5px;
}

.chat-container .chat .chat-message-container .chat-message .message-input textarea {
  resize: none;
  background-color: transparent;
  border: 0;
  outline: none;
  margin-top: 0;
  color: var(--ot-text-primary);
}

.chat-container .chat .chat-message-container .chat-message .message-toolbar {
  padding: 0 10px 5px 10px;
}

.chat-container .chat .chat-message-container .chat-message .message-toolbar .toolbar-btns button {
  padding: 0;
  border: 0;
  outline: none;
  font-size: 22px;
  margin-right: 8px;
  color: var(--ot-text-primary);
}

.chat-container .chat .chat-message-container .chat-message .message-toolbar .toolbar-btns button:active, .chat-container .chat .chat-message-container .chat-message .message-toolbar .toolbar-btns button:hover, .chat-container .chat .chat-message-container .chat-message .message-toolbar .toolbar-btns button:focus {
  outline: none;
  border: 0;
  color: var(--ot-text-subtitle);
}

.chat-container .online,
.chat-container .offline,
.chat-container .me {
  margin-right: 2px;
  font-size: 8px;
  vertical-align: middle;
}

.chat-container .online {
  color: #86c541;
}

.chat-container .offline {
  color: #e47297;
}

.chat-container .me {
  color: #1d8ecd;
}

.chat-container .float-right {
  float: right;
}

.chat-container .clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

@media only screen and (max-width: 767px) {
  .chat-container .chat-app .people-list {
    height: 465px;
    width: 100%;
    overflow-x: auto;
    background: #fff;
    left: -400px;
    display: none;
  }

  .chat-container .chat-app .people-list.open {
    left: 0;
  }

  .chat-container .chat-app .chat {
    margin: 0;
  }

  .chat-container .chat-app .chat .chat-header {
    border-radius: 0.55rem 0.55rem 0 0;
  }

  .chat-container .chat-app .chat-history {
    height: 300px;
    overflow-x: auto;
  }
}

@media only screen and (min-width: 768px) and (max-width: 992px) {
  .chat-container .chat-app .chat-list {
    height: 650px;
    overflow-x: auto;
  }

  .chat-container .chat-app .chat-history {
    height: 600px;
    overflow-x: auto;
  }
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: landscape) and (-webkit-min-device-pixel-ratio: 1) {
  .chat-container .chat-app .chat-list {
    height: 480px;
    overflow-x: auto;
  }

  .chat-container .chat-app .chat-history {
    height: calc(100vh - 350px);
    overflow-x: auto;
  }
}

/* Carousel */
/* slick carousel */
.slick-slider .slick-prev:before,
.slick-slider .slick-next:before {
  color: #0f0f0f;
}

.slick-slider .slick-slide {
  padding: 0 8px;
}

/* Timeline */
/* timeline start */
/*==================================
    TIMELINE
==================================*/
/*-- GENERAL STYLES
    ------------------------------*/
.timeline {
  line-height: 1.4em;
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
}

/*----- TIMELINE ITEM -----*/
.timeline-item {
  padding-left: 40px;
  position: relative;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-item .timeline-info span {
  color: var(--ot-text-primary);
}

/*----- TIMELINE INFO -----*/
.timeline-info {
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 3px;
  margin: 0 0 0.5em 0;
  text-transform: uppercase;
  white-space: nowrap;
}

/*----- TIMELINE MARKER -----*/
.timeline-marker {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 15px;
}

.timeline-marker:before {
  background: var(--ot-dot-timeline);
  border: 3px solid transparent;
  border-radius: 100%;
  content: "";
  display: block;
  height: 15px;
  position: absolute;
  top: 4px;
  left: 0;
  width: 15px;
  transition: background 0.3s ease-in-out, border 0.3s ease-in-out;
}

.timeline-marker:after {
  content: "";
  width: 3px;
  background: #ccd5db;
  display: block;
  position: absolute;
  top: 24px;
  bottom: 0;
  left: 6px;
}

.timeline-item:last-child .timeline-marker:after {
  content: none;
}

.timeline-item:not(.period):hover .timeline-marker:before {
  background: transparent;
  border: 3px solid var(--ot-dot-timeline);
}

/*----- TIMELINE CONTENT -----*/
.timeline-content {
  padding-bottom: 40px;
}

.timeline-content p:last-child {
  margin-bottom: 0;
}

/*----- TIMELINE PERIOD -----*/
.period {
  padding: 0;
}

.period .timeline-info {
  display: none;
}

.period .timeline-marker:before {
  background: transparent;
  content: "";
  width: 15px;
  height: auto;
  border: none;
  border-radius: 0;
  top: 0;
  bottom: 30px;
  position: absolute;
  border-top: 3px solid #ccd5db;
  border-bottom: 3px solid #ccd5db;
}

.period .timeline-marker:after {
  content: "";
  height: 32px;
  top: auto;
}

.period .timeline-content {
  padding: 40px 0 70px;
}

.period .timeline-title {
  margin: 0;
}

/*----------------------------------------------
        MOD: TIMELINE SPLIT
    ----------------------------------------------*/
@media (min-width: 768px) {
  .timeline-split .timeline, .timeline-centered .timeline {
    display: table;
  }

  .timeline-split .timeline-item, .timeline-centered .timeline-item {
    display: table-row;
    padding: 0;
  }

  .timeline-split .timeline-info, .timeline-centered .timeline-info,
  .timeline-split .timeline-marker,
  .timeline-centered .timeline-marker,
  .timeline-split .timeline-content,
  .timeline-centered .timeline-content,
  .timeline-split .period .timeline-info,
  .timeline-centered .period .timeline-info {
    display: table-cell;
    vertical-align: top;
  }

  .timeline-split .timeline-marker,
  .timeline-centered .timeline-marker {
    position: relative;
  }

  .timeline-split .timeline-content,
  .timeline-centered .timeline-content {
    padding-left: 30px;
  }

  .timeline-split .timeline-info, .timeline-centered .timeline-info {
    padding-right: 30px;
  }

  .timeline-split .period .timeline-title, .timeline-centered .period .timeline-title {
    position: relative;
    left: -45px;
  }
}

/*----------------------------------------------
        MOD: TIMELINE CENTERED
    ----------------------------------------------*/
@media (min-width: 992px) {

  .timeline-centered,
  .timeline-centered .timeline-item,
  .timeline-centered .timeline-info,
  .timeline-centered .timeline-marker,
  .timeline-centered .timeline-content {
    display: block;
    margin: 0;
    padding: 0;
  }

  .timeline-centered .timeline-item {
    padding-bottom: 40px;
    overflow: hidden;
  }

  .timeline-centered .timeline-marker {
    position: absolute;
    left: 50%;
    margin-left: -7.5px;
  }

  .timeline-centered .timeline-info,
  .timeline-centered .timeline-content {
    width: 50%;
  }

  .timeline-centered>.timeline-item:nth-child(odd) .timeline-info {
    float: left;
    text-align: right;
    padding-right: 30px;
  }

  .timeline-centered>.timeline-item:nth-child(odd) .timeline-content {
    float: right;
    text-align: left;
    padding-left: 30px;
  }

  .timeline-centered>.timeline-item:nth-child(even) .timeline-info {
    float: right;
    text-align: left;
    padding-left: 30px;
  }

  .timeline-centered>.timeline-item:nth-child(even) .timeline-content {
    float: left;
    text-align: right;
    padding-right: 30px;
  }

  .timeline-centered>.timeline-item.period .timeline-content {
    float: none;
    padding: 0;
    width: 100%;
    text-align: center;
  }

  .timeline-centered .timeline-item.period {
    padding: 50px 0 90px;
  }

  .timeline-centered .period .timeline-marker:after {
    height: 30px;
    bottom: 0;
    top: auto;
  }

  .timeline-centered .period .timeline-title {
    left: auto;
  }
}

/*----------------------------------------------
        MOD: MARKER OUTLINE
    ----------------------------------------------*/
.marker-outline .timeline-marker:before {
  background: transparent;
  border-color: var(--ot-dot-timeline);
}

.marker-outline .timeline-item:hover .timeline-marker:before {
  background: var(--ot-dot-timeline);
}

/* timeline end */
/* Chart */
/* Apex Chart start */
.sparkboxes .box {
  padding-top: 10px;
  padding-bottom: 10px;
  text-shadow: 0 1px 1px 1px #666;
  box-shadow: 0px 1px 15px 1px rgba(69, 65, 78, 0.08);
  position: relative;
  border-radius: 5px;
}

.sparkboxes .box .details {
  position: absolute;
  color: var(--ot-text-primary);
  transform: scale(0.7) translate(-22px, 20px);
}

.sparkboxes strong {
  position: relative;
  z-index: 3;
  top: -8px;
  color: var(--ot-text-title);
}

.sparkboxes .box1 {
  background-image: linear-gradient(135deg, #abdcff 10%, #0396ff 100%);
}

.sparkboxes .box2 {
  background-image: linear-gradient(135deg, #2afadf 10%, #4c83ff 100%);
}

.sparkboxes .box3 {
  background-image: linear-gradient(135deg, #ffd3a5 10%, #fd6585 100%);
}

.sparkboxes .box4 {
  background-image: linear-gradient(135deg, #ee9ae5 10%, #5961f9 100%);
}

.sparkboxes .box1 {
  background-image: linear-gradient(135deg, #abdcff 10%, #0396ff 100%);
}

.sparkboxes .box2 {
  background-image: linear-gradient(135deg, #2afadf 10%, #4c83ff 100%);
}

.sparkboxes .box3 {
  background-image: linear-gradient(135deg, #ffd3a5 10%, #fd6585 100%);
}

.sparkboxes .box4 {
  background-image: linear-gradient(135deg, #ee9ae5 10%, #5961f9 100%);
}

/* Apex Chart end */
/* Chartjs start */
.chart-card {
  position: relative;
  height: 380px;
}

/* Chartjs end */
/* Invoice */
.ot-card .ot-invoice {
  display: flex;
}

@media (max-width: 576px) {
  .ot-card .ot-invoice {
    flex-direction: column;
  }
}

.ot-card .ot-invoice .ot-address ul {
  background: var(--ot-bg-secondary);
}

.ot-card .ot-invoice .ot-address ul li {
  color: var(--ot-text-primary);
}

.ot-card .ot-invoice .ot-address h2 {
  color: var(--ot-text-title);
}

.ot-card .ot-invoice .col-sm h5 {
  color: var(--ot-text-title);
}

.ot-card .ot-invoice .col-sm div ul {
  background: var(--ot-bg-secondary);
}

.ot-card .ot-invoice .col-sm div ul li {
  color: var(--ot-text-primary);
}

.ot-card .ot-invoice .col-sm div ul li strong {
  color: var(--ot-text-title);
}

.ot-card .ot-invoice .col-sm dl dt {
  color: var(--ot-text-title);
}

.ot-card .ot-invoice .col-sm dl dd {
  color: var(--ot-text-primary);
}

.ot-card .ot-invoice .table-responsive table thead tr th {
  color: var(--ot-text-title);
}

.ot-card .ot-invoice .table-responsive table tbody tr th {
  color: var(--ot-text-title);
}

.ot-card .ot-invoice .table-responsive table tbody tr td {
  color: var(--ot-text-primary);
}

.ot-card .ot-invoice .col-12 .email-address {
  color: var(--ot-text-primary);
}

.ot-table tbody tr td {
  color: var(--ot-text-primary);
}

.ot-table tbody tr td strong {
  color: var(--ot-text-title);
}

/* Footer */
#layout-wrapper {
  height: 100%;
}

#layout-wrapper main {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.footer {
  margin-top: 20px;
  padding: 21px 0px;
  width: 100%;
  background: var(--ot-bg-secondary-opacity);
  border-top: 2px solid var(--ot-bg-tertiary);
}

.footer p {
  display: flex;
  align-items: center;
  margin-left: 28px;
  color: var(--ot-text-primary);
}

.footer p a {
  margin-left: 3px;
  color: #1890ff;
}

@media (max-width: 576px) {
  .footer p {
    font-size: 11px;
  }
}

/* Auth */
/*Auth Container start */
.auth-page {
  height: 100vh;
  display: flex;
  overflow-x: hidden;
}

.auth-container {
  width: 560px;
  margin: auto;
  font-family: "Lexend", sans-serif;
  /* Form wrapper start */
  /* Form wrapper end */
}

.auth-container .form-wrapper {
  background-color: var(--ot-bg-secondary) !important;
  /* Form Logo start */
  /* Form Logo end */
  /* Form heading start */
  /* Form heading end */
  /* Social Login start */
  /* Social Login end */
  /* Text Devider start */
  /* Text Devider End */
  /* Form start */
  /* Submit btn end */
  /* Form start */
  /* Authenticate now start */
  /* Authenticate now end */
}

.auth-container .form-wrapper .form-container {
  width: 360px;
}

.auth-container .form-wrapper .form-logo img {
  width: 154px;
  height: 38px;
}

.auth-container .form-wrapper .form-logo img.dark-logo {
  display: none;
}

.auth-container .form-wrapper .form-heading .title {
  font-weight: 700;
  font-size: 24px;
  line-height: 30px;
  color: var(--ot-text-title);
}

.auth-container .form-wrapper .form-heading .subtitle {
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  color: var(--ot-text-primary);
}

.auth-container .form-wrapper .social-login {
  width: 100%;
}

.auth-container .form-wrapper .social-login .social-login-item {
  border: 1px solid #eaeaea;
  border-radius: 7px;
}

.auth-container .form-wrapper .social-login .social-login-item a {
  width: 100%;
  height: 100%;
  padding: 17px 38px;
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  color: var(--ot-text-title);
  text-decoration: none;
}

.auth-container .form-wrapper .social-login .social-login-item.facebook-login {
  background-color: #395799;
}

.auth-container .form-wrapper .social-login .social-login-item.facebook-login a {
  color: #fff;
}

.auth-container .form-wrapper .social-login .social-login-item:hover {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.auth-container .form-wrapper .login-with-divider-text {
  font-weight: 600;
  font-size: 10px;
  line-height: 14px;
  color: var(--ot-text-primary);
  margin-bottom: 0;
}

.auth-container .form-wrapper .auth-form {
  /* Form label start */
  width: 360px;
  /* Form label End */
  /* Form Input start */
  /* Form Input end */
  /* Input field with logo start */
  /* Input field with logo end */
  /* input field start */
  /* input field end */
  /* Remember me / forget password start */
  /* Remember me / forget password end */
  /* Submit btn start */
}

.auth-container .form-wrapper .auth-form label {
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  color: var(--ot-text-title);
  margin-bottom: 8px;
}

.auth-container .form-wrapper .auth-form .input-field-group {
  width: 100%;
  line-height: 0;
}

.auth-container .form-wrapper .auth-form input[type="text"],
.auth-container .form-wrapper .auth-form input[type="password"],
.auth-container .form-wrapper .auth-form input[type="date"],
.auth-container .form-wrapper .auth-form select,
.auth-container .form-wrapper .auth-form input[type="email"] {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  padding: 16px 12px !important;
  background: #ffffff;
  border: 1px solid #eaeaea;
  border-radius: 5px;
  padding-left: 36px !important;
  color: var(--ot-text-title);
}

.auth-container .form-wrapper .auth-form input[type="text"]::placeholder,
.auth-container .form-wrapper .auth-form input[type="password"]::placeholder,
.auth-container .form-wrapper .auth-form input[type="date"]::placeholder,
.auth-container .form-wrapper .auth-form select::placeholder,
.auth-container .form-wrapper .auth-form input[type="email"]::placeholder {
  color: #b2bec3;
}

.auth-container .form-wrapper .auth-form input[type="text"]:focus-visible,
.auth-container .form-wrapper .auth-form input[type="password"]:focus-visible,
.auth-container .form-wrapper .auth-form input[type="date"]:focus-visible,
.auth-container .form-wrapper .auth-form select:focus-visible,
.auth-container .form-wrapper .auth-form input[type="email"]:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.auth-container .form-wrapper .auth-form .custom-input-field {
  width: 100%;
  position: relative;
}

.auth-container .form-wrapper .auth-form .custom-input-field img {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
}

.auth-container .form-wrapper .auth-form .custom-input-field i {
  position: absolute;
  right: 16px;
  transform: translateY(-50%);
  top: 50%;
  color: var(--ot-text-primary);
  font-size: 18px;
  cursor: pointer;
}

.auth-container .form-wrapper .auth-form .input-error {
  font-weight: 400;
  font-size: 10px;
  line-height: 14px;
  margin-bottom: 0;
}

.auth-container .form-wrapper .auth-form .input-error.error-danger {
  color: #ff6a54;
}

.auth-container .form-wrapper .auth-form .input-error.error-warning {
  color: #ff991a;
}

.auth-container .form-wrapper .auth-form .remember-me label {
  margin-left: 8px;
  margin-bottom: 0;
  color: var(--ot-text-title) !important;
}

.auth-container .form-wrapper .auth-form .forgot-password,
.auth-container .form-wrapper .auth-form .remember-me label {
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  color: var(--ot-text-primary);
}

.auth-container .form-wrapper .auth-form .submit-btn {
  width: 100%;
 background: #645CBB;
  border-radius: 5px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: .3s;
}

.auth-container .form-wrapper .auth-form .submit-btn:hover {
  background: #F99417;
}

.auth-container .form-wrapper .authenticate-now {
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  color: var(--ot-text-primary);
}

.auth-container .form-wrapper .authenticate-now a {
 background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.auth-container .form-wrapper .authenticate-now a:hover {
  background: #645CBB;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.auth-container .form-wrapper .privacy-policy-navigation a,
.auth-container .form-wrapper .privacy-policy-navigation span {
  font-weight: 500;
  font-size: 12px;
  line-height: 21px;
  color: var(--ot-text-primary);
}

.auth-container .form-wrapper .privacy-policy-navigation span {
  font-size: 16px;
}

.rtl .auth-container .form-wrapper input[type="text"],
.rtl .auth-container .form-wrapper input[type="password"],
.rtl .auth-container .form-wrapper input[type="date"],
.rtl .auth-container .form-wrapper select,
.rtl .auth-container .form-wrapper input[type="email"] {
  padding-right: 36px !important;
  padding-left: initial !important;
}

.rtl .custom-input-field img {
  right: 14px;
  left: auto !important;
}

.rtl .custom-input-field i {
  left: 16px;
  right: auto !important;
}

.dark-theme .form-logo .default-logo {
  display: none;
}

.dark-theme .form-logo .dark-logo {
  display: block !important;
}

/*Auth Container end */
@media (max-width: 768px) {
  .auth-container {
    width: 480px;
  }

  .form-wrapper {
    padding: 40px 50px !important;
  }

  .form-container,
  .auth-form {
    width: 320px !important;
  }
}

@media (max-width: 576px) {
  .auth-container {
    width: 340px;
  }

  .form-wrapper {
    padding: 30px 40px !important;
  }

  .form-container,
  .auth-form {
    width: 300px !important;
  }
}

@media (max-width: 480px) {
  .auth-container {
    width: 320px;
  }

  .form-wrapper {
    padding: 40px 20px !important;
  }

  .form-container,
  .auth-form {
    width: 280px !important;
  }

  .social-login span {
    font-size: 10px;
  }
}

.auth-container .form-wrapper .auth-form label {
    margin-bottom: 8px;
    display: block;
}
.form-check.d-flex.align-items-center {
    display: flex;
    align-items: center;
}
.d-flex.justify-content-between.align-items-center.w-100.mt-20 {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}
.submit-button {
	cursor: pointer;
}
.select-role input{
  padding: none !important;
}
button{
  transition: .3s;
}
/* .button-group-pills .btn {
    border-radius: 5px;
    color: var(--ot-text-title) !important;
    font-size: 14px;
    font-weight: 600;
    padding-top: 16px !important;
    padding-bottom: 16px !important;
}
.btn-default input[type="radio"]{
    display: none;
}
.button-group-pills .btn.active {
    background: #645CBB;
    color: #fff !important;
    box-shadow: none;
}
.button-group-pills .btn:hover {
    background: #645CBB;
} */
.cus-btn-check:checked+.btn{
    background: #645CBB;
    color: #fff !important;
    padding: 16px;
}
.cus-btn {
    padding: 16px;
}
.btn-check:checked+.btn:focus,.cus-btn:focus{
    box-shadow: none !important;
    outline: none;
}
.btn-check:focus+.btn, .btn:focus
{
  box-shadow: none;
}
.login_credential{
  display: grid;
  grid-template-columns: repeat(3,1fr);
  grid-gap: 1rem;
  width: 100%;
  margin-bottom: 20px;
}
.login_credential button{
  text-transform: capitalize;
  margin: 0;
}
.auth-container .form-wrapper .auth-form label{}
.form-container .btn-group {}
.form-container .btn-group .btn {
  border-color: #645CBB;
}
.form-container .btn-group .btn:hover {
  background: #645CBB;
  color: #fff;
}