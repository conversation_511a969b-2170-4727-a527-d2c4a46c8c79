<?php

namespace App\Models;

use App\Models\User;
use App\Models\Property\Property;
use App\Models\Property\PropertyTenant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Account extends Model
{
    use HasFactory;

    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    public function propertyTenant(): BelongsTo
    {
        return $this->belongsTo(PropertyTenant::class);
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class,'bank_account_id','id');
    }

    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }
}
