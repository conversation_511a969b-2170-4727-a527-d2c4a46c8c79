<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $attributes = [
            //for staff
            'users'                     => ['read' =>  'user_read', 'create' => 'user_create', 'update' => 'user_update', 'delete' => 'user_delete'],
            'roles'                     => ['read' =>  'role_read', 'create' => 'role_create', 'update' =>  'role_update', 'delete' =>  'role_delete'],
            'language'                  => ['read' =>  'language_read', 'create' => 'language_create', 'update' =>  'language_update','update terms' =>  'language_update_terms', 'delete' =>  'language_delete'],
            'general settings'          => ['read' =>  'general_settings_read', 'update' => 'general_settings_update'],
            'storage settings'          => ['read' =>  'storage_settings_read', 'update' => 'storage_settings_update'],
            'recaptcha settings'        => ['read' =>  'recaptcha_settings_read', 'update' => 'recaptcha_settings_update'],
            'email settings'            => ['read' =>  'email_settings_read', 'update' => 'email_settings_update'],
            'categories'                => ['read' =>  'category_read', 'create' => 'category_create', 'update' => 'category_update', 'delete' => 'category_delete'],
            'property categories'       => ['read' =>  'property_category_read', 'create' => 'property_category_create', 'update' => 'property_category_update', 'delete' => 'property_category_delete'],
            'property facility types'   => ['read' =>  'property_facility_type_read', 'create' => 'property_facility_type_create', 'update' => 'property_facility_type_update', 'delete' => 'property_facility_type_delete'],
            'how-it-works'              => ['read' =>  'how-it-work_read', 'create' => 'how-it-work_create', 'update' => 'how-it-work_update', 'delete' => 'how-it-work_delete'],
            'users'                     => ['read' =>  'user_read', 'create' => 'user_create', 'update' => 'user_update', 'delete' => 'user_delete'],
            'roles'                     => ['read' =>  'role_read', 'create' => 'role_create', 'update' =>  'role_update', 'delete' =>  'role_delete'],
            'language'                  => ['read' =>  'language_read', 'create' => 'language_create', 'update' =>  'language_update','update terms' =>  'language_update_terms', 'delete' =>  'language_delete'],
            'general settings'          => ['read' =>  'general_settings_read', 'update' => 'general_settings_update'],
            'storage settings'          => ['read' =>  'storage_settings_read', 'update' => 'storage_settings_update'],
            'recaptcha settings'        => ['read' =>  'recaptcha_settings_read', 'update' => 'recaptcha_settings_update'],
            'email settings'            => ['read' =>  'email_settings_read', 'update' => 'email_settings_update'],
            'testimonials'              => ['read' =>  'testimonial_read', 'create' => 'testimonial_create', 'update' => 'testimonial_update', 'delete' => 'testimonial_delete'],
            'blogs'                     => ['read' =>  'blogs_read', 'update' => 'blogs_update', 'delete' => 'blogs_delete'],
            'blog categories'           => ['read' =>  'blog_categories_read', 'update' => 'blog_categories_update', 'delete' => 'blog_categories_delete'],
            'section title'             => ['update' => 'section_titles'],
            'partners'                  => ['read' =>  'partners_read', 'update' => 'partners_update', 'delete' => 'partners_delete'],
            'contacts'                  => ['read' =>  'contact_read', 'delete' => 'contact_delete'],
            'about'                     => ['update' => 'about_update'],
            'Advertisement Permission'  => ['update' => 'ad_action_permission'],
        ];

        foreach($attributes as $key => $attribute){
        	$permission               = new Permission();
        	$permission->attribute    = $key;
            $permission->keywords     = $attribute;
        	$permission->save();
        }
    }
}
