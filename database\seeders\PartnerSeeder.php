<?php

namespace Database\Seeders;

use App\Models\Image;
use App\Models\Partner;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class PartnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $lists = [
            'frontend/img/o_land_brand/1.png',
            'frontend/img/o_land_brand/2.png',
            'frontend/img/o_land_brand/3.png',
            'frontend/img/o_land_brand/4.png',
            'frontend/img/o_land_brand/5.png',
            'frontend/img/o_land_brand/6.png',
            'frontend/img/o_land_brand/7.png',
            'frontend/img/o_land_brand/8.png',
            'frontend/img/o_land_brand/9.png',
            'frontend/img/o_land_brand/10.png',
            'frontend/img/o_land_brand/11.png',
            'frontend/img/o_land_brand/12.png',
        ];
    
        foreach ($lists as $key => $list) {
            $image = Image::create([
                'path' => $list,
            ]);
            $uploaded_partner_img[] = $image->id;
        }
    
        $partners = [];
        for($i = 0; $i <= 10 ; $i++){
            $partners[] = [
                'name' => 'Partner'.$i,
                'image_id' => $uploaded_partner_img[$i],
                'status' => '1',
            ];
        }
        Partner::insert($partners);
    }
}
