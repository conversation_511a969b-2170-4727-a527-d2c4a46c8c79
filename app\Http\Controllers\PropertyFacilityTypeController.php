<?php

namespace App\Http\Controllers;

use App\Models\Property\PropertyFacilityType;
use Illuminate\Http\Request;

class PropertyFacilityTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Property\PropertyFacilityType  $propertyFacilityType
     * @return \Illuminate\Http\Response
     */
    public function show(PropertyFacilityType $propertyFacilityType)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Property\PropertyFacilityType  $propertyFacilityType
     * @return \Illuminate\Http\Response
     */
    public function edit(PropertyFacilityType $propertyFacilityType)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Property\PropertyFacilityType  $propertyFacilityType
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, PropertyFacilityType $propertyFacilityType)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Property\PropertyFacilityType  $propertyFacilityType
     * @return \Illuminate\Http\Response
     */
    public function destroy(PropertyFacilityType $propertyFacilityType)
    {
        //
    }
}
