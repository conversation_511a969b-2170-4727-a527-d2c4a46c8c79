<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('property_id')->constrained('properties')->cascadeOnDelete();
            $table->foreignId('property_tenant_id')->constrained('property_tenants')->cascadeOnDelete();
            $table->foreignId('rental_id')->constrained('rentals')->cascadeOnDelete();

            $table->double('amount', 10, 3)->nullable();
            $table->enum('type', ['rent', 'deposit', 'other', 'income', 'expense'])->default('rent');
            $table->date('date')->nullable();
            $table->longText('note')->nullable();
            $table->tinyInteger('status')->default(App\Enums\Status::ACTIVE);


            //payment method
            $table->enum('payment_method', ['cash', 'cheque', 'bank_transfer','online' ,'other'])->default('cash');
            $table->string('cheque_number')->nullable();
            $table->string('bank_name')->nullable();
            $table->string('bank_branch')->nullable();
            $table->string('bank_account_number')->nullable();
            $table->string('bank_account_name')->nullable();

            // online payment
            $table->string('online_payment_method')->nullable();
            $table->string('online_payment_transaction_id')->nullable();
            $table->string('online_payment_transaction_status')->nullable();


            //attachment file relation with images table
            $table->foreignId('attachment_id')->constrained('images')->cascadeOnDelete()->nullable();

            // payment status (paid, unpaid, partial) enum
            $table->enum('payment_status', ['paid', 'unpaid', 'partial'])->default('unpaid');

            //invoice related
            $table->string('invoice_number')->nullable();
            $table->date('invoice_date')->nullable();



            $table->foreignId('created_by')->constrained('users')->cascadeOnDelete();
            $table->foreignId('updated_by')->constrained('users')->cascadeOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transactions');
    }
};
