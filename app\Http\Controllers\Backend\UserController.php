<?php

namespace App\Http\Controllers\Backend;

use App\Models\User;
use App\Models\Rental;
use App\Models\Account;
use App\Models\Document;
use PDF;

use App\Models\BankAccount;
use Illuminate\Http\Request;
use App\Models\EmergencyContact;
use function PHPSTORM_META\type;

use App\Interfaces\RoleInterface;
use App\Interfaces\UserInterface;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\Property\Transaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use App\Interfaces\PermissionInterface;
use App\Http\Requests\User\UserStoreRequest;
use App\Http\Requests\User\UserUpdateRequest;
use Symfony\Component\HttpFoundation\Response;

class UserController extends Controller
{

    private $user;
    private $permission;
    private $role;
    private $rentalModel;

    function __construct(UserInterface $userInterface, PermissionInterface $permissionInterface, RoleInterface $roleInterface)
    {

        if (!Schema::hasTable('settings') && !Schema::hasTable('users')) {
            abort(400);
        }
        $this->user       = $userInterface;
        $this->permission = $permissionInterface;
        $this->role       = $roleInterface;
    }

    public function index()
    {
        $data['users'] = $this->user->getAll();
        $data['title'] = ___('users_roles.users');
        return view('backend.users.index', compact('data'));
    }

    public function profile()
    {
        $data['title'] = ___('users_roles.users');

        return view('backend.users.profile', compact('data'));
    }

    public function create()
    {
        $data['title']       = ___('common.create_user');
        $data['permissions'] = $this->permission->all();
        $data['roles']       = $this->role->all();
        return view('backend.users.create', compact('data'));
    }

    public function store(UserStoreRequest $request)
    {

        $result = $this->user->store($request);
        if ($result) {
            return redirect()->route('users.index')->with('success', ___('alert.user_created_successfully'));
        }
        return redirect()->route('users.index')->with('danger',  ___('alert.something_went_wrong_please_try_again'));
    }

    public function edit($id)
    {
        $data['user']        = $this->user->show($id);
        $data['title']       = ___('users_roles.users');
        $data['permissions'] = $this->permission->all();
        $data['roles']       = $this->role->all();
        return view('backend.users.edit', compact('data'));
    }

    public function update(UserUpdateRequest $request, $id)
    {
        $result = $this->user->update($request, $id);
        if ($result) {
            return redirect()->route('users.index')->with('success', ___('alert.user_updated_successfully'));
        }
        return redirect()->route('users.index')->with('danger',  ___('alert.something_went_wrong_please_try_again'));
    }

    public function delete($id)
    {
        if ($this->user->destroy($id)) :
            $success[0] = "Deleted Successfully";
            $success[1] = "Success";
            $success[2] = "Deleted";
        else :
            $success[0] = "Something went wrong, please try again.";
            $success[1] = 'error';
            $success[2] = "oops";
        endif;
        return response()->json($success);
    }

    public function changeRole(Request $request)
    {
        $data['role_permissions'] = $this->role->show($request->role_id)->permissions;
        $data['permissions']      = $this->permission->all();
        return view('backend.users.permissions', compact('data'))->render();
    }

    public function status(Request $request)
    {

        if ($request->type == 'active') {
            $request->merge([
                'status' => 1
            ]);
            $this->user->status($request);
        }

        if ($request->type == 'inactive') {
            $request->merge([
                'status' => 0
            ]);
            $this->user->status($request);
        }

        return response()->json(["message" => __("Status update successful")], Response::HTTP_OK);
    }

    public function deletes(Request $request)
    {
        $this->user->deletes($request);

        return response()->json(["message" => __('Delete successful.')], Response::HTTP_OK);
    }



    public function profileDetails($id, $type)
    {
        $data['id'] = $id;
        $user = User::where('id', $id)->first();
        $emergency = EmergencyContact::where('id', $id)->first();
        $documents = Document::where('user_id', Auth::user()->id)->get();
        $transactions = Transaction::where('property_tenant_id', $id)->get();
        $rental = Rental::where('id', $id)->first();
        $account = BankAccount::where('user_id', $id)->first();

        switch ($type) {
            case 'basicInfo':
                $data['title'] = 'Basic Info';
                return view('backend.users.basic_info', compact('data', 'user'));
                break;

            case 'documents':
                $data['title'] = 'Documents';
                return view('backend.users.Documents', compact('data', 'user', 'documents'));
                break;
            case 'emergency':
                $data['title'] = 'Emergency';
                return view('backend.users.Emergency', compact('data', 'user', 'emergency'));
                break;

            case 'accounts':
                $data['title'] = 'Account';
                return view('backend.users.Account', compact('data', 'user', 'account'));
                break;


            case 'transaction':
                $data['title'] = 'Transaction';
                return view('backend.users.Transaction', compact('data', 'user', 'transactions'));
                break;

            case 'agreements':
                $data['title'] = 'Agreements';
                return view('backend.users.Agreements', compact('data', 'rental', 'user'));
                break;
        }
    }

    public function profileDetailsStore(Request $request, $id, $type)
    {
        $data['id'] = $id;


        if ($type == 'basicInfo') {
            $result = $this->user->profileDetailsStore($request, $id, $type);
            if ($result) {
                return redirect()->route('users.profileDetails', [$id, $type])->with('success', ___('alert.user_created_successfully'));
            }
            return redirect()->route('users.profileDetails', [$id, $type])->with('danger',  ___('alert.something_went_wrong_please_try_again'));
        }
        elseif ($type == 'documents') {
            $result = $this->user->documentDetailsStore($request, $id, $type);

            if ($result) {
                return redirect()->route('users.profileDetails', [$id, $type])->with('success', ___('alert.user_created_successfully'));
            }
            return redirect()->route('users.profileDetails', [$id, $type])->with('danger',  ___('alert.something_went_wrong_please_try_again'));
        }
        elseif ($type == 'transaction') {
            $result = $this->user->transactionDetailsStore($request, $id, $type);

            if ($result) {
                return redirect()->route('users.profileDetails', [$id, $type])->with('success', ___('alert.user_created_successfully'));
            }
            return redirect()->route('users.profileDetails', [$id, $type])->with('danger',  ___('alert.something_went_wrong_please_try_again'));
        }
        elseif ($type == 'emergency') {
            $result = $this->user->emergencyDetailsStore($request, $id, $type);

            if ($result) {
                return redirect()->route('users.profileDetails', [$id, $type])->with('success', ___('alert.user_created_successfully'));
            }
            return redirect()->route('users.profileDetails', [$id, $type])->with('danger',  ___('alert.something_went_wrong_please_try_again'));
        }
        elseif ($type == 'accounts') {
            $result = $this->user->accountDetailsStore($request, $id, $type);

            if ($result) {
                return redirect()->route('users.profileDetails', [$id, $type])->with('success', ___('alert.user_created_successfully'));
            }
            return redirect()->route('users.profileDetails', [$id, $type])->with('danger',  ___('alert.something_went_wrong_please_try_again'));
        }
        elseif ($type == 'agreements') {
            $result = $this->user->rentalDetailsStore($request, $id, $type);
            if ($result) {
                return redirect()->route('users.profileDetails', [$id, $type])->with('success', ___('alert.user_created_successfully'));
            }
            return redirect()->route('users.profileDetails', [$id, $type])->with('danger',  ___('alert.something_went_wrong_please_try_again'));
        }
    }

    public function deleteAttachment($id)
    {
        $result = $this->user->deleteAttachment($id);
        if ($result) {
            return redirect()->back()->with('success', ___('alert.attachment_deleted_successfully'));
        }
        return redirect()->back()->with('danger', ___('alert.something_went_wrong_please_try_again'));
    }

    public function createPDF($id) {
        // retreive all records from db
        $data = Transaction::where('property_tenant_id', $id)->get();
        $dataOne = Transaction::where('id', $id)->first();
        // $totalIncome = Transaction::where('property_tenant_id', $id)->where('type', 'income')->count();
        $totalIncome = Transaction::where('property_tenant_id', $id)->where('type', '=', 'income')->sum('amount');
        $totalRent = Transaction::where('property_tenant_id', $id)->where('type', '=', 'rent')->sum('amount');
        $totalExpense = Transaction::where('property_tenant_id', $id)->where('type', '=', 'expense')->sum('amount');
        $totalDeposit = Transaction::where('property_tenant_id', $id)->where('type', '=', 'deposit')->sum('amount');
        $totalOther = Transaction::where('property_tenant_id', $id)->where('type', '=', 'other')->sum('amount');

        $totalAmount = $totalIncome - $totalExpense + $totalRent + $totalDeposit - $totalOther;

        // dd($totalRent);
        $pdf = PDF::loadView('backend.users.transaction_pdf',['data'=>$data , 'dataOne'=>$dataOne, 'totalAmount'=>$totalAmount]);
        // download PDF file with download method
        return $pdf->stream('pdf_file.pdf');
      }
}
