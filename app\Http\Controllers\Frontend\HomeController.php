<?php

namespace App\Http\Controllers\Frontend;

use App\Models\Blog;
use App\Models\About;
use App\Models\Partner;
use App\Models\HowItWork;
use App\Models\BlogReview;
use App\Models\HeroSection;
use Illuminate\Support\Str;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use App\Models\Property\Property;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\Property\PropertyCategory;
use App\Http\Resources\PropertyShowResource;
use App\Http\Resources\PropertyFilteringResource;
use App\Models\Property\PropertyReview;

class HomeController extends Controller
{


    public function propertyMapping($properties){

        return $properties->map(function ($property) {
            return [
                'id' => @$property->id,
                'name' => @$property->name,
                'slug' => @$property->slug,
                'address' => @$property->address,
                'bedrooms' => @$property->bedroom,
                'bathrooms' => @$property->bathroom,
                'size' => @$property->size,
                'price' => @$property->rent_amount,
                'image' => apiAssetPath(@$property->defaultImage->path),
                'type' => @$property->type,
                'vacant' => @$property->vacant == 1 ? 'Vacant' : 'Occupied',
                'details_url' => url("property/".$property->id."/details"."/".$property->slug ), //url("property/".$item->id."/details"."/".$item->slug )
                'flat_no' => @$property->flat_no,
                'completion' => @$property->completion==1 ? 'Ready' : 'Under Construction',
                'deal_type' => @$property->deal_type==1 ? 'Rent' : 'Sale',
                'category' => @$property->category->name,
                'address' => [
                    'id'                                       => @$property->location->id,
                    'country'                                  => @$property->location->country->name,
                    'division'                                 => @$property->location->division->name,
                    'district'                                 => @$property->location->district->name,
                    'upazila'                                  => @$property->location->upazila->name,
                    'address'                                  => @$property->location->address==""?'no data':@$property->location->address,
                ]
            ];
        });
    }

    public function index()
    {
        $data['title'] = 'Home';
        $hero_banners = HeroSection::with('image:id,path')->where('status', 1)->select('id', 'title', 'highlight_title_one', 'btn_one', 'image_id', 'status')->get();

        $list =  Property::where('status', 1)->inRandomOrder()->take(5)->get();
        $data['topRatingProperties']  = $this->propertyMapping($list);

        $peopleChoices = Property::where('status', 1)->inRandomOrder()->take(5)->get();
        $data['peopleChoices'] = $this->propertyMapping($peopleChoices);

        $featuredProperties = Property::where('status', 1)->inRandomOrder()->take(5)->get();
        $data['featuredProperties'] = $this->propertyMapping($featuredProperties);

        $data['partners'] = Partner::where('status', 1)->limit(10)->get();;


        $data['how_it_works'] = HowItWork::where('status', 1)->get();



        $data['sliders'] = $hero_banners->map(function ($slider) {
            return [
                'id'                    => $slider->id,
                'title'                 => $slider->title,
                'highlight_title_one'   => $slider->highlight_title_one,
                'btn_one'               => $slider->btn_one,
                'imageURL'              => globalAsset($slider->image->path),
            ];
        });

        // $propertyCategories = PropertyCategory::where('status',1)->get();
        // return view('frontend.home',compact('data','propertyCategories'));
        return view('frontend.home', compact('data'));
    }
    public function orderTracking()
    {
        return view('frontend.order.order_tracking');
    }
    public function properties()
    {
        $data['title'] = 'Properties';
        $data['properties'] = Property::paginate(9);
        $data['keyword'] = 'All';
        return view('frontend.property.index', compact('data'));
    }
    public function categoryWiseProperties($slug)
    {
        $category = PropertyCategory::where('slug', $slug)->first();
        $data['title'] = 'Properties of ' . $category->name;
        $data['keyword'] = $category->name;
        $data['category'] = $category;
        // $data['properties'] = $category->properties()->where('status',1)->get();
        $data['properties'] = Property::paginate(9);
        return view('frontend.property.index', compact('data'));
    }
    public function blogs()
    {
        $data['title'] = 'Blogs';
        $data['categories'] = BlogCategory::where('status', 1)->get();

        // Blogs Start
        $blogs = Blog::with('image:id,path')->where('status', 1)->select('id', 'title', 'content', 'image_id', 'created_at', 'category_id', 'slug')->orderBy('created_at', 'desc')->paginate(9);
        $data['blogs'] = $blogs->map(function ($blogs) {
            return [
                'id' => $blogs->id,
                'title' => $blogs->title,
                'content' => Str::limit($blogs->content, 100),
                'image' => $blogs->image->path,
                'created_at' => date('F d, Y', strtotime($blogs->created_at)),
                'category' => $blogs->category->title,
                'slug' => $blogs->slug,
            ];
        });
        // Blogs End

        // Recent Blogs Start
        $blogs = Blog::where('status', 1)->select('id', 'title', 'created_at', 'category_id', 'slug')->orderBy('created_at', 'desc')->paginate(3);
        $data['recent_blogs'] = $blogs->map(function ($blogs) {
            return [
                'id' => $blogs->id,
                'title' => str::limit($blogs->title, 30),
                'created_at' => date('F d, Y', strtotime($blogs->created_at)),
                'category' => $blogs->category->title,
                'slug' => $blogs->slug,
            ];
        });
        // Recent Blogs End

        // dd($data['blogs']);

        return view('frontend.blog.index', compact('data'));
    }
    public function terms()
    {
        return view('frontend.terms_condition');
    }

    public function blogDetails($slug)
    {
        $data['title'] = 'Blog Details';
        $data['categories'] = BlogCategory::where('status', 1)->get();

        // Blog Details Start
        $blog = Blog::with('image:id,path')->where('slug', $slug)->select('id', 'title', 'content', 'image_id', 'created_at', 'category_id', 'slug', 'tags')->first();
        $data['blog_details'] = [
            'id' => $blog->id,
            'title' => $blog->title,
            'content' => $blog->content,
            'image' => $blog->image->path,
            'created_at' => date('F d, Y', strtotime($blog->created_at)),
            'category' => $blog->category->title,
            'slug' => $blog->slug,
            'tags' => $blog->tags,
        ];
        // Blog Details End

        // Recent Blogs Start
        $blogs = Blog::where('status', 1)->select('id', 'title', 'created_at', 'category_id', 'slug')->orderBy('created_at', 'desc')->paginate(3);
        $data['blogs'] = $blogs->map(function ($blogs) {
            return [
                'id' => $blogs->id,
                'title' => str::limit($blogs->title, 30),
                'created_at' => date('F d, Y', strtotime($blogs->created_at)),
                'category' => $blogs->category->title,
                'slug' => $blogs->slug,
            ];
        });
        // Recent Blogs End

        // Blog Reviews Start
        $data['blog_reviews'] = BlogReview::where('blog_id', $blog->id)->get();
        // Blog Reviews End

        return view('frontend.blog.details', compact('data'));
    }
    public function blogReview(Request $request, $blogId){

            // Validate the input fields
            if (Auth::check()) {
                $validatedData = $request->validate([
                    'comments' => 'required',
                    'ratings' => 'required|numeric|min:1|max:5',
                ]);
            } else {
                $validatedData = $request->validate([
                    'name' => 'required',
                    'email' => 'required|email',
                    'comments' => 'required',
                    'ratings' => 'required|numeric|min:1|max:5',
                ]);
            }

            $blogReview = new BlogReview();

            if (Auth::check()) {
                // User is logged in, retrieve name and email from authenticated user
                $user = Auth::user();
                $blogReview->name = $user->name;
                $blogReview->email = $user->email;
            } else {
                // User is not logged in, use the values from the request
                $blogReview->name = $request->name;
                $blogReview->email = $request->email;
            }

            $blogReview->blog_id = $blogId; // Set the blog ID
            $blogReview->comments = $validatedData['comments'];
            $blogReview->ratings = $validatedData['ratings'];
            $blogReview->save();

            // Redirect or perform other actions after saving the review

            return redirect()->back()->with('success', 'Review posted successfully!');
        
    }

    public function faq()
    {
        return view('frontend.faq');
    }
    public function error()
    {
        return view('frontend.error');
    }

    public function propertyDetails()
    {
        return view('frontend.property.details');
    }
    public function propertyReview(Request $request, $id){

        // Validate the input fields
        if (Auth::check()) {
            $validatedData = $request->validate([
                'comments' => 'required',
                'ratings' => 'required|numeric|min:1|max:5',
            ]);
        } else {
            $validatedData = $request->validate([
                'name' => 'required',
                'email' => 'required|email',
                'comments' => 'required',
                'ratings' => 'required|numeric|min:1|max:5',
            ]);
        }

        $blogReview = new PropertyReview();

        if (Auth::check()) {
            // User is logged in, retrieve name and email from authenticated user
            $user = Auth::user();
            $blogReview->name = $user->name;
            $blogReview->email = $user->email;
        } else {
            // User is not logged in, use the values from the request
            $blogReview->name = $request->name;
            $blogReview->email = $request->email;
        }

        $blogReview->property_id = $id; // Set the blog ID
        $blogReview->comments = $validatedData['comments'];
        $blogReview->ratings = $validatedData['ratings'];
        $blogReview->save();

        // Redirect or perform other actions after saving the review

        return redirect()->back()->with('success', 'Review posted successfully!');
    
}
    public function frontendDAshboard()
    {
        return view('frontend.frontend_dashboard');
    }

    public function privacy()
    {
        return view('frontend.privacy_policy');
    }
}
