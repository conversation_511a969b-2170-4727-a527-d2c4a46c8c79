<?php

namespace App\Http\Controllers\Backend;

use Illuminate\Http\Request;
use App\Interfaces\RentalInterface;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Schema;
use App\Interfaces\PermissionInterface;

class RentalController extends Controller
{

    private $rental;
    private $permission;

    function __construct(RentalInterface $userInterface, PermissionInterface $permissionInterface)
    {

        if (!Schema::hasTable('settings') && !Schema::hasTable('users')) {
            abort(400);
        }
        $this->rental       = $userInterface;
        $this->permission = $permissionInterface;
    }

    public function profile()
    {
        $data['title'] = ___('users_roles.users');

        return view('backend.users.profile', compact('data'));
    }


}
