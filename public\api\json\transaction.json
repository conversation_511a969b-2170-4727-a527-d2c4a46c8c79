{"result": true, "api_end_point": "http://landlord.test/api/transaction", "message": "http://landlord.test/api/json/transaction.json", "data": {"messages": "Transaction", "items": {"list": [{"id": 1, "property": "City Center Apartment", "app_date": "20 Feb, 2023", "attachment": "http://landlord.test/frontend/img/favicon.png", "tenant": {"id": 9, "name": "Tenant", "email": "<EMAIL>", "phone": "***********"}, "rental_agreement": {"id": 2, "amount": 11000, "start_date": "2023-04-01", "end_date": "2023-07-31", "note": "pre paid"}, "amount": 1000, "type": "rent", "date": "2023-02-20", "note": "This is a note", "payment_details": {"payment_method": "cash", "cheque_number": "123456", "bank_name": "Bank Name", "bank_branch": "Bank Branch", "bank_account_number": "*********", "bank_account_name": "Bank Account Name", "online_payment_method": null, "online_payment_transaction_id": null, "online_payment_transaction_status": null, "payment_status": "unpaid"}, "invoice": {"invoice_number": "X2Gw2pJnks", "invoice_date": "2023-02-20", "app_invoice_date": "20 Feb, 2023"}}, {"id": 2, "property": "Central Park View", "app_date": "20 Feb, 2023", "attachment": "http://landlord.test/frontend/img/favicon.png", "tenant": {"id": 2, "name": "Tenant", "email": "<EMAIL>", "phone": "***********"}, "rental_agreement": {"id": 2, "amount": 11000, "start_date": "2023-04-01", "end_date": "2023-07-31", "note": "pre paid"}, "amount": 1000, "type": "rent", "date": "2023-02-20", "note": "This is a note", "payment_details": {"payment_method": "cash", "cheque_number": "123456", "bank_name": "Bank Name", "bank_branch": "Bank Branch", "bank_account_number": "*********", "bank_account_name": "Bank Account Name", "online_payment_method": null, "online_payment_transaction_id": null, "online_payment_transaction_status": null, "payment_status": "unpaid"}, "invoice": {"invoice_number": "H2LqEv6rb9", "invoice_date": "2023-02-20", "app_invoice_date": "20 Feb, 2023"}}, {"id": 3, "property": "Ocean View Condo", "app_date": "20 Feb, 2023", "attachment": "http://landlord.test/frontend/img/favicon.png", "tenant": {"id": 3, "name": "Tenant", "email": "<EMAIL>", "phone": "***********"}, "rental_agreement": {"id": 3, "amount": 12000, "start_date": "2023-05-01", "end_date": "2023-08-31", "note": "advance paid"}, "amount": 1000, "type": "rent", "date": "2023-02-20", "note": "This is a note", "payment_details": {"payment_method": "cash", "cheque_number": "123456", "bank_name": "Bank Name", "bank_branch": "Bank Branch", "bank_account_number": "*********", "bank_account_name": "Bank Account Name", "online_payment_method": null, "online_payment_transaction_id": null, "online_payment_transaction_status": null, "payment_status": "unpaid"}, "invoice": {"invoice_number": "evh1nRR3a8", "invoice_date": "2023-02-20", "app_invoice_date": "20 Feb, 2023"}}, {"id": 4, "property": "Central Park View", "app_date": "20 Feb, 2023", "attachment": "http://landlord.test/frontend/img/favicon.png", "tenant": {"id": 2, "name": "Tenant", "email": "<EMAIL>", "phone": "***********"}, "rental_agreement": {"id": 1, "amount": 10000, "start_date": "2023-03-01", "end_date": "2023-06-30", "note": "advance paid"}, "amount": 1000, "type": "rent", "date": "2023-02-20", "note": "This is a note", "payment_details": {"payment_method": "cash", "cheque_number": "123456", "bank_name": "Bank Name", "bank_branch": "Bank Branch", "bank_account_number": "*********", "bank_account_name": "Bank Account Name", "online_payment_method": null, "online_payment_transaction_id": null, "online_payment_transaction_status": null, "payment_status": "unpaid"}, "invoice": {"invoice_number": "CcTCpO55d5", "invoice_date": "2023-02-20", "app_invoice_date": "20 Feb, 2023"}}, {"id": 5, "property": "Ocean View Condo", "app_date": "20 Feb, 2023", "attachment": "http://landlord.test/frontend/img/favicon.png", "tenant": {"id": 3, "name": "Tenant", "email": "<EMAIL>", "phone": "***********"}, "rental_agreement": {"id": 3, "amount": 12000, "start_date": "2023-05-01", "end_date": "2023-08-31", "note": "advance paid"}, "amount": 1000, "type": "rent", "date": "2023-02-20", "note": "This is a note", "payment_details": {"payment_method": "cash", "cheque_number": "123456", "bank_name": "Bank Name", "bank_branch": "Bank Branch", "bank_account_number": "*********", "bank_account_name": "Bank Account Name", "online_payment_method": null, "online_payment_transaction_id": null, "online_payment_transaction_status": null, "payment_status": "unpaid"}, "invoice": {"invoice_number": "xE1e74hRmd", "invoice_date": "2023-02-20", "app_invoice_date": "20 Feb, 2023"}}, {"id": 6, "property": "Central Park View", "app_date": "20 Feb, 2023", "attachment": "http://landlord.test/frontend/img/favicon.png", "tenant": {"id": 8, "name": "Tenant", "email": "<EMAIL>", "phone": "***********"}, "rental_agreement": {"id": 1, "amount": 10000, "start_date": "2023-03-01", "end_date": "2023-06-30", "note": "advance paid"}, "amount": 1000, "type": "rent", "date": "2023-02-20", "note": "This is a note", "payment_details": {"payment_method": "cash", "cheque_number": "123456", "bank_name": "Bank Name", "bank_branch": "Bank Branch", "bank_account_number": "*********", "bank_account_name": "Bank Account Name", "online_payment_method": null, "online_payment_transaction_id": null, "online_payment_transaction_status": null, "payment_status": "unpaid"}, "invoice": {"invoice_number": "heWCKjcz5g", "invoice_date": "2023-02-20", "app_invoice_date": "20 Feb, 2023"}}, {"id": 7, "property": "Central Park View", "app_date": "20 Feb, 2023", "attachment": "http://landlord.test/frontend/img/favicon.png", "tenant": {"id": 7, "name": "Tenant", "email": "<EMAIL>", "phone": "***********"}, "rental_agreement": {"id": 1, "amount": 10000, "start_date": "2023-03-01", "end_date": "2023-06-30", "note": "advance paid"}, "amount": 1000, "type": "rent", "date": "2023-02-20", "note": "This is a note", "payment_details": {"payment_method": "cash", "cheque_number": "123456", "bank_name": "Bank Name", "bank_branch": "Bank Branch", "bank_account_number": "*********", "bank_account_name": "Bank Account Name", "online_payment_method": null, "online_payment_transaction_id": null, "online_payment_transaction_status": null, "payment_status": "unpaid"}, "invoice": {"invoice_number": "hXktFYaQHB", "invoice_date": "2023-02-20", "app_invoice_date": "20 Feb, 2023"}}, {"id": 8, "property": "Central Park View", "app_date": "20 Feb, 2023", "attachment": "http://landlord.test/frontend/img/favicon.png", "tenant": {"id": 1, "name": "Tenant", "email": "<EMAIL>", "phone": "***********"}, "rental_agreement": {"id": 1, "amount": 10000, "start_date": "2023-03-01", "end_date": "2023-06-30", "note": "advance paid"}, "amount": 1000, "type": "rent", "date": "2023-02-20", "note": "This is a note", "payment_details": {"payment_method": "cash", "cheque_number": "123456", "bank_name": "Bank Name", "bank_branch": "Bank Branch", "bank_account_number": "*********", "bank_account_name": "Bank Account Name", "online_payment_method": null, "online_payment_transaction_id": null, "online_payment_transaction_status": null, "payment_status": "unpaid"}, "invoice": {"invoice_number": "TPBwSjvYMh", "invoice_date": "2023-02-20", "app_invoice_date": "20 Feb, 2023"}}, {"id": 9, "property": "Ocean View Condo", "app_date": "20 Feb, 2023", "attachment": "http://landlord.test/frontend/img/favicon.png", "tenant": {"id": 4, "name": "Tenant", "email": "<EMAIL>", "phone": "***********"}, "rental_agreement": {"id": 2, "amount": 11000, "start_date": "2023-04-01", "end_date": "2023-07-31", "note": "pre paid"}, "amount": 1000, "type": "rent", "date": "2023-02-20", "note": "This is a note", "payment_details": {"payment_method": "cash", "cheque_number": "123456", "bank_name": "Bank Name", "bank_branch": "Bank Branch", "bank_account_number": "*********", "bank_account_name": "Bank Account Name", "online_payment_method": null, "online_payment_transaction_id": null, "online_payment_transaction_status": null, "payment_status": "unpaid"}, "invoice": {"invoice_number": "BuMM1O3lSF", "invoice_date": "2023-02-20", "app_invoice_date": "20 Feb, 2023"}}], "links": {"first": "http://landlord.test/api/transaction?page=1", "last": "http://landlord.test/api/transaction?page=1", "prev": null, "next": null}, "pagination": {"total": 9, "count": 9, "per_page": 10, "current_page": 1, "total_pages": 1}}}}