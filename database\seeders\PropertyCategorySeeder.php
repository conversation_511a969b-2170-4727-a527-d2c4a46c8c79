<?php

namespace Database\Seeders;

use App\Models\Image;
use Illuminate\Database\Seeder;
use App\Models\Property\PropertyCategory;

class PropertyCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $image_list = [
            'assets/categories/Apartment.png',
            'assets/categories/Building.png',
            'assets/categories/Office.png',
            'assets/categories/Room.png',
            'assets/categories/Room.png',
            'assets/categories/Room.png',
        ];
        foreach ($image_list as $key => $list) {
            $image = Image::create([
                'path' => $list,
            ]);
            $uploaded_property_category[] = $image->id;
        }

        // array of property types
        $property_types = [
            'Apartment',
            'Building',
            'Office',
            'Room',
            'Flat',
            'Land',
        ];

        $i = 0;
        // loop through property types
        foreach ($property_types as $type) {
            PropertyCategory::create([
                'name' => $type,
                'slug' => strtolower(str_replace(' ', '-', $type)),
                'image_id' => $uploaded_property_category[$i],
                'status' => 1,
                'serial' => null,
                'featured' => (($i == (count($property_types) - 1)) || ($i == (count($property_types) - 2))) ? 1 : 0,
            ]);
            $i++;
        }
    }
}

