<?php

use App\Models\Language;
use App\Models\Setting;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;

function getPagination($ITEM)
{
    return view('common.pagination', compact('ITEM'));
}

function setting($name)
{
    $setting_data = Setting::where('name', $name)->first();
    if ($setting_data) {
        return $setting_data->value;
    }

    return null;
}

function findDirectionOfLang()
{
    $data = Language::where('code', Cache::get('locale'))->select('direction')->first();
    return @$data->direction != null ? strtolower(@$data->direction) : '';
}

// for menu active
if (!function_exists('set_menu')) {
    function set_menu(array $path, $active = 'mm-active')
    {
        foreach ($path as $route) {
            if (Route::currentRouteName() == $route) {
                return $active;
            }
        }
        return (request()->is($path)) ? $active : '';
        // return call_user_func_array('Request::is', (array) $path) ? $active : '';
    }
}

// for  submenu list item active
if (!function_exists('menu_active_by_route')) {
    function menu_active_by_route($route)
    {
        return request()->routeIs($route) ? 'mm-show' : 'in-active';
    }
}


function ___($key = null, $replace = [], $locale = null)
{
    $input = explode('.', $key);
    $file = $input[0];
    $term = $input[1];
    $app_local = app()->getLocale();

    $jsonString = file_get_contents(base_path('lang/' . $app_local . '/' . $file . '.json'));
    $data = json_decode($jsonString, true);
    if (@$data[$term]) {
        return $data[$term];
    }

    return $term;
}

// global thumbnails
if (!function_exists('globalAsset')) {
    function globalAsset($path, $default_image = null)
    {
        if ($path == "") {
            return url('backend/uploads/default-images/no-image-default.png');
        } else {
            try {

                if (setting('file_system') == "s3" && Storage::disk('s3')->exists($path) && $path != "") {
                    return Storage::disk('s3')->url($path);
                } else if (setting('file_system') == "local" && file_exists(@$path)) {
                    return url($path);
                } else {
                    if ($default_image == null) {
                        return url('backend/uploads/default-images/no-image-default.png');
                    } else {
                        return url("backend/uploads/default-images/$default_image");
                    }
                }

            } catch (\Exception$c) {
                return url("backend/uploads/default-images/$default_image");
            }

        }
    }
}



// Permission check
if (!function_exists('hasPermission')) {
    function hasPermission($keyword)
    {
        if (in_array($keyword, Auth::user()->permissions ?? [])) {
            return true;
        }
        return false;
    }
}

if (!function_exists('userTheme')) {
    function userTheme()
    {
        $session_theme = Cache::get('user_theme');

        if (isset($session_theme)) {
            return $session_theme;
        } else {
            return 'default-theme';
        }
    }
}

if (!function_exists('leadingZero')) {
    function withLeadingZero($number)
    {

        // $strNumber = $number;
        // if(strlen($strNumber) < 10){
        //     return $strNumber;
        // }

        return $number;
    }
}

if (!function_exists('setEnvironmentValue')) {
    function setEnvironmentValue($envKey, $envValue)
    {
        $envFile = app()->environmentFilePath();
        $str = file_get_contents($envFile);

        $str .= "\n"; // In case the searched variable is in the last line without \n
        $keyPosition = strpos($str, "{$envKey}=");
        $endOfLinePosition = strpos($str, PHP_EOL, $keyPosition);
        $oldLine = substr($str, $keyPosition, $endOfLinePosition - $keyPosition);
        $str = str_replace($oldLine, "{$envKey}={$envValue}", $str);
        $str = substr($str, 0, -1);

        $fp = fopen($envFile, 'w');
        fwrite($fp, $str);
        fclose($fp);
    }
}

if (!function_exists('s3Upload')) {
    function s3Upload($directory, $file)
    {
        $directory = 'public/' . $directory;
        return Storage::disk('s3')->put($directory, $file, 'public');
    }
}

if (!function_exists('s3ObjectCheck')) {
    function s3ObjectCheck($path)
    {
        return Storage::disk('s3')->exists($path);
    }
}



// return path for all api call
if (!function_exists('apiAssetPath')) {
    function apiAssetPath($path)
    {
        if ($path == "") {
            return url('frontend/img/favicon.png');
        } else {
            if (file_exists($path)) {
                return url($path);
            } else {
                return url('frontend/img/favicon.png');
            }

        }
    }
}
if (! function_exists('include_route_files')) {
    /**
     * Loops through a folder and requires all PHP files
     * Searches sub-directories as well.
     *
     * @param $folder
     */
    function include_route_files($folder)
    {
        try {
            $rdi = new RecursiveDirectoryIterator($folder);
            $it = new RecursiveIteratorIterator($rdi);

            while ($it->valid()) {
                if (! $it->isDot() && $it->isFile() && $it->isReadable() && $it->current()->getExtension() === 'php') {
                    require $it->key();
                }

                $it->next();
            }
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }
}

if (!function_exists('update_settings_table')) {
    function update_settings_table($model, $name, $value) {
        $setting = $model::where('name', $name)->first();
        if($setting) {
            $setting->value = $value;
        } else {
            $setting = new $model;
            $setting->name = $name;
            $setting->value = $value;
        }
        $setting->save();
    }
}


if (!function_exists('_translation')) {
    function _translation($key)
    {
        $trans = trans($key);
        try {
            $exp = explode('.', $trans);
            if (count($exp) == 2) {
                return $exp[1];
            } else {
                return $trans;
            }
        } catch (\Throwable $th) {
            return $key;
        }
    }
}
if (!function_exists('verifyUrl')) {
    function verifyUrl($verifier = 'auth')
    {
        $url = config('app.verifier');
        return $url;
    }
}
if (!function_exists('curlIt')) {

    function curlIt($url, $postData = array())
    {
        $url = preg_replace("/\r|\n/", "", $url);
        try {
            $response = Http::timeout(3)->acceptJson()->get($url);
            if ($response->successful()) {
                return $response->json();
            }

            return [];
        } catch (\Exception$e) {
        }
        return [
            'goto' => $url . '&from=browser',
        ];
    }
}
if (!function_exists('gv')) {

    function gv($params, $key, $default = null)
    {
        return (isset($params[$key]) && $params[$key]) ? $params[$key] : $default;
    }
}

if (!function_exists('gbv')) {
    function gbv($params, $key)
    {
        return (isset($params[$key]) && $params[$key]) ? 1 : 0;
    }
}
if (!function_exists('appMode')) {
    function appMode()
    {
        $APP_NESTKEEPER = Config::get('app.APP_NESTKEEPER');
        if ($APP_NESTKEEPER == false) {
            return true;
        } else {
            return false;
        }
    }
}
if (!function_exists('_trans')) {
    function _trans($value)
    {
        try {
            $local = app()->getLocale();

            $langPath = resource_path('lang/' . $local . '/');

            if (!file_exists($langPath)) {
                mkdir($langPath, 0777, true);
            }

            if (str_contains($value, '.')) {
                $new_trns = explode('.', $value);
                $file_name = $new_trns[0];
                $trans_key = $new_trns[1];

                $file_path = $langPath . '' . $file_name . '.json';
                if (file_exists($file_path)) {

                    $file_data = json_decode(file_get_contents($file_path), true);
                    $file_content = new \stdClass;
                    foreach (array_keys($file_data) as $property) {
                        $file_content->{$property} = $file_data[$property];
                    }
                    if (array_key_exists($trans_key, $file_data)) {
                        return $file_content->{$trans_key};
                    } else {
                        $file_content->{$trans_key} = $trans_key;
                        $str = <<<EOT
                                            {
                                            EOT;
                        foreach ($file_content as $key => $val) {
                            if (gettype($val) == 'string') {

                                $line = <<<EOT
                                                                    "{$key}" : "{$val}",\n
                                                                EOT;
                            }
                            if (gettype($val) == 'array') {
                                $line = <<<EOT
                                                                            "{$key}": [\n
                                                                        EOT;
                                $str .= $line;
                                foreach ($val as $lang_key => $lang_val) {

                                    $line = <<<EOT
                                                                            "{$lang_key}": "{$lang_val}",\n
                                                                        EOT;

                                    $str .= $line;
                                }

                                $line = <<<EOT
                                                                        ],\n
                                                                    EOT;
                            }

                            $str .= $line;
                        }
                        $end = <<<EOT
                                                 }
                                            EOT;
                        $str .= $end;

                        $new_setting = [];
                        $new_setting[$trans_key] = $trans_key;
                        $merged_array = array_merge($file_data, $new_setting);
                        $merged_array = json_encode($merged_array, JSON_PRETTY_PRINT);
                        file_put_contents($file_path, $merged_array);
                    }
                } else {

                    fopen($file_path, 'w');

                    $file_content = [];
                    $file_content[$trans_key] = $trans_key;

                    $str = <<<EOT
                                            {
                                            EOT;
                    foreach ($file_content as $key => $val) {
                        if (gettype($val) == 'string') {

                            $line = <<<EOT
                                                                    "{$key}" : "{$val}"\n
                                                                EOT;
                        }
                        if (gettype($val) == 'array') {
                            $line = <<<EOT
                                                                            "{$key}" : [\n
                                                                        EOT;
                            $str .= $line;
                            foreach ($val as $lang_key => $lang_val) {

                                $line = <<<EOT
                                                                            "{$lang_key}" : "{$lang_val}",\n
                                                                        EOT;

                                $str .= $line;
                            }

                            $line = <<<EOT
                                                                        ]\n
                                                                    EOT;
                        }

                        $str .= $line;
                    }
                    $end = <<<EOT
                                                }
                                            EOT;
                    $str .= $end;
                    $file_data = json_encode($str);
                    $file_data = json_decode($file_data, true);
                    $new_setting = [];
                    $new_setting[$trans_key] = $trans_key;
                    file_put_contents($file_path, $file_data);
                }
                return _translation($value);
            } else {

                $trans_key = $value;
                $file_path = resource_path('lang/' . $local . '/' . $local . '.json');

                fopen($file_path, 'w');
                $file_content = [];
                $file_content[$trans_key] = $trans_key;
                $str = <<<EOT
                                            {
                                            EOT;
                foreach ($file_content as $key => $val) {
                    if (gettype($val) == 'string') {

                        $line = <<<EOT
                                                                    "{$key}" : "{$val}",\n
                                                                EOT;
                    }
                    if (gettype($val) == 'array') {
                        $line = <<<EOT
                                                                            "{$key}" : [\n
                                                                        EOT;
                        $str .= $line;
                        foreach ($val as $lang_key => $lang_val) {

                            $line = <<<EOT
                                                                            "{$lang_key}" : "{$lang_val}",\n
                                                                        EOT;

                            $str .= $line;
                        }

                        $line = <<<EOT
                                                                        ],\n
                                                                    EOT;
                    }

                    $str .= $line;
                }
                $end = <<<EOT
                                                }

                                            EOT;
                $str .= $end;

                $file_data = json_encode($str);
                $file_data = json_decode($file_data, true);
                $new_setting = [];
                $new_setting[$trans_key] = $trans_key;
                file_put_contents($file_path, $file_data);

                return _translation($value);
            }
            return _translation($value);
        } catch (Exception $exception) {
            return $value;
        }
    }
}
if (!function_exists('app_url')) {
    function app_url()
    {
        $saas = config('app.saas_module_name', 'Saas');
        $module_check_function = config('app.module_status_check_function', 'moduleStatusCheck');
        if (function_exists($module_check_function) && $module_check_function($saas)) {
            return config('app.url');
        }
        return url('/');
    }
}
function openJSONFile($lang)
{
    $jsonString = [];
    if (File::exists(base_path('resources/lang/'.$lang.'.json'))) {
        $jsonString = file_get_contents(base_path('resources/lang/'.$lang.'.json'));
        $jsonString = json_decode($jsonString, true);
    }
    return $jsonString;
}
function getRating($r)
{
    $str = '';
    if ($r == 5) {
        $str = '
       <i class="fas fa-star active"></i>
       <i class="fas fa-star active"></i>
       <i class="fas fa-star active"></i>
       <i class="fas fa-star active"></i>
       <i class="fas fa-star active"></i>';
    } elseif ($r == 4) {
        $str = '

        <i class="fas fa-star active"></i>
        <i class="fas fa-star active"></i>
        <i class="fas fa-star active"></i>
        <i class="fas fa-star active"></i>
        <i class="fas fa-star " ></i>';
    } elseif ($r == 3) {
        $str = '

        <i class="fas fa-star active"></i>
        <i class="fas fa-star active"></i>
        <i class="fas fa-star active"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>';
    } elseif ($r == 2) {

        $str = '
        <i class="fas fa-star active"></i>
        <i class="fas fa-star active"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>';
    } elseif ($r == 1) {
        $str = '
        <i class="fas fa-star active"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>';
    } elseif ($r == 0) {
        $str = '
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>';
    }

    return $str;
}

function saveJSONFile($lang, $data)
{
    $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    file_put_contents(base_path('resources/lang/'.$lang .'.json'), stripslashes($jsonData));
}
if (!function_exists('envu')) {
    function envu($data = array())
    {
        foreach ($data as $key => $value) {
            if (env($key) === $value) {
                unset($data[$key]);
            }
        }

        if (!count($data)) {
            return false;
        }

        // write only if there is change in content

        $env = file_get_contents(base_path() . '/.env');
        $env = explode("\n", $env);
        foreach ((array) $data as $key => $value) {
            foreach ($env as $env_key => $env_value) {
                $entry = explode("=", $env_value, 2);
                if ($entry[0] === $key) {
                    $env[$env_key] = $key . "=" . (is_string($value) ? '"' . $value . '"' : $value);
                } else {
                    $env[$env_key] = $env_value;
                }
            }
        }
        $env = implode("\n", $env);
        file_put_contents(base_path() . '/.env', $env);
        return true;
    }
}
if (!function_exists('is_Admin')) {

    function is_Admin()
    {
        if (auth()->user()->role->slug == 'superadmin' || auth()->user()->role->slug == 'admin' || auth()->user()->role->slug == 'landlord') {
            return true;
        } else {
            return false;
        }
    }
}

if (!function_exists('isConnected')) {
    function isConnected()
    {
        $connected = @fsockopen("www.google.com", 80);
        if ($connected) {
            fclose($connected);
            return true;
        }

        return false;
    }
}
if (!function_exists('isTestMode')) {
    function isTestMode()
    {
        if (env('APP_LAND') == true && env('APP_ENV') == 'local') {
            return true;
        } else {
            return false;
        }
    }
}
if (!function_exists('aboutSystem')) {
    function aboutSystem()
    {
        $data = [
            'version' => '',
            'release_date' => '',
        ];
        try {
            $about_system = base_path('version.json');
            $about_system = file_get_contents($about_system);
            $about_system = json_decode($about_system, true);
            $data['version'] = $about_system['version'];
            $data['release_date'] = $about_system['release_date'];
            return $data;
        } catch (\Throwable$th) {
            return $data;
        }
    }
}
if (!function_exists('putEnvConfigration')) {
    function putEnvConfigration($envKey, $envValue)
    {
        $envValue = str_replace('\\', '\\' . '\\', $envValue);
        $value = '"' . $envValue . '"';
        $envFile = app()->environmentFilePath();
        $str = file_get_contents($envFile);

        $str .= "\n";
        $keyPosition = strpos($str, "{$envKey}=");

        if (is_bool($keyPosition)) {

            $str .= $envKey . '="' . $envValue . '"';
        } else {
            $endOfLinePosition = strpos($str, "\n", $keyPosition);
            $oldLine = substr($str, $keyPosition, $endOfLinePosition - $keyPosition);
            $str = str_replace($oldLine, "{$envKey}={$value}", $str);

            $str = substr($str, 0, -1);
        }

        if (!file_put_contents($envFile, $str)) {
            return false;
        } else {
            return true;
        }
    }
}

if (!function_exists('AuthPermitCheck')) {
    function AuthPermitCheck()
    {
         // Check if all required files exist
         $WelcomeNote = Storage::disk('local')->exists('.WelcomeNote') ? Storage::disk('local')->get('.WelcomeNote') : null;
         $CheckEnvironment = Storage::disk('local')->exists('.CheckEnvironment') ? Storage::disk('local')->get('.CheckEnvironment') : null;
         $LicenseVerification = Storage::disk('local')->exists('.LicenseVerification') ? Storage::disk('local')->get('.LicenseVerification') : null;
         $DatabaseSetup = Storage::disk('local')->exists('.DatabaseSetup') ? Storage::disk('local')->get('.DatabaseSetup') : null;
         $AdminSetup = Storage::disk('local')->exists('.AdminSetup') ? Storage::disk('local')->get('.AdminSetup') : null;
         $Complete = Storage::disk('local')->exists('.Complete') ? Storage::disk('local')->get('.Complete') : null;

         // Check if all required files are present
        return  $allFilesExist = $WelcomeNote && $CheckEnvironment && $LicenseVerification && $DatabaseSetup && $AdminSetup && $Complete;
    }
}


