<?php

namespace App\Http\Controllers;

use App\Models\MasterOrder;
use Illuminate\Http\Request;

class MasterOrderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\MasterOrder  $masterOrder
     * @return \Illuminate\Http\Response
     */
    public function show(MasterOrder $masterOrder)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\MasterOrder  $masterOrder
     * @return \Illuminate\Http\Response
     */
    public function edit(MasterOrder $masterOrder)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\MasterOrder  $masterOrder
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, MasterOrder $masterOrder)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\MasterOrder  $masterOrder
     * @return \Illuminate\Http\Response
     */
    public function destroy(MasterOrder $masterOrder)
    {
        //
    }
}
