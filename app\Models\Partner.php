<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Model;

class Partner extends Model
{
    use HasFactory;

    protected $fillable =[
        'name',
        'image_id',
        'status',
    ];

    public function image(): BelongsTo
    {
        return $this->belongsTo(Image::class);
    }
}
