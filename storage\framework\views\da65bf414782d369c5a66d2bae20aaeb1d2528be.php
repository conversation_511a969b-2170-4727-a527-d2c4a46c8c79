<?php $__env->startSection('title'); ?>
<?php echo e(@$data['title']); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="page-content">

    
    <div class="page-header">
        <div class="row">
            <div class="col-sm-6">
                <h4 class="bradecrumb-title mb-1"><?php echo e($data['title']); ?></h1>
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(___('common.home')); ?></a></li>
                        <li class="breadcrumb-item"><?php echo e($data['title']); ?></li>
                    </ol>
            </div>
        </div>
    </div>
    

    <!--  table content start -->
    <div class="table-content table-basic mt-20">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><?php echo e(_trans('landlord.Advertisement')); ?></h4>
                <?php if(hasPermission('property_create')): ?>
                <a href="<?php echo e(route('advertisements.create')); ?>" class="btn btn-lg ot-btn-primary">
                    <span><i class="fa-solid fa-plus"></i> </span>
                    <span class=""><?php echo e(_trans('landlord.Add Advertisement')); ?></span>
                </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered user-table">
                        <thead class="thead">
                            <tr>
                                <th class="serial"><?php echo e(_trans('landlord.SR No..')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Property Name')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Advertisement Type')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Booking Amount')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Rent Amount')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Rent Type')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Rent Start Date')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Rent End Date')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Sell Amount')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Sell Start Date')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Negotiable')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Status')); ?></th>
                                <th class="purchase"><?php echo e(_trans('landlord.Approval Status')); ?></th>
                                <?php if(hasPermission('user_update') || hasPermission('user_delete')): ?>
                                <th class="action"><?php echo e(_trans('landlord.action')); ?></th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody class="tbody">
                            <?php $__empty_1 = true; $__currentLoopData = $data['property']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>

                            <tr id="row_<?php echo e($row->id); ?>">
                                <td class="serial"><?php echo e(++$key); ?></td>
                                <td>
                                    <?php echo e(@$row->property->name); ?>

                                </td>
                                <td>
                                    <?php echo e(@$row->advertisement_type == 1 ? 'Rent' : 'Sell'); ?>

                                </td>
                                <td>
                                    <?php echo e(@$row->booking_amount); ?>

                                </td>
                                <?php if(@$row->ade): ?>

                                <?php endif; ?>
                                <td>
                                    <?php echo e(@$row->rent_amount); ?>

                                </td>
                                <td>

                                    <?php echo e(@$row->rent_type == 1 ? 'Monthly' : 'Yearly'); ?>

                                </td>
                                <td>
                                    <?php echo e(@$row->rent_start_date); ?>

                                </td>
                                <td>
                                    <?php echo e(@$row->rent_end_date); ?>

                                </td>
                                <td>
                                    <?php echo e(@$row->sell_amount); ?>

                                </td>
                                <td>
                                    <?php echo e(@$row->sell_start_date); ?>

                                </td>
                                <td>
                                    <?php echo e(@$row->negotiable == 1 ? 'Negotiable' : 'Not Negotiable'); ?>

                                </td>
                                <td>
                                    <?php if($row->status == App\Enums\Status::ACTIVE): ?>
                                    <span class="badge-basic-success-text"><?php echo e(___('common.active')); ?></span>
                                    <?php else: ?>
                                    <span class="badge-basic-danger-text"><?php echo e(___('common.inactive')); ?></span>
                                    <?php endif; ?>
                                </td>

                                <td>
                                    <?php if($row->approval_status == App\Enums\ApprovalStatus::APPROVED): ?>
                                    <span class="badge-basic-success-text"><?php echo e(_trans('landlord.Approved')); ?></span>
                                    <?php elseif($row->approval_status == App\Enums\ApprovalStatus::PENDING): ?>
                                    <span class="badge-basic-warning-text"><?php echo e(_trans('landlord.Pending')); ?></span>
                                    <?php elseif($row->approval_status == App\Enums\ApprovalStatus::REJECTED): ?>
                                    <span class="badge-basic-danger-text"><?php echo e(_trans('landlord.Rejected')); ?></span>
                                    <?php elseif($row->approval_status == App\Enums\ApprovalStatus::CANCELLED): ?>
                                    <span class="badge-basic-danger-text"><?php echo e(_trans('landlord.Cancelled')); ?></span>
                                    <?php elseif($row->approval_status == App\Enums\ApprovalStatus::DELETED): ?>
                                    <span class="badge-basic-danger-text"><?php echo e(_trans('landlord.Deleted')); ?></span>
                                    <?php endif; ?>
                                </td>

                                <?php if(hasPermission('user_update') || hasPermission('user_delete')): ?>
                                <td class="action">
                                    <div class="dropdown dropdown-action">
                                        <button type="button" class="btn-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fa-solid fa-ellipsis"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li>
                                                <a class="dropdown-item" href="<?php echo e(route('admin.properties.details', [@$row->id, 'basicInfo'])); ?>">
                                                    <span class="icon mr-8"><i class="fa-solid fa-eye"></i></span>
                                                    <span><?php echo e(___('common.view')); ?></span>
                                                </a>
                                            </li>
                                            <?php if(hasPermission('user_delete')): ?>
                                            <li>
                                                <a class="dropdown-item" href="javascript:void(0);" onclick="delete_row('advertisements/delete', <?php echo e(@$row->id); ?>)">
                                                    <span class="icon mr-12"><i class="fa-solid fa-trash-can"></i></span>
                                                    <span><?php echo e(___('common.delete')); ?></span>
                                                </a>
                                            </li>
                                            <?php endif; ?>

                                            <?php if(hasPermission('ad_action_permission')): ?>
                                            <?php if($row->approval_status == 2): ?>
                                            <li>
                                                <a class="dropdown-item" href="<?php echo e(route('advertisements.approveStatus', [@$row->id, 'approve'])); ?>">
                                                    <span class="icon mr-8"><i class="fa-solid fa-circle-check"></i></span>
                                                    <span><?php echo e(___('common.Approve')); ?></span>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="<?php echo e(route('advertisements.approveStatus', [@$row->id, 'reject'])); ?>">
                                                    <span class="icon mr-8"><i class="fa-solid fa-circle-xmark"></i></span>
                                                    <span><?php echo e(___('common.Reject')); ?></span>
                                                </a>
                                            </li>
                                            <?php elseif($row->approval_status == 1): ?>
                                            <li>
                                                <a class="dropdown-item" href="<?php echo e(route('advertisements.approveStatus', [@$row->id, 'reject'])); ?>">
                                                    <span class="icon mr-8"><i class="fa-solid fa-circle-xmark"></i></span>
                                                    <span><?php echo e(___('common.Reject')); ?></span>
                                                </a>
                                            </li>
                                            <?php elseif($row->approval_status == 3): ?>
                                            <li>
                                                <a class="dropdown-item" href="<?php echo e(route('advertisements.approveStatus', [@$row->id, 'approve'])); ?>">
                                                    <span class="icon mr-8"><i class="fa-solid fa-circle-check"></i></span>
                                                    <span><?php echo e(___('common.Approve')); ?></span>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                            <?php endif; ?>
                                        </ul>
                                    </div>

                                </td>
                                <?php endif; ?>
                            </tr>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="100%" class="text-center gray-color">
                                    <img src="<?php echo e(asset('images/no_data.svg')); ?>" alt="" class="mb-primary" width="100">
                                    <p class="mb-0 text-center">No data available</p>
                                    <p class="mb-0 text-center text-secondary font-size-90">
                                        Please add new entity regarding this table</p>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <!--  table end -->
                <!--  pagination start -->
                <div class="ot-pagination pagination-content d-flex justify-content-end align-content-center py-3">
                    <nav aria-label="Page navigation example">
                        <ul class="pagination justify-content-between">
                            <?php echo $data['property']->links(); ?>

                        </ul>
                    </nav>
                </div>
                <!--  pagination end -->
            </div>
        </div>
    </div>
    <!--  table content end -->
</div>
<?php $__env->stopSection(); ?>


<?php $__env->startPush('script'); ?>
<?php echo $__env->make('backend.partials.delete-ajax', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\landlord\resources\views/backend/advertisement/index.blade.php ENDPATH**/ ?>