<?php

namespace App\Repositories;
use App\Interfaces\CartInterface;
use App\Models\Cart;
use App\Models\Wishlist;
use App\Traits\ReturnFormatTrait;
use Brian2694\Toastr\Facades\Toastr;

class CartRepository implements CartInterface
{
    use ReturnFormatTrait;

    private $model;
    private $wishlistModel;

    public function __construct(Cart $model, Wishlist $wishlistModel)
    {
        $this->model = $model;
        $this->wishlistModel = $wishlistModel;
    }

    public function store($request)
    {
        try {

            $data          = [];

            if ($cart = $this->model->where('property_id', $request->property_id)->where('tenant_id', \Auth::user()->id)->first()) {
                $data['count'] = $this->model->where('tenant_id', \Auth::user()->id)->count();
                $message = __('Property Already Added');
                toastr()->success($message);

                return $this->responseWithSuccess(___('alert.already_added'), $data);
            } else {

                $cart              = new $this->model;
                $cart->property_id = $request->property_id;
                $cart->amount      = $request->amount;
                $cart->tenant_id   = \Auth::user()->id;
                $cart->save();

            }



            $data['count'] = $this->model->where('tenant_id', \Auth::user()->id)->count();




            return $this->responseWithSuccess(___('alert.added_successfully'), $data);
        } catch (\Throwable $th) {
            return $this->responseWithError(___('alert.something_went_wrong_please_try_again'), []);
        }

    }
    public function wishlistStore($request)
    {
        try {

            $data          = [];

            if ($cart = $this->wishlistModel->where('property_id', $request->property_id)->where('user_id', \Auth::user()->id)->first()) {
                $data['count'] = $this->wishlistModel->where('user_id', \Auth::user()->id)->count();
                $message = __('Wishlist Already Added');
                toastr()->success($message);


            } else {

                $cart              = new $this->wishlistModel;
                $cart->property_id = $request->property_id;
                $cart->user_id   = \Auth::user()->id;
                $cart->save();

            }



            $data['count'] = $this->wishlistModel->where('user_id', \Auth::user()->id)->count();




            return $this->responseWithSuccess(___('alert.added_successfully'), $data);
        } catch (\Throwable $th) {
            return $this->responseWithError(___('alert.something_went_wrong_please_try_again'), []);
        }

    }
}
