<?php

namespace App\Http\Controllers;

use App\Models\Property\PropertyTenant;
use Illuminate\Http\Request;

class PropertyTenantController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Property\PropertyTenant  $propertyTenant
     * @return \Illuminate\Http\Response
     */
    public function show(PropertyTenant $propertyTenant)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Property\PropertyTenant  $propertyTenant
     * @return \Illuminate\Http\Response
     */
    public function edit(PropertyTenant $propertyTenant)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Property\PropertyTenant  $propertyTenant
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, PropertyTenant $propertyTenant)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Property\PropertyTenant  $propertyTenant
     * @return \Illuminate\Http\Response
     */
    public function destroy(PropertyTenant $propertyTenant)
    {
        //
    }
}
