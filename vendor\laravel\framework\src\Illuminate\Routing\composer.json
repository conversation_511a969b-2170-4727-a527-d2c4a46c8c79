{"name": "illuminate/routing", "description": "The Illuminate Routing package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "ext-filter": "*", "ext-hash": "*", "illuminate/collections": "^9.0", "illuminate/container": "^9.0", "illuminate/contracts": "^9.0", "illuminate/http": "^9.0", "illuminate/macroable": "^9.0", "illuminate/pipeline": "^9.0", "illuminate/session": "^9.0", "illuminate/support": "^9.0", "symfony/http-foundation": "^6.0", "symfony/http-kernel": "^6.0", "symfony/routing": "^6.0"}, "autoload": {"psr-4": {"Illuminate\\Routing\\": ""}}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "suggest": {"illuminate/console": "Required to use the make commands (^9.0).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}