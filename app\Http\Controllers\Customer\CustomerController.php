<?php

namespace App\Http\Controllers\Customer;

use Carbon\Carbon;
use App\Models\Cart;
use App\Models\User;
use App\Models\Image;
use App\Models\Order;
use App\Models\Rental;
use App\Models\Document;
use App\Models\Wishlist;
use App\Models\DuePayment;
use App\Models\BankAccount;
use App\Models\MasterOrder;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\BillingAddress;
use App\Models\EmergencyContact;
use App\Models\Locations\Country;
use App\Models\Property\Property;
use App\Traits\CommonHelperTrait;

use App\Http\Controllers\Controller;
use App\Models\Property\Transaction;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Console\View\Components\Alert;
use App\Http\Requests\Order\BillingAddressRequest;

class CustomerController extends Controller
{
    use  CommonHelperTrait;
    public function __construct()
    {
        $this->middleware('customer.auth');
    }

    public function logout()
    {
        Auth::guard('customer')->logout();
        Auth::logout();
        return redirect()->route('home');
    }

    public function dashboard()
    {
        $data['title']              =  'Dashboard';
        $data['breadcrumb']         =  ['Dashboard' => 'customer/dashboard'];
        $data['profile']            =  auth()->guard('customer')->user();
        $data['order']              =  Order::where('tenant_id', Auth::user()->id)->count();
        $data['coupon']             =  Order::where('tenant_id', Auth::user()->id)->whereNotNull('coupon_code')->count();
        $data['totalAmount']        =  Order::where('tenant_id', Auth::user()->id)->sum('total_amount');
        $data['complete_order']     =  MasterOrder::where('tenant_id', Auth::user()->id)->where('payment_status','approved')->count();
        $data['cart']               =  Cart::where('tenant_id',  Auth::user()->id)->count();
        $data['wishlist']           =  Wishlist::where('user_id', Auth::user()->id)->count();
        $data['order_history']      =  MasterOrder::where('tenant_id', Auth::user()->id)->get();

        // dd($data['order_history']->toArray());
        $data['statistics'] = [
            [
                'title' => 'Total Orders',
                'value' => 0,
            ],
            [
                'title' => 'Total Products',
                'value' => 0,
            ],
            [
                'title' => 'Total Reviews',
                'value' => 0,
            ],
            [
                'title' => 'Total Wishlist',
                'value' => 0,
            ],
        ];

        $data['wishlist'] = Wishlist::where('user_id', Auth::user()->id)->take(4)->get();
        return view('frontend.customer.dashboard', compact('data'));
    }

    public function purchaseHistory()
    {
        $data['title'] = 'Purchase History';
        $data['breadcrumb'] = ['Dashboard' => 'customer/dashboard'];
        $data['order_history']      =  MasterOrder::where('tenant_id', Auth::user()->id)->paginate(10);

        return view('frontend.customer.purchase_history', compact('data'));
    }

    public function myWishlist()
    {
        $data['wishlist'] = Wishlist::where('user_id', Auth::user()->id)->paginate(3);
        return view('frontend.customer.wishlist', compact('data'));
    }

    public function myOrders()
    {
        $data['master_orders'] = MasterOrder::where('tenant_id', Auth::user()->id)->get();
        $data['orders'] = Order::where('tenant_id', Auth::user()->id)->get();


        return view('frontend.customer.order', compact('data'));
    }

    public function duePayment()
    {
        $data['due_payments'] = DuePayment::where('tenant_id', Auth::user()->id)->get();

        return view('frontend.customer.due_payment', compact('data'));
    }
    public function checkout()
    {
        $data['carts'] = Cart::where('tenant_id', Auth::user()->id)->get();
        $cartAmount = $data['carts']->pluck('amount')->toArray();
        // dd($cartAmount);
        $discountAmount = $data['carts']->pluck('property_id');

        $data['country']    = Country::get();

        $data['totalAmount']    = Cart::where('tenant_id', Auth::user()->id)->sum('amount');
        $data['address']        = BillingAddress::where('user_id', Auth::id())->get();

        return view('frontend.customer.checkout', compact('data'));
    }

    public function placeOrder(Request $request)
    {




        $master                              = new MasterOrder;
        $master->payment_method              = $request->payment_method;
        $master->tenant_id                   = Auth::id();
        $master->order_date                  = Carbon::now()->format('Y-m-d');
        $master->order_number                = Str::random(10);

        $master->save();
        $cart_items = Cart::where('tenant_id', Auth::id())->get();

        $totalCartAmount = 0;
        foreach ($cart_items as $cart_item) {
            // Get the Property ID associated with the cart item
            $property_id = $cart_item->property_id;

            // Retrieve the Property object using the Property ID
            $property = Property::find($property_id);

            $order = new Order;
            $order->master_order_id =  $master->id;
            $order->property_id = $property->id;
            $order->tenant_id = Auth::id();
            $order->builder_id = 1;
            $order->landowner_id = 1;
            $order->advertisement_id = 1;
            $order->total_amount = $property->rent_amount;
            $order->discount_amount = $property->discount_amount;
            $order->campaign_name = "test";
            $order->coupon_code = Str::random(4);
            $order->coupon_discount_amount = 434;
            $order->save();

            // $totalCartAmount = $totalCartAmount +
            $currentDate = Carbon::now();

            for ($i = 1; $i <=  $cart_item->durations; $i++) {
                $duePayment = new DuePayment();
                $duePayment->months                 = $currentDate->addMonths($i)->format('F');;
                $duePayment->master_order_id        =  $master->id;
                $duePayment->property_id            = $property->id;
                $duePayment->tenant_id              = Auth::id();
                $duePayment->order_number           = $master->order_number;
                $duePayment->property_name          = $property->name;
                $duePayment->amount                 = $property->rent_amount;
                $duePayment->payment_status         = "Paid";
                $duePayment->save();
            }
            $nextMonth = $currentDate->addMonth();
            // Create a new DuePayment for the next month and save to the database
            $duePayment = new DuePayment();
            $duePayment->months = $nextMonth->format('F');
            $duePayment->master_order_id =  $master->id;
                $duePayment->property_id = $property->id;
                $duePayment->tenant_id = Auth::id();
                $duePayment->order_number = $master->order_number;
                $duePayment->property_name = $property->name;
                $duePayment->amount = $property->rent_amount;
                $duePayment->payment_status = "Due";
            $duePayment->save();
        }

        if ($request->has('billing_address_id') && $request->billing_address_id != '') {
            $master->billing_address_id          = $request->billing_address_id;
            $master->save();
        } else {
            $billingAddress                             = new BillingAddress;
            $billingAddress->name                       = $request->name;
            $billingAddress->user_id                    = Auth::id();
            $billingAddress->address                    = $request->address;
            $billingAddress->country_id                 = $request->country;
            $billingAddress->postal                     = $request->postal;
            $billingAddress->phone                      = $request->phone;
            $billingAddress->email                      = $request->email;
            // $billingAddress->terms_and_condition    = $request->terms_and_condition;
            $billingAddress->save();

            $master->billing_address_id          = $billingAddress->id;
            $master->save();
        }

        Cart::where('tenant_id', Auth::id())->delete();
        toastr()->success('Your order has been placed successfully.');

        return redirect()->route('home');
    }


    public function checkOutProcess(Request $request)
    {


        $cart = Cart::where('id', $request->cart_id)->first();
        $months = (int) $request->month;
        $cart->durations = $months;
        $cart->amount = $months * $cart->property->rent_amount;
        $cart->save();
        $cart['totalAmount'] = Cart::where('tenant_id', Auth::user()->id)->sum('amount');

        // Return the updated data as a JSON response
        return response()->json([
            'data' => $cart,
            'message' => 'Cart updated successfully'

        ]);
    }
    public function shopping()
    {

        return view('frontend.customer.shopping');
    }
    public function payment()
    {
        return view('frontend.customer.payment');
    }
    public function myCoupon()
    {
        return view('frontend.customer.coupon');
    }
    public function notification()
    {
        return view('frontend.customer.notification');
    }

    // Cart Start
    public function cart()
    {
        $data['carts'] = Cart::where('tenant_id', Auth::user()->id)->get();
        $data['totalAmount'] = Cart::where('tenant_id', Auth::user()->id)->sum('amount');
        return view('frontend.customer.cart', compact('data'));
    }
    public function removeCart($id)
    {
        $cart = Cart::find($id);
        if ($cart) {
            $cart->delete();

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false]);
    }
    // Cart End

    public function removeWishlist($id)
    {
        $wishlist = Wishlist::find($id);
        if ($wishlist) {
            $wishlist->delete();

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false]);
    }



    public function orderDetails($id)
    {
        $data['order'] = Order::findOrFail($id);

        // Additional logic to retrieve and pass any necessary data to the view

        return view('frontend.customer.order_details', compact('data'));
    }

    public function invoiceDownload($id){
        $data['order'] = Order::findOrFail($id);
        $pdf = \PDF::setOptions([
            'isHtml5ParserEnabled' => true, 'isRemoteEnabled' => true,
            'logOutputFile' => storage_path('logs/log.htm'),
            'tempDir' => storage_path('logs/'),
        ])->loadView('frontend.customer.invoice', compact('data'));
        return $pdf->download();
        // return $pdf->stream();
    }





    public function refundDetails()
    {
        return view('frontend.customer.refund_details');
    }
    public function profile()
    {
        $data['profile'] = User::where('id', Auth::user()->id)->first();
        $data['address'] = BillingAddress::where('user_id', Auth::user()->id)->get();
        $data['accounts'] = BankAccount::where('user_id', Auth::user()->id)->first();
        $data['agreement'] = Rental::where('property_tenant_id', Auth::user()->id)->get();
        $data['transactionHistory'] = Transaction::where('property_tenant_id', Auth::user()->id)->get();
        $data['emergencyContact'] = EmergencyContact::where('property_tenant_id', Auth::user()->id)->get();
        $data['documents'] = Document::where('user_id', Auth::user()->id)->get();
        $data['country'] = Country::get();
        return view('frontend.customer.profile', compact('data'));
    }
    public function updateProfile(Request $request)
    {
        $user = Auth::user();


        $validatedData = $request->validate([
            'name'              => 'required|string|max:255',
            'email'             => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone'             => 'required',
            'date_of_birth'     => 'required|date',
            'occupation'        => 'required'
        ]);

        $user->name             = $validatedData['name'];
        $user->email            = $validatedData['email'];
        $user->phone            = $validatedData['phone'];
        $user->date_of_birth    = $validatedData['date_of_birth'];
        $user->occupation       = $validatedData['occupation'];
        $user->image_id         = $this->UploadImageCreate($request->image, 'backend/uploads/users');
        $user->save();

        return redirect()->route('customer.profile')->with('success', 'Profile updated successfully!');
    }
    public function updateAccount(Request $request)
    {
        try {
            $account = BankAccount::where('user_id', Auth::user()->id)->first();


            $validatedData = $request->validate([
                'account_number'                         => 'required|max:255',
                'account_name'                           => 'required|max:255',
                'name'                          => 'required|max:255',
                'branch'                             => 'required|max:255'

            ]);

            $account->account_number                       = $validatedData['account_number'];
            $account->account_name              = $validatedData['account_name'];
            $account->name                        = $validatedData['name'];
            $account->branch                           = $validatedData['branch'];
            $account->save();

            return redirect()->route('customer.profile')->with('success', 'Account updated successfully!');
        } catch (\Throwable $th) {
            dd($th);
        }
    }
    public function editAddress($id)
    {
        $address = BillingAddress::findOrFail($id);
        return view('frontend.customer.profile', compact('address'));
    }

    public function editEmergency($id)
    {
        $emergency = EmergencyContact::findOrFail($id);
        return view('frontend.customer.profile', compact('emergency'));
    }

    public function updateAddress(Request $request, $id)
    {
        $address = BillingAddress::findOrFail($id);
        $address->name = $request->input('name');
        $address->address = $request->input('address');
        $address->country_id = $request->input('country_id');
        $address->email = $request->input('email');
        $address->phone = $request->input('phone');
        $address->save();

        return redirect()
            ->route('customer.profile')
            ->with('success', 'Address updated successfully!');
    }
    public function updateEmergency(Request $request, $id)
    {
        $emergencyContact = EmergencyContact::findOrFail($id);
        $emergencyContact->name = $request->input('name');
        $emergencyContact->occupied = $request->input('occupied');
        $emergencyContact->relation = $request->input('relation');
        $emergencyContact->email = $request->input('email');
        $emergencyContact->phone = $request->input('phone');
        $emergencyContact->save();

        return redirect()
            ->route('customer.profile')
            ->with('success', 'Emergency contact updated successfully!');
    }
    public function updatePassword(Request $request)
    {

        $data = Validator::make($request->all(), [
            'password' => 'required',
            'new_password' => 'required|string|min:8',
            'confirm_password' => 'required|string|min:8',
        ]);
        if ($data->fails()) {

            return redirect()->route('customer.profile')->withError('Validations failed must be password and confirm password 8 characters and string');
        }
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('customer.profile')->with('Error', 'User Not Found');
        }

        $currentPassword = $request->input('password');
        $newPassword = $request->input('new_password');
        $confirmPassword = $request->input('confirm_password');


        if (!Hash::check($currentPassword, $user->password)) {
            return redirect()->route('customer.profile')->withError('Current password is incorrect');
        }

        if ($newPassword !== $confirmPassword) {
            return redirect()->route('customer.profile')->withError('Password is not matched');
        }

        $user->password = Hash::make($newPassword);
        $user->save();

        return redirect()->route('customer.profile')->with('success', 'Your password has been changed.');
    }

    public function referral()
    {
        return view('frontend.customer.referral');
    }
    public function refund()
    {
        return view('frontend.customer.refund');
    }
    public function supportTicket()
    {
        return view('frontend.customer.support_ticket');
    }
}
