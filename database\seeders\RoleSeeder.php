<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;


class RoleSeeder extends Seeder
{
    public function run()
    {
        Role::create([
            'name' => 'Super Admin',
            'permissions' => [
                'user_read',
                'user_create',
                'user_update',
                'user_delete',
                'role_read',
                'role_create',
                'role_update',
                'role_delete',
                'language_read',
                'language_create',
                'language_update',
                'language_update_terms',
                'language_delete',
                'general_settings_read',
                'general_settings_update',
                'storage_settings_read',
                'storage_settings_update',
                'recaptcha_settings_read',
                'recaptcha_settings_update',
                'email_settings_read',
                'email_settings_update',
                'testimonial_read',
                'testimonial_create',
                'testimonial_update',
                'testimonial_delete',
                'blogs_read',
                'blogs_create',
                'blogs_update',
                'blogs_delete',
                'blog_categories_read',
                'blog_categories_create',
                'blog_categories_update',
                'blog_categories_delete',
                'category_read',
                'category_create',
                'category_update',
                'category_delete',
                'how-it-work_read',
                'how-it-work_create',
                'how-it-work_update',
                'how-it-work_delete',
                'business_model_create',
                'business_model_update',
                'business_model_delete',
                'section_titles_update',
                'partners_read',
                'partners_create',
                'partners_update',
                'partners_delete',
                'feature_read',
                'feature_create',
                'feature_update',
                'feature_delete',
                'hero_section_read',
                'hero_section_create',
                'hero_section_update',
                'hero_section_delete',
                'tenant_read',
                'tenant_create',
                'tenant_update',
                'tenant_delete',
                'property_read',
                'property_create',
                'property_update',
                'property_delete',
                'contact_read',
                'contact_delete',
                'mail_subscribe_read',
                'mail_subscribe_delete',
                'about_update',
                'property_category_read',
                'property_category_create',
                'property_category_update',
                'property_category_delete',
                'property_facility_type_read',
                'property_facility_type_create',
                'property_facility_type_update',
                'property_facility_type_delete',
                'ad_action_permission'
            ],
        ]);
        Role::create([
            'name' => 'Admin',
            'permissions' => [
                'user_read',
                'user_create',
                'user_update',
                'user_delete',
                'role_read',
                'role_create',
                'role_update',
                'role_delete',
                'language_read',
                'language_create',
                'language_update_terms',
                'general_settings_read',
                'general_settings_update',
                'storage_settings_read',
                'storage_settings_read',
                'recaptcha_settings_update',
                'email_settings_read',
                'testimonial_read',
                'testimonial_create',
                'testimonial_update',
                'testimonial_delete',
                'blogs_read',
                'blogs_create',
                'blogs_update',
                'blogs_delete',
                'blog_categories_read',
                'blog_categories_create',
                'blog_categories_update',
                'blog_categories_delete',
                'category_read',
                'category_create',
                'category_update',
                'category_delete',
                'how-it-work_read',
                'how-it-work_create',
                'how-it-work_update',
                'how-it-work_delete',
                'business_model_create',
                'business_model_update',
                'business_model_delete',
                'section_titles_update',
                'partners_read',
                'partners_create',
                'partners_update',
                'partners_delete',
                'feature_read',
                'feature_create',
                'feature_update',
                'feature_delete',
                'hero_section_read',
                'hero_section_create',
                'hero_section_update',
                'hero_section_delete',
                'tenant_read',
                'tenant_create',
                'tenant_update',
                'tenant_delete',
                'contact_read',
                'contact_delete',
                'mail_subscribe_read',
                'mail_subscribe_delete',
                'about_update',
                'property_category_read',
                'property_category_create',
                'property_category_update',
                'property_category_delete',
                'ad_action_permission'

            ],
        ]);
        Role::create([
            'name' => 'Land Owner',
            'permissions' => [
                'property_read',
                'property_create',
                'property_update',
                'property_delete',
            ],
        ]);
        Role::create([
            'name' => 'Land Lord',
            'permissions' => [
                'property_read',
                'property_create',
                'property_update',
                'property_delete',
            ],
        ]);
        Role::create([
            'name' => 'Tenant',
            'permissions' => [],
        ]);
        Role::create([
            'name' => 'Staff',
            'permissions' => [
                'user_read',
                'role_read',
                'language_read',
                'testimonial_read',
                'category_read',
                'how-it-work_read',
            ],
        ]);
    }
}
