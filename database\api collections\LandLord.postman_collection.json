{"info": {"_postman_id": "d061d15c-bdef-4fe9-9e5f-cfd894ae5665", "name": "LandLord", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "6693071"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"123456\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/login", "host": ["{{url}}api"], "path": ["login"]}}, "response": []}, {"name": "Register", "request": {"auth": {"type": "apikey"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\":\"ridom\",\n    \"email\":\"<EMAIL>\",\n    \"password\":\"12345678\",\n    \"type\":\"landlord\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/register", "host": ["{{url}}api"], "path": ["register"], "query": [{"key": "role_id", "value": "", "disabled": true}]}}, "response": []}, {"name": "email/verify/send/otp", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/email/send/otp", "host": ["{{url}}api"], "path": ["email", "send", "otp"]}}, "response": []}, {"name": "email/verified/otp", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"3850\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/email/verified/otp", "host": ["{{url}}api"], "path": ["email", "verified", "otp"]}}, "response": []}, {"name": "email/forget-password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/email/forgot-password", "host": ["{{url}}api"], "path": ["email", "forgot-password"]}}, "response": []}, {"name": "email/reset-password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"otp\":\"124578\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"12345678\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/email/reset-password", "host": ["{{url}}api"], "path": ["email", "reset-password"]}}, "response": []}]}, {"name": "property", "item": [{"name": "steps -not now", "item": [{"name": "Property Details (All tab list)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/property/info-list", "host": ["{{url}}api"], "path": ["property", "info-list"]}}, "response": []}, {"name": "Property/{id}/details/{type}", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "r proepty", "type": "text"}, {"key": "address", "value": "banani", "type": "text"}, {"key": "description", "value": "sgdfhfdhfd", "type": "text"}, {"key": "user_id", "value": "4", "type": "text", "disabled": true}, {"key": "default_image", "value": "1", "type": "text", "disabled": true}, {"key": "property_type_id", "value": "1", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}api/property/1/details/tenant", "host": ["{{url}}api"], "path": ["property", "1", "details", "tenant"]}}, "response": []}]}, {"name": "Property List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "r proepty", "type": "text", "disabled": true}, {"key": "address", "value": "banani", "type": "text", "disabled": true}, {"key": "description", "value": "sgdfhfdhfd", "type": "text", "disabled": true}, {"key": "user_id", "value": "4", "type": "text", "disabled": true}, {"key": "default_image", "value": "1", "type": "text", "disabled": true}, {"key": "property_type_id", "value": "1", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}api/property/list", "host": ["{{url}}api"], "path": ["property", "list"]}}, "response": []}, {"name": "SEARCH", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "r proepty", "type": "text", "disabled": true}, {"key": "address", "value": "banani", "type": "text", "disabled": true}, {"key": "description", "value": "sgdfhfdhfd", "type": "text", "disabled": true}, {"key": "user_id", "value": "4", "type": "text", "disabled": true}, {"key": "default_image", "value": "1", "type": "text", "disabled": true}, {"key": "property_type_id", "value": "1", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}api/property/list?search=Ocean", "host": ["{{url}}api"], "path": ["property", "list"], "query": [{"key": "search", "value": "Ocean"}]}}, "response": [{"name": "SEARCH", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "r proepty", "type": "text", "disabled": true}, {"key": "address", "value": "banani", "type": "text", "disabled": true}, {"key": "description", "value": "sgdfhfdhfd", "type": "text", "disabled": true}, {"key": "user_id", "value": "4", "type": "text", "disabled": true}, {"key": "default_image", "value": "1", "type": "text", "disabled": true}, {"key": "property_type_id", "value": "1", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}api/property/list", "host": ["{{url}}api"], "path": ["property", "list"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.23.3"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "X-Powered-By", "value": "PHP/8.2.3"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Wed, 22 Feb 2023 14:33:44 GMT"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"properties\": [\n        {\n            \"id\": 1,\n            \"name\": \"Central Park View - 11\",\n            \"vacant\": \"1\",\n            \"occupied\": 6\n        },\n        {\n            \"id\": 2,\n            \"name\": \"Ocean View Condo - 12\",\n            \"vacant\": \"1\",\n            \"occupied\": 6\n        },\n        {\n            \"id\": 3,\n            \"name\": \"City Center Apartment - 13\",\n            \"vacant\": \"1\",\n            \"occupied\": 6\n        }\n    ],\n    \"total_properties\": 3,\n    \"vacant\": 3,\n    \"total_occupied\": 0\n}"}]}, {"name": "Property Details List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/property/1/details-list", "host": ["{{url}}api"], "path": ["property", "1", "details-list"]}}, "response": []}, {"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/property/create", "host": ["{{url}}api"], "path": ["property", "create"]}}, "response": []}, {"name": "Property/{id}/details/{type}", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "r proepty", "type": "text"}, {"key": "address", "value": "banani", "type": "text"}, {"key": "description", "value": "sgdfhfdhfd", "type": "text"}, {"key": "user_id", "value": "4", "type": "text", "disabled": true}, {"key": "default_image", "value": "1", "type": "text", "disabled": true}, {"key": "property_type_id", "value": "1", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}api/property/1/details/basic", "host": ["{{url}}api"], "path": ["property", "1", "details", "basic"]}}, "response": []}, {"name": "Edit", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}api/property/edit/1", "host": ["{{url}}api"], "path": ["property", "edit", "1"]}}, "response": []}, {"name": "update", "request": {"method": "POST", "header": [], "url": {"raw": "{{url}}api/property/update", "host": ["{{url}}api"], "path": ["property", "update"]}}, "response": []}, {"name": "Store", "request": {"method": "POST", "header": [], "url": {"raw": "{{url}}api/property/store", "host": ["{{url}}api"], "path": ["property", "store"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "4|JDckEW4xlRirk1oD6VZg4viXOGKf4q19S3ve7dIf", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Tenant", "item": [{"name": "Tenant List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/tenant", "host": ["{{url}}api"], "path": ["tenant"]}}, "response": []}, {"name": "Tenant List Search", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/tenant?search=title", "host": ["{{url}}api"], "path": ["tenant"], "query": [{"key": "search", "value": "title"}]}}, "response": []}, {"name": "Tenant Details (All tab list)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/tenant/info-list", "host": ["{{url}}api"], "path": ["tenant", "info-list"]}}, "response": []}, {"name": "Create", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}api/tenant/create", "host": ["{{url}}api"], "path": ["tenant", "create"]}}, "response": []}, {"name": "Edit", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}api/tenant/edit/1", "host": ["{{url}}api"], "path": ["tenant", "edit", "1"]}}, "response": []}, {"name": "update", "request": {"method": "POST", "header": [], "url": {"raw": "{{url}}api/tenant/update", "host": ["{{url}}api"], "path": ["tenant", "update"]}}, "response": []}, {"name": "Store", "request": {"method": "POST", "header": [], "url": {"raw": "{{url}}api/tenant/store", "host": ["{{url}}api"], "path": ["tenant", "store"]}}, "response": []}, {"name": "Tenant Details List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/tenant/1/details-list", "host": ["{{url}}api"], "path": ["tenant", "1", "details-list"]}}, "response": []}]}, {"name": "Transaction", "item": [{"name": "Transaction List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/transaction", "host": ["{{url}}api"], "path": ["transaction"]}}, "response": [{"name": "Transaction List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{url}}api/transaction", "host": ["{{url}}api"], "path": ["transaction"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.23.3"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "X-Powered-By", "value": "PHP/8.2.3"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Wed, 22 Feb 2023 14:21:58 GMT"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "57"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"result\": true,\n    \"api_end_point\": \"http://landlord.test/api/transaction\",\n    \"message\": \"Transaction\",\n    \"data\": {\n        \"messages\": \"Transaction\",\n        \"items\": {\n            \"list\": [\n                {\n                    \"id\": 1,\n                    \"property\": \"City Center Apartment - 13\",\n                    \"app_date\": \"22 Feb, 2023\",\n                    \"attachment\": \"http://landlord.test/frontend/img/favicon.png\",\n                    \"tenant\": {\n                        \"id\": null,\n                        \"name\": null,\n                        \"email\": null,\n                        \"phone\": null\n                    },\n                    \"rental_agreement\": {\n                        \"id\": 1,\n                        \"amount\": 10000,\n                        \"start_date\": \"2023-03-01\",\n                        \"end_date\": \"2023-06-30\",\n                        \"note\": \"advance paid\"\n                    },\n                    \"amount\": 1000,\n                    \"type\": \"income\",\n                    \"date\": \"2023-02-22\",\n                    \"note\": \"This is a note\",\n                    \"payment_details\": {\n                        \"payment_method\": \"cash\",\n                        \"cheque_number\": \"123456\",\n                        \"bank_name\": \"Bank Name\",\n                        \"bank_branch\": \"Bank Branch\",\n                        \"bank_account_number\": \"*********\",\n                        \"bank_account_name\": \"Bank Account Name\",\n                        \"online_payment_method\": null,\n                        \"online_payment_transaction_id\": null,\n                        \"online_payment_transaction_status\": null,\n                        \"payment_status\": \"unpaid\"\n                    },\n                    \"invoice\": {\n                        \"invoice_number\": \"CfxYXZKJOH\",\n                        \"invoice_date\": \"2023-02-22\",\n                        \"app_invoice_date\": \"22 Feb, 2023\"\n                    }\n                },\n                {\n                    \"id\": 2,\n                    \"property\": \"City Center Apartment - 13\",\n                    \"app_date\": \"22 Feb, 2023\",\n                    \"attachment\": \"http://landlord.test/frontend/img/favicon.png\",\n                    \"tenant\": {\n                        \"id\": 1,\n                        \"name\": \"Tenant 5\",\n                        \"email\": \"<EMAIL>\",\n                        \"phone\": \"018110000094\"\n                    },\n                    \"rental_agreement\": {\n                        \"id\": 1,\n                        \"amount\": 10000,\n                        \"start_date\": \"2023-03-01\",\n                        \"end_date\": \"2023-06-30\",\n                        \"note\": \"advance paid\"\n                    },\n                    \"amount\": 1000,\n                    \"type\": \"expense\",\n                    \"date\": \"2023-02-22\",\n                    \"note\": \"This is a note\",\n                    \"payment_details\": {\n                        \"payment_method\": \"cash\",\n                        \"cheque_number\": \"123456\",\n                        \"bank_name\": \"Bank Name\",\n                        \"bank_branch\": \"Bank Branch\",\n                        \"bank_account_number\": \"*********\",\n                        \"bank_account_name\": \"Bank Account Name\",\n                        \"online_payment_method\": null,\n                        \"online_payment_transaction_id\": null,\n                        \"online_payment_transaction_status\": null,\n                        \"payment_status\": \"unpaid\"\n                    },\n                    \"invoice\": {\n                        \"invoice_number\": \"amNQNR6TD6\",\n                        \"invoice_date\": \"2023-02-22\",\n                        \"app_invoice_date\": \"22 Feb, 2023\"\n                    }\n                },\n                {\n                    \"id\": 3,\n                    \"property\": \"Ocean View Condo - 12\",\n                    \"app_date\": \"22 Feb, 2023\",\n                    \"attachment\": \"http://landlord.test/frontend/img/favicon.png\",\n                    \"tenant\": {\n                        \"id\": null,\n                        \"name\": null,\n                        \"email\": null,\n                        \"phone\": null\n                    },\n                    \"rental_agreement\": {\n                        \"id\": 2,\n                        \"amount\": 11000,\n                        \"start_date\": \"2023-04-01\",\n                        \"end_date\": \"2023-07-31\",\n                        \"note\": \"pre paid\"\n                    },\n                    \"amount\": 1000,\n                    \"type\": \"deposit\",\n                    \"date\": \"2023-02-22\",\n                    \"note\": \"This is a note\",\n                    \"payment_details\": {\n                        \"payment_method\": \"cash\",\n                        \"cheque_number\": \"123456\",\n                        \"bank_name\": \"Bank Name\",\n                        \"bank_branch\": \"Bank Branch\",\n                        \"bank_account_number\": \"*********\",\n                        \"bank_account_name\": \"Bank Account Name\",\n                        \"online_payment_method\": null,\n                        \"online_payment_transaction_id\": null,\n                        \"online_payment_transaction_status\": null,\n                        \"payment_status\": \"unpaid\"\n                    },\n                    \"invoice\": {\n                        \"invoice_number\": \"lOlCZz28jH\",\n                        \"invoice_date\": \"2023-02-22\",\n                        \"app_invoice_date\": \"22 Feb, 2023\"\n                    }\n                }\n            ],\n            \"links\": {\n                \"first\": \"http://landlord.test/api/transaction?page=1\",\n                \"last\": \"http://landlord.test/api/transaction?page=1\",\n                \"prev\": null,\n                \"next\": null\n            },\n            \"pagination\": {\n                \"total\": 3,\n                \"count\": 3,\n                \"per_page\": 10,\n                \"current_page\": 1,\n                \"total_pages\": 1\n            }\n        }\n    }\n}"}]}, {"name": "SEARCH", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/transaction?search=City Center Apartment", "host": ["{{url}}api"], "path": ["transaction"], "query": [{"key": "search", "value": "City Center Apartment"}]}}, "response": [{"name": "SEARCH", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{url}}api/transaction?search=City Center Apartment", "host": ["{{url}}api"], "path": ["transaction"], "query": [{"key": "search", "value": "City Center Apartment"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.23.3"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "X-Powered-By", "value": "PHP/8.2.3"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Wed, 22 Feb 2023 14:27:39 GMT"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"result\": true,\n    \"api_end_point\": \"http://landlord.test/api/transaction\",\n    \"message\": \"Transaction\",\n    \"data\": {\n        \"messages\": \"Transaction\",\n        \"items\": {\n            \"list\": [\n                {\n                    \"id\": 1,\n                    \"property\": \"City Center Apartment - 13\",\n                    \"app_date\": \"22 Feb, 2023\",\n                    \"attachment\": \"http://landlord.test/frontend/img/favicon.png\",\n                    \"tenant\": {\n                        \"id\": null,\n                        \"name\": null,\n                        \"email\": null,\n                        \"phone\": null\n                    },\n                    \"rental_agreement\": {\n                        \"id\": 1,\n                        \"amount\": 10000,\n                        \"start_date\": \"2023-03-01\",\n                        \"end_date\": \"2023-06-30\",\n                        \"note\": \"advance paid\"\n                    },\n                    \"amount\": 1000,\n                    \"type\": \"income\",\n                    \"date\": \"2023-02-22\",\n                    \"note\": \"This is a note\",\n                    \"payment_details\": {\n                        \"payment_method\": \"cash\",\n                        \"cheque_number\": \"123456\",\n                        \"bank_name\": \"Bank Name\",\n                        \"bank_branch\": \"Bank Branch\",\n                        \"bank_account_number\": \"*********\",\n                        \"bank_account_name\": \"Bank Account Name\",\n                        \"online_payment_method\": null,\n                        \"online_payment_transaction_id\": null,\n                        \"online_payment_transaction_status\": null,\n                        \"payment_status\": \"unpaid\"\n                    },\n                    \"invoice\": {\n                        \"invoice_number\": \"CfxYXZKJOH\",\n                        \"invoice_date\": \"2023-02-22\",\n                        \"app_invoice_date\": \"22 Feb, 2023\"\n                    }\n                },\n                {\n                    \"id\": 2,\n                    \"property\": \"City Center Apartment - 13\",\n                    \"app_date\": \"22 Feb, 2023\",\n                    \"attachment\": \"http://landlord.test/frontend/img/favicon.png\",\n                    \"tenant\": {\n                        \"id\": 1,\n                        \"name\": \"Tenant 5\",\n                        \"email\": \"<EMAIL>\",\n                        \"phone\": \"018110000094\"\n                    },\n                    \"rental_agreement\": {\n                        \"id\": 1,\n                        \"amount\": 10000,\n                        \"start_date\": \"2023-03-01\",\n                        \"end_date\": \"2023-06-30\",\n                        \"note\": \"advance paid\"\n                    },\n                    \"amount\": 1000,\n                    \"type\": \"expense\",\n                    \"date\": \"2023-02-22\",\n                    \"note\": \"This is a note\",\n                    \"payment_details\": {\n                        \"payment_method\": \"cash\",\n                        \"cheque_number\": \"123456\",\n                        \"bank_name\": \"Bank Name\",\n                        \"bank_branch\": \"Bank Branch\",\n                        \"bank_account_number\": \"*********\",\n                        \"bank_account_name\": \"Bank Account Name\",\n                        \"online_payment_method\": null,\n                        \"online_payment_transaction_id\": null,\n                        \"online_payment_transaction_status\": null,\n                        \"payment_status\": \"unpaid\"\n                    },\n                    \"invoice\": {\n                        \"invoice_number\": \"amNQNR6TD6\",\n                        \"invoice_date\": \"2023-02-22\",\n                        \"app_invoice_date\": \"22 Feb, 2023\"\n                    }\n                }\n            ],\n            \"links\": {\n                \"first\": \"http://landlord.test/api/transaction?page=1\",\n                \"last\": \"http://landlord.test/api/transaction?page=1\",\n                \"prev\": null,\n                \"next\": null\n            },\n            \"pagination\": {\n                \"total\": 2,\n                \"count\": 2,\n                \"per_page\": 10,\n                \"current_page\": 1,\n                \"total_pages\": 1\n            }\n        }\n    }\n}"}]}, {"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/transaction/create", "host": ["{{url}}api"], "path": ["transaction", "create"]}}, "response": [{"name": "Create", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{url}}api/transaction/create", "host": ["{{url}}api"], "path": ["transaction", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.23.3"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "X-Powered-By", "value": "PHP/8.2.3"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Wed, 22 Feb 2023 13:03:19 GMT"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"result\": true,\n    \"api_end_point\": \"http://landlord.test/api/transaction/create\",\n    \"message\": \"Transaction Create\",\n    \"data\": {\n        \"messages\": \"Transaction Create\",\n        \"properties\": [\n            {\n                \"id\": 1,\n                \"name\": \"Central Park View - 11\"\n            },\n            {\n                \"id\": 2,\n                \"name\": \"Ocean View Condo - 12\"\n            },\n            {\n                \"id\": 3,\n                \"name\": \"City Center Apartment - 13\"\n            }\n        ],\n        \"categories\": {\n            \"income\": [\n                {\n                    \"id\": 1,\n                    \"title\": \"Full-time\"\n                },\n                {\n                    \"id\": 2,\n                    \"title\": \"Part-time\"\n                },\n                {\n                    \"id\": 3,\n                    \"title\": \"Writing\"\n                },\n                {\n                    \"id\": 4,\n                    \"title\": \"Graphic design\"\n                },\n                {\n                    \"id\": 5,\n                    \"title\": \"Stocks\"\n                },\n                {\n                    \"id\": 6,\n                    \"title\": \"Real estate\"\n                }\n            ],\n            \"expense\": [\n                {\n                    \"id\": 7,\n                    \"title\": \"Food\"\n                },\n                {\n                    \"id\": 8,\n                    \"title\": \"Groceries\"\n                },\n                {\n                    \"id\": 9,\n                    \"title\": \"Eating Out\"\n                },\n                {\n                    \"id\": 10,\n                    \"title\": \"Transportation\"\n                },\n                {\n                    \"id\": 11,\n                    \"title\": \"Gas\"\n                },\n                {\n                    \"id\": 12,\n                    \"title\": \"Public Transportation\"\n                },\n                {\n                    \"id\": 13,\n                    \"title\": \"Car Maintenance\"\n                },\n                {\n                    \"id\": 14,\n                    \"title\": \"Housing\"\n                },\n                {\n                    \"id\": 15,\n                    \"title\": \"Rent\"\n                },\n                {\n                    \"id\": 16,\n                    \"title\": \"Mortgage\"\n                },\n                {\n                    \"id\": 17,\n                    \"title\": \"Utilities\"\n                },\n                {\n                    \"id\": 18,\n                    \"title\": \"Entertainment\"\n                },\n                {\n                    \"id\": 19,\n                    \"title\": \"Movies\"\n                },\n                {\n                    \"id\": 20,\n                    \"title\": \"Concerts\"\n                },\n                {\n                    \"id\": 21,\n                    \"title\": \"Sports\"\n                },\n                {\n                    \"id\": 22,\n                    \"title\": \"Personal Care\"\n                },\n                {\n                    \"id\": 23,\n                    \"title\": \"Haircut\"\n                },\n                {\n                    \"id\": 24,\n                    \"title\": \"Toiletries\"\n                }\n            ]\n        }\n    }\n}"}]}, {"name": "Create", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}api/transaction/create", "host": ["{{url}}api"], "path": ["transaction", "create"]}}, "response": []}, {"name": "Edit", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}api/transaction/edit/1", "host": ["{{url}}api"], "path": ["transaction", "edit", "1"]}}, "response": []}, {"name": "update", "request": {"method": "POST", "header": [], "url": {"raw": "{{url}}api/transaction/update", "host": ["{{url}}api"], "path": ["transaction", "update"]}}, "response": []}, {"name": "Store", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "type", "value": " income", "type": "text"}, {"key": "date", "value": " 2023-02-12", "type": "text"}, {"key": "amount", "value": " 20000", "type": "text"}, {"key": "property_id", "value": " 1", "type": "text"}, {"key": "attachment_id", "type": "file", "src": "/home/<USER>/Desktop/8.jpg"}, {"key": "", "type": "file", "src": [], "disabled": true}, {"key": "category_id", "value": "1", "type": "text"}, {"key": "user_id", "value": "5", "type": "text"}]}, "url": {"raw": "{{url}}api/transaction/store", "host": ["{{url}}api"], "path": ["transaction", "store"]}}, "response": []}]}, {"name": "Dashboard", "item": [{"name": "User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/dashboard", "host": ["{{url}}api"], "path": ["dashboard"]}}, "response": []}]}, {"name": "Profile", "item": [{"name": "profile update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n   \"name\": \"land Update vvvg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/profile-update", "host": ["{{url}}api"], "path": ["profile-update"]}}, "response": []}, {"name": "Change Password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n   \"password\": \"12345678\",\n   \"new_password\": \"1234567i\",\n   \"confirm_password\": \"12345j67\"\n\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/change-password", "host": ["{{url}}api"], "path": ["change-password"]}}, "response": []}]}, {"name": "Notification", "item": [{"name": "Notification List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/notifications", "host": ["{{url}}api"], "path": ["notifications"]}}, "response": []}]}, {"name": "Cash Management", "item": [{"name": "Cash List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "2|HJK5uyo4sRvnz9Y2DLjT3hP1l6YzIti582rAPfBT", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}api/cash-management/list", "host": ["{{url}}api"], "path": ["cash-management", "list"]}}, "response": []}]}, {"name": "Category", "item": [{"name": "Expense Income List", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}api/categories/", "host": ["{{url}}api"], "path": ["categories", ""]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "token", "value": "1|fJ9hM8lUhEXufMvjKvmEwWb25AKMipk0aJS3fcrg", "type": "string"}, {"key": "url", "value": "http://landlord.test/", "type": "string", "disabled": true}, {"key": "url", "value": "https://landlord.onestweb.com/", "type": "string"}, {"key": "localpc", "value": "http://land-lord-saas.test/", "type": "string", "disabled": true}]}