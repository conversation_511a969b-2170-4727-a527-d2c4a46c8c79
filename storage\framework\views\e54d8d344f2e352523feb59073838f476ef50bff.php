<?php $__env->startSection('title'); ?>
<?php echo e(@$data['title']); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="page-content">

    
    <div class="page-header">
        <div class="row">
            <div class="col-sm-6">
                <h4 class="bradecrumb-title mb-1"><?php echo e($data['title']); ?></h1>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(___('common.home')); ?></a></li>
                    <li class="breadcrumb-item"><?php echo e($data['title']); ?></li>
                </ol>
            </div>
        </div>
    </div>
    

    <!--  table content start -->
    <div class="table-content table-basic mt-20">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><?php echo e(___('common.Property')); ?></h4>
                <?php if(hasPermission('property_create')): ?>
                <a href="<?php echo e(route('properties.create')); ?>" class="btn btn-lg ot-btn-primary">
                    <span><i class="fa-solid fa-plus"></i> </span>
                    <span class=""><?php echo e(___('common.Add Property')); ?></span>
                </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered user-table">
                        <thead class="thead">
                            <tr>
                                <th class="serial"><?php echo e(___('common.sr_no.')); ?></th>
                                <th class="purchase"><?php echo e(___('common.name')); ?></th>
                                <th class="purchase"><?php echo e(___('common.Info')); ?></th>
                                <th class="purchase"><?php echo e(___('common.Particulars')); ?></th>
                                <th class="purchase"><?php echo e(___('common.Address')); ?></th>
                                <th class="purchase"><?php echo e(___('common.Type')); ?></th>
                                <th class="purchase"><?php echo e(___('common.status')); ?></th>
                                <?php if(hasPermission('user_update') || hasPermission('user_delete')): ?>
                                <th class="action"><?php echo e(___('common.action')); ?></th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody class="tbody">
                            <?php $__empty_1 = true; $__currentLoopData = $data['property']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr id="row_<?php echo e($row->id); ?>">
                                <td class="serial"><?php echo e(++$key); ?></td>
                                <td>
                                    <div class="">
                                        <a href="<?php echo e(route('admin.properties.details',[ $row->id, 'basicInfo'])); ?>">
                                            <div class="user-card">
                                                <div class="">
                                                    <img src="<?php echo e(@globalAsset($row->defaultImage->path)); ?>"
                                                        alt="<?php echo e($row->name); ?>">
                                                </div>
                                                <div class="user-info">
                                                    <span class="tb-lead p-2"><?php echo e($row->name); ?> </span>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <strong><?php echo e($row->type==1?'Commercial':'Residential'); ?></strong><br>
                                    <strong><?php echo e(___('common.Status')); ?>:</strong> <?php echo e($row->completion==1?'Completed':'Under Construction'); ?><br>
                                    <strong><?php echo e(___('common.Unit')); ?>:</strong> <?php echo e($row->total_unit); ?><br>
                                    <strong><?php echo e(___('common.Occupied')); ?>:</strong> <?php echo e($row->total_occupied); ?><br>
                                    <strong><?php echo e(___('common.Rent')); ?>:</strong> <?php echo e($row->total_rent); ?><br>
                                    <strong><?php echo e(___('common.Sell')); ?>:</strong> <?php echo e($row->total_sell); ?>

                                </td>
                                <td>
                                    <strong><?php echo e(___('common.Size')); ?>:</strong> <?php echo e($row->size); ?> <?php echo e(___('common.sqft')); ?><br>
                                    <strong><?php echo e(___('common.Dining Combined')); ?>:</strong> <?php echo e($row->dining_combined==1?'Yes':'No'); ?><br>
                                    <strong><?php echo e(___('common.Bedroom')); ?>:</strong> <?php echo e($row->bedroom); ?><br>
                                    <strong><?php echo e(___('common.Bathroom')); ?>:</strong> <?php echo e($row->bathroom); ?><br>
                                    <strong><?php echo e(___('common.Rent Amount')); ?>:</strong> <?php echo e($row->rent_amount); ?><br>
                                    <?php if($row->flat_no): ?>
                                    <strong><?php echo e(___('common.Flat No.')); ?>:</strong> <?php echo e($row->flat_no); ?>

                                    <?php endif; ?>
                                </td>
                                
                                <td>
                                    <strong><?php echo e(___('common.Address')); ?>:</strong> <?php echo e(@$row->location->address); ?><br>
                                    <strong><?php echo e(___('common.State')); ?>:</strong> <?php echo e(@$row->location->state->name); ?><br>
                                    <strong><?php echo e(___('common.City')); ?>:</strong> <?php echo e(@$row->location->city->name); ?><br>
                                    <strong><?php echo e(___('common.Post Code')); ?>:</strong> <?php echo e(@$row->location->post_code); ?><br>
                                    <strong><?php echo e(___('common.Country')); ?>:</strong> <?php echo e(@$row->location->country->name); ?>

                                </td>
                                <td>
                                    <div>
                                        <span class="badge badge-pill <?php echo e($row->deal_type == 1 ? 'badge-basic-primary-text':'badge-basic-warning-text'); ?>"><?php echo e($row->deal_type == 1 ? 'Rent':'Sell'); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <?php if($row->status == App\Enums\Status::ACTIVE): ?>
                                    <span class="badge-basic-success-text"><?php echo e(___('common.active')); ?></span>
                                    <?php else: ?>
                                    <span class="badge-basic-danger-text"><?php echo e(___('common.inactive')); ?></span>
                                    <?php endif; ?>
                                </td>
                                <?php if(hasPermission('user_update') || hasPermission('user_delete')): ?>
                                <td class="action">
                                    <div class="dropdown dropdown-action">
                                        <button type="button" class="btn-dropdown" data-bs-toggle="dropdown"
                                            aria-expanded="false">
                                            <i class="fa-solid fa-ellipsis"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            
                                            <li>
                                                <a class="dropdown-item" href="<?php echo e(route('admin.properties.details',[ $row->id, 'basicInfo'])); ?>">
                                                    <span class="icon mr-8"><i
                                                            class="fa-solid fa-eye"></i></span>
                                                    <span><?php echo e(___('common.view')); ?></span>
                                                </a>
                                            </li>
                                            
                                            
                                            <?php if(hasPermission('user_delete')): ?>
                                            <li>
                                                <a class="dropdown-item" href="javascript:void(0);"
                                                    onclick="delete_row('users/delete', <?php echo e($row->id); ?>)">
                                                    <span class="icon mr-12"><i
                                                            class="fa-solid fa-trash-can"></i></span>
                                                    <span><?php echo e(___('common.delete')); ?></span>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>

                                </td>
                                <?php endif; ?>
                            </tr>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="100%" class="text-center gray-color">
                                    <img src="<?php echo e(asset('images/no_data.svg')); ?>" alt="" class="mb-primary" width="100">
                                    <p class="mb-0 text-center">No data available</p>
                                    <p class="mb-0 text-center text-secondary font-size-90">
                                        Please add new entity regarding this table</p>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <!--  table end -->
                <!--  pagination start -->
                    <div class="ot-pagination pagination-content d-flex justify-content-end align-content-center py-3">
                        <nav aria-label="Page navigation example">
                            <ul class="pagination justify-content-between">
                                <?php echo $data['property']->links(); ?>

                            </ul>
                        </nav>
                    </div>
                <!--  pagination end -->
            </div>
        </div>
    </div>
    <!--  table content end -->
</div>
<?php $__env->stopSection(); ?>


<?php $__env->startPush('script'); ?>
<?php echo $__env->make('backend.partials.delete-ajax', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\landlord\resources\views/backend/property/index.blade.php ENDPATH**/ ?>