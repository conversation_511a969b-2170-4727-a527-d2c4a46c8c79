<?php

namespace Database\Seeders;

use App\Models\City;
use Faker\Factory;
use App\Models\User;
use App\Models\Image;
use App\Models\Document;
use App\Models\Locations\Country;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use App\Models\Locations\Upazila;
use App\Models\Property\Property;
use App\Models\Locations\District;
use App\Models\Locations\Division;
use Illuminate\Support\Facades\Auth;
use App\Models\Property\PropertyTenant;
use App\Models\Property\PropertyGallery;
use App\Models\Property\PropertyCategory;
use App\Models\Property\PropertyLocation;
use App\Models\State;

class PropertySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $state = State::where('id', 1399)->first();
        $city = City::where('state_id', $state->id)->first();
        $country = Country::where('id',233)->first();

        $properties = [
            "Harmony Place",
            "Green View Terrace",
            "Golden Sands",
            "Bay view Heights",
            "Willow Grove",
            "Saddleback Ridge",
            "Creek side Villas",
            "Cherry wood Estates",
            "River view Gardens",
            "Sunny dale Heights",
            "Maple wood Ridge",
            "Autumn Ridge",
            "Stone brook Place",
            "Forest Glen",
            "Pine crest Heights",
            "West wood Village",
            "Springfield Gardens",
            "Pleasant Valley",
            "Briarwood Court",
            "Meadow brook Ridge",
            "Cedar Creek",
            "Lake view Heights",
            "Woodland Hills",
            "Birch wood Place",
            "Aspen Ridge",
            "Riverside Meadows",
            "Meadowlark Place",
            "Windsor Gardens",
            "Fair view Heights",
            "Oak wood Terrace",
        ];

        $user = User::where('role_id', 4)->inRandomOrder()->first();
        $Faker = \Faker\Factory::create();
        $categories = PropertyCategory::all();

        foreach ($categories as $category) {
            for ($i = 1; $i <= 5; $i++) {
                for ($j = 1; $j <= 6; $j++) {
                    $image = Image::create([
                        'path' => 'assets/property/' . $category->id . '/' . $i . '/' . $j . '.webp',
                    ]);
                    $uploaded_file_ids[$category->id][$i][$j] = $image->id;
                }

                $propertyName = $properties[array_rand($properties)];
                $property = Property::create([
                    'name' => $propertyName,
                    'slug' => Str::slug($propertyName) . '-' . Str::random(5),
                    'size' => 500 + rand(0, 1000),
                    'dining_combined' => 'yes',
                    'bedroom' => 1 + rand() % 3,
                    'bathroom' => 1 + rand() % 3,
                    'rent_amount' => 20000 + rand() % 100000,
                    'flat_no' => 'A10' . rand() % 9,
                    'description' => $Faker->text(200),
                    'vacant' => 1 + $i % 2,
                    'completion' => 1 + $i % 2,
                    'deal_type' => 1 + $i % 2,
                    'status' => 1,
                    'type' => 1 + $i % 2,
                    'user_id' => $user->id,
                    'default_image' => $uploaded_file_ids[$category->id][$i][1],
                    // 'property_category_id' => $category->id,
                    'property_category_id' => rand(5,6),
                    'total_unit' => 1,
                    'total_occupied' => 0,
                    'total_rent' => 1 + rand() % 3,
                    'total_sell' => 1 + rand() % 3,
                    'discount_type' => 'fixed',
                    'discount_amount' => 0,
                ]);

                $duration['start_date'] = \Carbon\Carbon::parse('2015-01-01');
                $duration['end_date'] = \Carbon\Carbon::parse('2018-12-31');



                // START:: property tenant create
                $user_id = User::where('role_id', 5)->inRandomOrder()->first()->id;
                $_tenant = new PropertyTenant;
                $_tenant->property_id = $property->id;
                $_tenant->user_id = $user_id;
                $_tenant->landowner_id = $property->user_id;
                $_tenant->emergency_contact_id = 1;
                $_tenant->start_date = $duration['start_date'];
                $_tenant->end_date = $duration['end_date'];
                $_tenant->status =1;
                $_tenant->save();
                // END:: property tenant create





                for ($g = 2; $g <= 4; $g++) {
                    $newPropertyGallery = new PropertyGallery();
                    $newPropertyGallery->title = $property->name;
                    $newPropertyGallery->property_id = $property->id;
                    $newPropertyGallery->image_id = $uploaded_file_ids[$category->id][$i][$g];
                    $newPropertyGallery->status = 1;
                    $newPropertyGallery->is_default = 0;
                    $newPropertyGallery->serial = $g - 1;
                    $newPropertyGallery->type = 'gallery';
                    $newPropertyGallery->save();
                }

                // floor_plan
                $newPropertyGallery = new PropertyGallery();
                $newPropertyGallery->title = $property->name;
                $newPropertyGallery->property_id = $property->id;
                $newPropertyGallery->image_id = $uploaded_file_ids[$category->id][$i][5];
                $newPropertyGallery->status = 1;
                $newPropertyGallery->is_default = 0;
                $newPropertyGallery->serial = 1;
                $newPropertyGallery->type = 'floor_plan';
                $newPropertyGallery->save();

                PropertyLocation::create([
                    'property_id' => $property->id,
                    'state_id' => $state->id,
                    'city_id' => $city->id,
                    'country_id' => $country->id,
                    'post_code' => 'ab12012',
                    'user_id' => $user->id,
                    'address' =>  $city->name . ', ' . $state->name . ',' .$country->name,
                ]);

                //document
                $document = new Document();
                $document->attachment_table = 'property';
                $document->attachment_table_id = $property->id;
                $document->attachment_type = 'normal';
                $document->attachment_id = $uploaded_file_ids[$category->id][$i][6];
                $document->user_id = $user->id;
                $document->save();

            }
        }
    }
}
