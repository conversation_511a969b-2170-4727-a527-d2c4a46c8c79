<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PropertyFilteringResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

        // mapping this collection

            return [
                'id' => $this->id,
                'name' => $this->name,
                'slug' => $this->slug,
                'address' => $this->address,
                'bedrooms' => $this->bedroom,
                'bathrooms' => $this->bathroom,
                'size' => $this->size,
                'price' => $this->rent_amount,
                'image' => apiAssetPath($this->defaultImage->path),
                'type' => $this->type,
                'vacant' => $this->vacant == 1 ? 'Vacant' : 'Occupied',
                'details_url' =>  url("property/" . $this->id . "/details" . "/" . $this->slug),  //
                'flat_no' => $this->flat_no,
                'completion' => $this->completion == 1 ? 'Ready' : 'Under Construction',
                'deal_type' => $this->deal_type == 1 ? 'Rent' : 'Sale',
                'category' => $this->category->name,

            ];
    }
}
