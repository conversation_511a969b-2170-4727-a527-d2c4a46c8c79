name: PHP Pipeline

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      max-parallel: 2
      matrix:
        php-versions: ['7.3', '7.4', '8.0']

    name: PHP ${{ matrix.php-versions }}

    steps:
    - uses: actions/checkout@v1

    - name: Setup PHP
      uses: shivammathur/setup-php@master
      with:
        php-version: ${{ matrix.php-versions }}
        coverage: xdebug

    - name: Validate composer.json and composer.lock
      run: composer validate

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-suggest

    - name: Run test suite
      run: composer run-script test-coverage

    - name: Push coverage to Scrutinizer-ci
      if: matrix.php-versions == '7.4'
      run: |
        wget https://scrutinizer-ci.com/ocular.phar
        php ocular.phar code-coverage:upload --format=php-clover coverage.clover
