/*-----------------------------------
  Ck Editor
-----------------------------------*/

window.addEventListener("load", (e)=>{
    ClassicEditor.create( document.querySelector( '#keypoint' ) )
    .then( editor => {
        console.log( editor );
    } )
    .catch( error => {
        console.error( error );
    } );
    ClassicEditor.create( document.querySelector( '#course-feature' ) )
    .then( editor => {
        console.log( editor );
    } )
    .catch( error => {
        console.error( error );
    } );

});

CKEDITOR.replace('keypoint');

