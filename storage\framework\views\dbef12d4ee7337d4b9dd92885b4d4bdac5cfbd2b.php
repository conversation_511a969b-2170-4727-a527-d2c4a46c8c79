<aside class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <a href="<?php echo e(route('dashboard')); ?>">
                <input type="hidden" name="global_light_logo" id="global_light_logo"
                    value="<?php echo e(@globalAsset(setting('light_logo'), '154x38.png')); ?>" />
                <input type="hidden" name="global_dark_logo" id="global_dark_logo"
                    value="<?php echo e(@globalAsset(setting('dark_logo'), '154x38.png')); ?>" />

                <img id="sidebar_full_logo" class="full-logo setting-image"
                    src="<?php echo e(userTheme() == 'default-theme' ? @globalAsset(setting('light_logo'), '154x38.png') : @globalAsset(setting('dark_logo'), '154x38.png')); ?>"
                    alt="" />

                <img class="half-logo" src="<?php echo e(globalAsset(setting('favicon'), '38x38.png')); ?>" alt="" />
            </a>
        </div>

        <button class="half-expand-toggle sidebar-toggle">
            <img src="<?php echo e(asset('backend')); ?>/assets/images/icons/collapse-arrow.svg" alt="" />
        </button>
        <button class="close-toggle sidebar-toggle">
            <img src="<?php echo e(asset('backend')); ?>/assets/images/icons/collapse-arrow.svg" alt="" />
        </button>
    </div>

    <div class="sidebar-menu srollbar">
        <div class="sidebar-menu-section">


            <!-- parent menu list start  -->
            <ul class="sidebar-dropdown-menu parent-menu-list">
                <li class="sidebar-menu-item">
                    <a href="<?php echo e(route('dashboard')); ?>" class="parent-item-content">
                        
                        <i class="las la-tachometer-alt"></i>
                        <span class="on-half-expanded"><?php echo e(_trans('landlord.Dashboard')); ?></span>
                    </a>
                </li>
                <?php if(hasPermission('user_read') || hasPermission('role_read')): ?>
                    <li class="sidebar-menu-item <?php echo e(set_menu(['users*', 'roles*'])); ?>">
                        <a class="parent-item-content has-arrow">
                            
                            <i class="las la-user-tag"></i>
                            <span class="on-half-expanded"><?php echo e(_trans('landlord.Users & Roles')); ?></span>
                        </a>

                        <!-- second layer child menu list start  -->

                        <ul class="child-menu-list">
                            <?php if(hasPermission('role_read')): ?>
                                <li class="sidebar-menu-item <?php echo e(set_menu(['roles*'])); ?>">
                                    <a href="<?php echo e(route('roles.index')); ?>"><?php echo e(_trans('landlord.Roles')); ?></a>
                                </li>
                            <?php endif; ?>
                            <?php if(hasPermission('user_read')): ?>
                                <li class="sidebar-menu-item <?php echo e(set_menu(['users*'])); ?>">
                                    <a href="<?php echo e(route('users.index')); ?>"><?php echo e(_trans('landlord.Users')); ?></a>
                                </li>
                            <?php endif; ?>


                        </ul>
                    </li>
                <?php endif; ?>


                <!-- User Profile Layout Start -->
                <li class="sidebar-menu-item">
                    <a class="parent-item-content has-arrow">
                        <i class="las la-user-circle"></i>
                        <span class="on-half-expanded"><?php echo e(_trans('landlord.Profile')); ?></span>
                    </a>
                    <ul class="child-menu-list">
                        <li class="sidebar-menu-item <?php echo e(set_menu(['my.profile'])); ?>">
                            <a href=" <?php echo e(route('my.profile')); ?>"><?php echo e(_trans('landlord.My Profile')); ?></a>
                        </li>
                        <li class="sidebar-menu-item <?php echo e(set_menu(['my.profile.edit'])); ?>">
                            <a href="<?php echo e(route('my.profile.edit')); ?>"><?php echo e(_trans('landlord.Edit My Profile')); ?></a>
                        </li>
                        <li class="sidebar-menu-item <?php echo e(set_menu(['passwordUpdate'])); ?>">
                            <a href="<?php echo e(route('passwordUpdate')); ?>"><?php echo e(_trans('landlord.Update Password')); ?></a>
                        </li>
                    </ul>
                </li>
                <!-- User Profile Layout End -->

                <!-- second layer child menu list end  -->

                <!-- Language layout start -->
                <?php if(hasPermission('language_read')): ?>
                    <li class="sidebar-menu-item <?php echo e(set_menu(['languages*'])); ?>">
                        <a href="<?php echo e(route('languages.index')); ?>" class="parent-item-content">
                            <i class="las la-language"></i>
                            <span class="on-half-expanded"><?php echo e(_trans('landlord.Language')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <!-- Language layout end -->

                <!-- Category layout start -->
                <?php if(hasPermission('category_read')): ?>
                    <li class="sidebar-menu-item <?php echo e(set_menu(['categories*'])); ?>">
                        <a href="<?php echo e(route('categories.index')); ?>" class="parent-item-content">
                            <i class="las la-tags"></i>
                            <span class="on-half-expanded"><?php echo e(_trans('landlord.Category')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <!-- Category layout end -->

                <!-- Category layout start -->
                <?php if(hasPermission('property_read')): ?>
                    <li class="sidebar-menu-item">
                        <a class="parent-item-content has-arrow">
                            <i class="las la-city"></i>
                            <span class="on-half-expanded"><?php echo e(___('common.Property')); ?></span>
                        </a>
                        <ul class="child-menu-list">
                            <li class="sidebar-menu-item <?php echo e(set_menu(['properties*'])); ?>">
                                <a href=" <?php echo e(route('properties.index')); ?>"><?php echo e(_trans('landlord.Property')); ?></a>
                            </li>
                            <?php if(hasPermission('property_category_read')): ?>
                                <li class="sidebar-menu-item <?php echo e(set_menu(['property/categories'])); ?>">
                                    <a
                                        href=" <?php echo e(route('properties.categories.index')); ?>"><?php echo e(_trans('landlord.Property Category')); ?></a>
                                </li>
                            <?php endif; ?>
                            <?php if(hasPermission('property_facility_type_read')): ?>
                                <li class="sidebar-menu-item <?php echo e(set_menu(['property/facility-types'])); ?>">
                                    <a
                                        href=" <?php echo e(route('properties.facilityType.index')); ?>"><?php echo e(_trans('landlord.Property Facility Type')); ?></a>
                                </li>
                            <?php endif; ?>

                            <li class="sidebar-menu-item <?php echo e(set_menu(['advertisements*'])); ?>">
                                <a
                                    href=" <?php echo e(route('advertisements.index')); ?>"><?php echo e(_trans('landlord.Advertisements')); ?></a>
                            </li>
                        </ul>
                    </li>
                <?php endif; ?>
                <!-- Category layout end -->

                <!-- Blog Layout Start -->
                <?php if(hasPermission('blogs_read')): ?>
                    <li class="sidebar-menu-item">
                        <a class="parent-item-content has-arrow">
                            <i class="lab la-blogger"></i>
                            <span class="on-half-expanded"><?php echo e(_trans('landlord.Blog')); ?></span>
                        </a>
                        <ul class="child-menu-list">
                            <li
                                class="sidebar-menu-item <?php echo e(set_menu(['blogs.index'])); ?> <?php echo e(set_menu(['blogs.category.create'])); ?> <?php echo e(set_menu(['blogs.edit'])); ?>">
                                <a href=" <?php echo e(route('blogs.index')); ?>"><?php echo e(___('common.blog')); ?></a>
                            </li>
                            <li
                                class="sidebar-menu-item <?php echo e(set_menu(['blogs.category.index'])); ?> <?php echo e(set_menu(['blogs.category.create'])); ?> <?php echo e(set_menu(['blogs.category.edit'])); ?>">
                                <a href="<?php echo e(route('blogs.category.index')); ?>"><?php echo e(_trans('landlord.Category')); ?></a>
                            </li>
                        </ul>
                    </li>
                    <!-- Blog Layout End -->
                <?php endif; ?>

                <!-- Category layout start -->
                <?php if(hasPermission('mail_subscribe_read')): ?>
                    <li class="sidebar-menu-item">
                        <a href="#" class="parent-item-content">
                            <i class="las la-envelope"></i>
                            <span class="on-half-expanded"><?php echo e(_trans('landlord.Mail Subscribe')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <!-- Category layout end -->


                <!-- Category layout start -->

                <!-- Category layout end -->

                <?php if(hasPermission('section_titles_update')): ?>


                    <!-- Home Section layout start -->
                    <li class="sidebar-menu-item">
                        <a class="parent-item-content has-arrow">
                            <i class="las la-user-circle"></i>
                            <span class="on-half-expanded"><?php echo e(_trans('landlord.Home Page')); ?></span>
                        </a>
                        <ul class="child-menu-list">
                            <li class="sidebar-menu-item <?php echo e(set_menu(['home.section-titles.index'])); ?>">
                                <a
                                    href="<?php echo e(route('home.section-titles.index')); ?>"><?php echo e(_trans('landlord.Section Titles')); ?></a>
                            </li>
                            <li class="sidebar-menu-item <?php echo e(set_menu(['hero-sections*'])); ?>">
                                <a
                                    href=" <?php echo e(route('hero-sections.index')); ?>"><?php echo e(_trans('landlord.Hero Section')); ?></a>
                            </li>
                            <li
                                class="sidebar-menu-item <?php echo e(set_menu(['home.partners.index'])); ?><?php echo e(set_menu(['home.partners.create'])); ?><?php echo e(set_menu(['home.partners.edit'])); ?>">
                                <a href="<?php echo e(route('home.partners.index')); ?>"><?php echo e(_trans('landlord.Partners')); ?></a>
                            </li>
                            <li class="sidebar-menu-item <?php echo e(set_menu(['business-models*'])); ?>">
                                <a
                                    href="<?php echo e(route('business-models.index')); ?>"><?php echo e(_trans('landlord.Business Model')); ?></a>
                            </li>
                            <li class="sidebar-menu-item <?php echo e(set_menu(['features*'])); ?>">
                                <a href="<?php echo e(route('features.index')); ?>"><?php echo e(_trans('landlord.Features')); ?></a>
                            </li>
                            <li class="sidebar-menu-item <?php echo e(set_menu(['how-it-works*'])); ?>">
                                <a href="<?php echo e(route('how-it-works.index')); ?>"><?php echo e(_trans('landlord.How It Works')); ?></a>
                            </li>
                            <li class="sidebar-menu-item">
                                <a
                                    href="<?php echo e(route('home.section-titles.index')); ?>#download_app"><?php echo e(_trans('landlord.Download Apps')); ?></a>
                            </li>
                            <li class="sidebar-menu-item <?php echo e(set_menu(['testimonials*'])); ?>">
                                <a href="<?php echo e(route('testimonials.index')); ?>"><?php echo e(_trans('landlord.Testimonial')); ?></a>
                            </li>
                        </ul>
                    </li>
                    <!-- Home Section layout end -->

                    <?php if(hasPermission('contact_read')): ?>
                        <!-- Home Section layout start -->
                        <li class="sidebar-menu-item">
                            <a class="parent-item-content has-arrow">
                                <i class="las la-id-card"></i>
                                <span class="on-half-expanded"><?php echo e(_trans('landlord.contact Us')); ?></span>
                            </a>
                            <ul class="child-menu-list">
                                <li class="sidebar-menu-item <?php echo e(set_menu(['contact.index'])); ?>">
                                    <a href="<?php echo e(route('contact.index')); ?>"><?php echo e(_trans('landlord.Get in Touch')); ?></a>
                                </li>
                            </ul>
                        </li>
                        <!-- Home Section layout end -->
                    <?php endif; ?>

                    <?php if(hasPermission('about_update')): ?>
                        <!-- Home Section layout start -->
                        <li class="sidebar-menu-item">
                            <a class="parent-item-content has-arrow">
                                <i class="las la-id-card"></i>
                                <span class="on-half-expanded"><?php echo e(_trans('landlord.About Us')); ?></span>
                            </a>
                            <ul class="child-menu-list">
                                <li class="sidebar-menu-item <?php echo e(set_menu(['contact.index'])); ?>">
                                    <a
                                        href="<?php echo e(route('about.section-titles.index')); ?>"><?php echo e(_trans('landlord.About')); ?></a>
                                </li>
                            </ul>
                        </li>
                        <!-- Home Section layout end -->
                    <?php endif; ?>

                    <!-- Settings layout start -->
                    <?php if(hasPermission('general_settings_read') ||
                            hasPermission('storage_settings_read') ||
                            hasPermission('recaptcha_settings_read') ||
                            hasPermission('email_settings_read')): ?>
                        <li class="sidebar-menu-item <?php echo e(set_menu(['setting*'])); ?>">
                            <a href="#" class="parent-item-content has-arrow">
                                <i class="las la-cog"></i>
                                <span class="on-half-expanded"> <?php echo e(_trans('landlord.Settings')); ?></span>
                            </a>

                            <!-- second layer child menu list start  -->
                            <ul class="child-menu-list">
                                <?php if(hasPermission('general_settings_read')): ?>
                                    <li class="sidebar-menu-item <?php echo e(set_menu(['settings.general-settings'])); ?>">
                                        <a
                                            href="<?php echo e(route('settings.general-settings')); ?>"><?php echo e(_trans('landlord.General Settings')); ?></a>
                                    </li>
                                <?php endif; ?>

                                <?php if(hasPermission('storage_settings_read')): ?>
                                    <li class="sidebar-menu-item <?php echo e(set_menu(['settings.storagesetting'])); ?>">
                                        <a
                                            href="<?php echo e(route('settings.storagesetting')); ?>"><?php echo e(_trans('landlord.Storage Settings')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <li class="sidebar-menu-item sidebar-menu-item <?php echo e(set_menu(['countries.index'])); ?>">
                                    <a href="<?php echo e(route('countries.index')); ?>">
                                        <span class="on-half-expanded"><?php echo e(_trans('landlord.Country')); ?></span>
                                    </a>
                                </li>

                                <?php if(hasPermission('recaptcha_settings_read')): ?>
                                    <li class="sidebar-menu-item <?php echo e(set_menu(['settings.recaptcha-setting'])); ?>">
                                        <a
                                            href="<?php echo e(route('settings.recaptcha-setting')); ?>"><?php echo e(_trans('landlord.Recaptcha Settings')); ?></a>
                                    </li>
                                <?php endif; ?>

                                <?php if(hasPermission('email_settings_read')): ?>
                                    <li class="sidebar-menu-item <?php echo e(set_menu(['settings.mail-setting'])); ?>">
                                        <a
                                            href="<?php echo e(route('settings.mail-setting')); ?>"><?php echo e(_trans('landlord.Email Settings')); ?></a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                            <!-- second layer child menu list end  -->
                        </li>
                    <?php endif; ?>
                    <!-- Settings layout end -->

                    <!-- Components Layout End -->
            </ul>
            <!-- parent menu list end  -->
            <?php endif; ?>
        </div>

    </div>
</aside>
<?php /**PATH C:\xampp\htdocs\landlord\resources\views/backend/partials/sidebar.blade.php ENDPATH**/ ?>