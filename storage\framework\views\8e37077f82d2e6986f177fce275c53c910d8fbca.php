<?php $__env->startSection('title'); ?>
    <?php echo e(@$data['title']); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="page-content">
        <!-- profile content start -->
        <div class="profile-content">
            <div class="d-flex flex-column flex-lg-row gap-4 gap-lg-0">
                <!-- profile menu mobile start -->
                <div class="profile-menu-mobile">
                    <button class="btn-menu-mobile" type="button" data-bs-toggle="offcanvas"
                        data-bs-target="#offcanvasWithBothOptionsMenuMobile"
                        aria-controls="offcanvasWithBothOptionsMenuMobile">
                        <span class="icon"><i class="fa-solid fa-bars"></i></span>
                    </button>

                    <div class="offcanvas offcanvas-start" data-bs-scroll="true" tabindex="-1"
                        id="offcanvasWithBothOptionsMenuMobile">
                        <div class="offcanvas-header">
                            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close">
                                <span class="icon"><i class="fa-solid fa-xmark"></i></span>
                            </button>
                        </div>
                        <div class="offcanvas-body">
                            <!-- profile menu start -->
                            <div class="profile-menu">
                                <!-- profile menu head start -->
                                <?php echo $__env->make('backend.partials.property-profile-menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <!-- profile menu head end -->

                                <div class="profile-menu-body">
                                    <?php echo $__env->make('backend.property.propert_nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <!-- profile menu end -->
                        </div>
                    </div>
                </div>
                <!-- profile menu mobile end -->

                <!-- profile menu start -->
                <div class="profile-menu">

                    <!-- profile menu head start -->
                    <?php echo $__env->make('backend.partials.property-profile-menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <!-- profile menu head end -->

                    <!-- profile menu body start -->
                    <div class="profile-menu-body">
                        <?php echo $__env->make('backend.property.propert_nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <!-- profile menu body end -->
                </div>
                <!-- profile menu end -->

                <!-- profile body start -->
                <div class="profile-body">

                    <div class="emergency-header-edit mb-16">
                        <h3 class="title m-0">Overview</h3>

                        <a href="#" class="add-edit-btn">
                            <i class="fa-regular fa-pen-to-square"></i>
                        </a>
                    </div>

                    <!-- profile body nav end -->
                    <!-- profile body form start -->
                    <div class="profile-body-form style-2">
                        <div class="form-item border-bottom-0 p-0">

                            
                            <div class="land-basic-data">

                                <div class="ot-card ot_heightFull mb-24">

                                    <div class="browser-details">


                                        <div class="card table-content table-basic mb-5">
                                            <div class="card-body">
                                                <div class="title mb-10">
                                                    <h3 class="">Flat Details</h3>
                                                </div>
                                                <div class="all-lands-basic">
                                                    <div class="table-responsive table-height-350 niceScroll">
                                                        <table class="table table-bordered">
                                                            <tbody class="tbody">
                                                                <tr>
                                                                    <td class="w-50percent"><i
                                                                            class="fa-solid fa-expand"></i> Size</td>
                                                                    <td class="w-50percent"><?php echo e($data['property']->size); ?>

                                                                        <?php echo e(___('common.Square Feet')); ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="w-50percent"><i
                                                                            class="fa-solid fa-mattress-pillow"></i>
                                                                        Beds</td>
                                                                    <td class="w-50percent"><?php echo e($data['property']->bedroom); ?>

                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="w-50percent"><i
                                                                            class="fa-solid fa-shower"></i> Bath</td>
                                                                    <td class="w-50percent">
                                                                        <?php echo e($data['property']->bathroom); ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="w-50percent"><i
                                                                            class="fa-regular fa-credit-card"></i> Rent
                                                                    </td>
                                                                    <td class="w-50percent">
                                                                        <?php echo e($data['property']->rent_amount); ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="w-50percent"><i
                                                                            class="fa-solid fa-sliders"></i> Type</td>
                                                                    <td class="w-50percent">
                                                                        <?php echo e($data['property']->type == 1 ? 'Commercial' : 'Residential'); ?>

                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="w-50percent"><i
                                                                            class="fa-solid fa-sliders"></i> Category</td>
                                                                    <td class="w-50percent">
                                                                        <?php echo e($data['property']->category->name); ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="w-50percent"><i
                                                                            class="fa-regular fa-circle-check"></i>
                                                                        Completion</td>
                                                                    <td class="w-50percent">
                                                                        <?php echo e($data['property']->completion == 1 ? 'Completed' : 'Under Construction'); ?>

                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="w-50percent"><i
                                                                            class="fa-regular fa-clipboard"></i>
                                                                        Description</td>
                                                                    <td class="w-50percent">
                                                                        <?php echo e($data['property']->description); ?></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        
                                        <div class="card table-content table-basic">
                                            <div class="card-body">
                                                <div class="title mb-10">
                                                    <h3 class="">Edit Basic Info</h3>
                                                </div>
                                                <div class="lands-basic-edit">
                                                    <form
                                                        action="<?php echo e(route('properties.update', [$data['property']->id, 'basicInfo'])); ?>"
                                                        enctype="multipart/form-data" method="post" id="visitForm">
                                                        <?php echo csrf_field(); ?>
                                                        <div class="row mb-3">

                                                            <div class="col-md-6 mb-3">
                                                                <label for="exampleDataList"
                                                                    class="form-label "><?php echo e(___('common.Name')); ?></label>
                                                                <input
                                                                    class="form-control ot-input <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="name" list="datalistOptions"
                                                                    id="exampleDataList"
                                                                    placeholder="<?php echo e(___('common.3')); ?>"
                                                                    value="<?php echo e(@$data['property']->name); ?>">
                                                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="exampleDataList"
                                                                    class="form-label "><?php echo e(___('common.Size of Property')); ?>

                                                                    [<?php echo e(___('common.Square Feet')); ?>]</label>
                                                                <input
                                                                    class="form-control ot-input <?php $__errorArgs = ['size_of_property'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="size_of_property" type="number"
                                                                    list="datalistOptions" id="exampleDataList"
                                                                    placeholder="<?php echo e(___('common.234')); ?>"
                                                                    value="<?php echo e(@$data['property']->size); ?>">
                                                                <?php $__errorArgs = ['size_of_property'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="exampleDataList"
                                                                    class="form-label "><?php echo e(___('common.Bedroom')); ?>

                                                                </label>
                                                                <input
                                                                    class="form-control ot-input <?php $__errorArgs = ['bedroom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="bedroom" list="datalistOptions"
                                                                    id="exampleDataList"
                                                                    placeholder="<?php echo e(___('common.3')); ?>"
                                                                    value="<?php echo e(@$data['property']->bedroom); ?>">
                                                                <?php $__errorArgs = ['bedroom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="exampleDataList"
                                                                    class="form-label "><?php echo e(___('common.Bathroom')); ?></label>
                                                                <input
                                                                    class="form-control ot-input <?php $__errorArgs = ['bathroom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="bathroom" list="datalistOptions"
                                                                    id="exampleDataList"
                                                                    placeholder="<?php echo e(___('common.3')); ?>"
                                                                    value="<?php echo e(@$data['property']->bathroom); ?>">
                                                                <?php $__errorArgs = ['bathroom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>


                                                            <div class="col-md-6 mb-3">
                                                                <label for="exampleDataList"
                                                                    class="form-label "><?php echo e(___('common.Rent Price')); ?></label>
                                                                <input
                                                                    class="form-control ot-input <?php $__errorArgs = ['rent_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="rent_price" list="datalistOptions"
                                                                    id="exampleDataList"
                                                                    placeholder="<?php echo e(___('common.40,000')); ?>"
                                                                    value="<?php echo e(@$data['property']->rent_amount); ?>">
                                                                <?php $__errorArgs = ['rent_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="exampleDataList"
                                                                    class="form-label "><?php echo e(___('common.Flat Number')); ?></label>
                                                                <input
                                                                    class="form-control ot-input <?php $__errorArgs = ['Flat Number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="Flat_Number" list="datalistOptions"
                                                                    id="exampleDataList"
                                                                    placeholder="<?php echo e(___('common.3')); ?>"
                                                                    value="<?php echo e(@$data['property']->rent_amount); ?>">
                                                                <?php $__errorArgs = ['Flat Number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="validationServer04"
                                                                    class="form-label"><?php echo e(___('common.Property Type')); ?></label>
                                                                <select
                                                                    class="nice-select niceSelect bordered_style wide <?php $__errorArgs = ['property_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="property_type" id="validationServer04"
                                                                    aria-describedby="validationServer04Feedback">
                                                                    <option value="0" selected>
                                                                        <?php echo e(___('common.Residential')); ?></option>
                                                                    <option value="1"
                                                                        <?php echo e(@$data['property']->type == 1 ? 'selected' : ''); ?>>
                                                                        <?php echo e(___('common.Commercial')); ?></option>
                                                                </select>
                                                                <?php $__errorArgs = ['property_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="validationServer04"
                                                                    class="form-label"><?php echo e(___('common.Property Category')); ?></label>
                                                                <select
                                                                    class="nice-select niceSelect bordered_style wide <?php $__errorArgs = ['property_category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="property_category" id="validationServer04"
                                                                    aria-describedby="validationServer04Feedback">
                                                                    <?php $__currentLoopData = $data['property_categories']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categories): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <option value="<?php echo e($categories->id); ?>"
                                                                            <?php echo e(@$data['property']->property_category_id == $categories->id ? 'selected' : ''); ?>>
                                                                            <?php echo e($categories->name); ?></option>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </select>
                                                                <?php $__errorArgs = ['property_category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3 mt-3">
                                                                <input class="form-check-input mr-4  common-key"
                                                                    type="checkbox" name="drawing_dining_combined"
                                                                    id="drawing_dinning_combined"
                                                                    <?php echo e(@$data['property']->dining_combined == 1 ? 'checked' : ''); ?> />
                                                                <label class="form-check-label"
                                                                    for="drawing_dinning_combined"><?php echo e(___('common.drawing dining combined')); ?><span
                                                                        class="fillable">*</span></label>
                                                            </div>

                                                            <div class="col-md-6 mb-3 mt-3">
                                                                <input class="form-check-input mr-4  common-key"
                                                                    type="checkbox" name="vacant" id="vacant"
                                                                    <?php echo e(@$data['property']->vacant == 1 ? 'checked' : ''); ?> />
                                                                <label class="form-check-label"
                                                                    for="vacant"><?php echo e(___('common.vacant')); ?><span
                                                                        class="fillable">*</span></label>
                                                            </div>

                                                            <div class="col-md-6 mb-3 mt-3">
                                                                
                                                                <label class="form-label"
                                                                    for="inputImage"><?php echo e(___('common.image')); ?></label>
                                                                <div class="ot_fileUploader left-side mb-3">
                                                                    <input class="form-control" type="text"
                                                                        placeholder="<?php echo e(___('common.image')); ?>"
                                                                        readonly="" id="placeholder">
                                                                    <button class="primary-btn-small-input"
                                                                        type="button">
                                                                        <label class="btn btn-lg ot-btn-primary"
                                                                            for="fileBrouse"><?php echo e(___('common.browse')); ?></label>
                                                                        <input type="file" class="d-none form-control"
                                                                            name="image" id="fileBrouse"
                                                                            accept="image/*">
                                                                    </button>
                                                                </div>
                                                            </div>

                                                            <div class="col-md-6 mb-3 mt-3">
                                                                <label for="exampleDataList"
                                                                    class="form-label "><?php echo e(___('common.Address')); ?></label>
                                                                <input
                                                                    class="form-control ot-input <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="address" list="datalistOptions"
                                                                    id="exampleDataList"
                                                                    placeholder="<?php echo e(___('common.Property Address')); ?>"
                                                                    value="<?php echo e(@$data['property']->location->address); ?>">
                                                                <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="validationServer04"
                                                                    class="form-label"><?php echo e(___('common.Country')); ?></label>
                                                                <select
                                                                    class="nice-select niceSelect bordered_style wide <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="country" id="country"
                                                                    aria-describedby="validationServer04Feedback">

                                                                    <?php $__currentLoopData = $data['countries']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <option value="<?php echo e($country->id); ?>"
                                                                            <?php echo e(@$data['property']->location->country_id == $country->id ? 'selected' : ''); ?>>
                                                                            <?php echo e($country->name); ?></option>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </select>
                                                                <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="validationServer04"
                                                                    class="form-label"><?php echo e(___('common.State')); ?></label>
                                                                <select
                                                                    class="nice-select niceSelect bordered_style wide <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="state" id="state_2"
                                                                    aria-describedby="validationServer04Feedback">
                                                                    <?php $__currentLoopData = $data['state']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <option value="<?php echo e($state->id); ?>"
                                                                            <?php echo e(@$data['property']->location->state_id == $state->id ? 'selected' : ''); ?>>
                                                                            <?php echo e($state->name); ?></option>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </select>
                                                                <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="validationServer04"
                                                                    class="form-label"><?php echo e(___('common.City')); ?></label>
                                                                <select
                                                                    class="nice-select niceSelect bordered_style wide <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="city" id="city_1"
                                                                    aria-describedby="validationServer04Feedback">
                                                                    <?php $__currentLoopData = $data['city']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <option value="<?php echo e($city->id); ?>"
                                                                            <?php echo e(@$data['property']->location->city_id == $city->id ? 'selected' : ''); ?>>
                                                                            <?php echo e($city->name); ?></option>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </select>
                                                                <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            

                                                            <div class="col-md-6 mb-3">
                                                                <label for="validationServer04"
                                                                    class="form-label"><?php echo e(___('common.Completion')); ?></label>
                                                                <select
                                                                    class="nice-select niceSelect bordered_style wide <?php $__errorArgs = ['completion'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="completion" id="validationServer04"
                                                                    aria-describedby="validationServer04Feedback">
                                                                    <option value="1"><?php echo e(___('common.Completed')); ?>

                                                                    </option>
                                                                    <option value="0"
                                                                        <?php echo e(@$data['property']->completion == 0 ? 'selected' : ''); ?>>
                                                                        <?php echo e(___('common.Under Construction')); ?>

                                                                    </option>
                                                                </select>
                                                                <?php $__errorArgs = ['completion'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-6 mb-3">
                                                                <label for="validationServer04"
                                                                    class="form-label"><?php echo e(___('common.Status')); ?></label>
                                                                <select
                                                                    class="nice-select niceSelect bordered_style wide <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                                    name="status" id="validationServer04"
                                                                    aria-describedby="validationServer04Feedback">
                                                                    <option value="1"><?php echo e(___('common.Active')); ?>

                                                                    </option>
                                                                    <option value="0"
                                                                        <?php echo e(@$data['property']->status == 0 ? 'selected' : ''); ?>>
                                                                        <?php echo e(___('common.Inactive')); ?>

                                                                    </option>
                                                                </select>
                                                                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-12 mb-3">
                                                                <label for="exampleDataList"
                                                                    class="form-label "><?php echo e(___('common.Description')); ?>

                                                                    <span class="fillable">*</span></label>
                                                                <textarea name="Description" id="Description" placeholder="<?php echo e(___('common.Description')); ?>"
                                                                    class="form-control m-0 <?php $__errorArgs = ['Description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(@$data['property']->description); ?></textarea>
                                                                <?php $__errorArgs = ['Description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                    <div id="validationServer04Feedback"
                                                                        class="invalid-feedback">
                                                                        <?php echo e($message); ?>

                                                                    </div>
                                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                            </div>

                                                            <div class="col-md-12 mt-24">
                                                                <div class="text-center">
                                                                    <button type="submit"
                                                                        class="btn btn-lg ot-btn-primary"><span><i
                                                                                class="fa-solid fa-save"></i>
                                                                        </span><?php echo e(___('common.Save')); ?></button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                            </div>
                            

                        </div>
                    </div>
                    <!-- profile body form end -->
                </div>
                <!-- profile body end -->
            </div>
        </div>
    <?php $__env->stopSection(); ?>

    <?php $__env->startPush('script'); ?>
        <script>
            $(document).ready(function() {
                $("#country").change(function() {
                    var countryId = $(this).val();

                    var baseUrl = $('meta[name="base-url"]').attr("content");

                    $.ajax({
                        url: baseUrl + "/properties/get-states/" + countryId,
                        type: "GET",
                        dataType: "json",
                        success: function(data) {
                            $('#state_2').empty();
                            $.each(data.states, function(key, value) {
                                $('#state_2').append('<option value="' + value
                                    .id + '">' + value.name + '</option>');
                            });
                            $('#state_2').niceSelect('destroy');
                            $('#state_2').niceSelect();


                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error(xhr.responseText);
                        }
                    });
                });

                $('#state_2').on('change', function() {
                    var stateId = $(this).val();

                    $.ajax({
                        url: "/properties/get-cities/" + stateId,
                        type: "GET",
                        dataType: "json",
                        success: function(data) {
                            $('#city_1').empty();
                            $.each(data.cities, function(key, value) {
                                $('#city_1').append('<option value="' + value
                                    .id + '">' + value.name + '</option>');
                            });
                            $('#city_1').niceSelect('destroy');
                            $('#city_1').niceSelect();
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error(xhr.responseText);
                        }
                    });
                });

            });
        </script>
        <?php echo $__env->make('backend.partials.delete-ajax', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\landlord\resources\views/backend/property/basicInfo.blade.php ENDPATH**/ ?>