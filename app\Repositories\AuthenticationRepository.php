<?php

namespace App\Repositories;

use App\Models\Role;
use App\Models\User;
use App\Models\Image;
use App\Mail\ResetPassword;
use Illuminate\Support\Str;
use App\Mail\EmailVerification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Interfaces\AuthenticationRepositoryInterface;

class AuthenticationRepository implements AuthenticationRepositoryInterface
{
    public function login($request)
    {
        $authenticate = Auth::attempt([
            'email' => data_get($request, 'email'),
            'password' => data_get($request, 'password'),
        ], data_get($request, 'rememberMe') ? true : false);

        if ($authenticate) {
            return true;
        }
        return false;
    }

    public function logout()
    {
        Auth::logout();
        Auth::guard('customer')->logout();

        request()->session()->invalidate();

        request()->session()->regenerateToken();
    }

    public function register($request)
    {
        $role = decrypt($request->role);
        switch ($role) {
            case 'Tenant':
                $role_id = 5;
                break;
            case 'Builder':
                $role_id = 4;
                break;
            case 'Owner':
                $role_id = 3;
                break;
            default:
                $role_id = 6;
                break;
        }
        $image =
        Image::create([
            'path' => 'frontend/img/default-image.jpeg'
        ]);
        DB::beginTransaction();
        try {
            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->phone = $request->phone;
            $user->password = Hash::make($request->password);
            $user->token = Str::random(30);
            $user->role_id = $role_id;
            $user->image_id = $image->id;
            $permissions = Role::where('id', $role_id)->pluck('permissions')->first();
            $user->permissions = $permissions;
            $user->save();

            try {
                if (env('EMAIL_VERIFIED')) {
                    $user->email_verified_at = now();
                    $user->save();
                } else {
                    Mail::to($user->email)->send(new EmailVerification($user));
                }
            } catch (\Throwable$th) {
                Log::error($th);
            }

            DB::commit();
            return true;

        } catch (\Throwable$th) {
            dd($th);
            DB::rollBack();
            return false;
        }
    }

    public function verifyEmail($email, $token)
    {
        try {
            $user = User::query()->firstWhere('email', $email);

            if (!$user) {
                return 'invalid_email';
            }

            if ($user->email_verified_at) {
                return 'already_verified';
            }

            if ($user->token != $token) {
                return 'invalid_token';
            }

            $user->email_verified_at = now();
            $user->token = null;
            $user->save();
            return 'success';

        } catch (\Throwable$th) {
            return false;
        }

    }

    public function forgotPassword($request)
    {
        try {
            $user = User::query()->firstWhere('email', $request->email);

            if (!$user) {
                return 'invalid_email';
            }

            $user->token = Str::random(30);
            $user->save();

            Mail::to($user->email)->send(new ResetPassword($user));

            return 'success';

        } catch (\Throwable$th) {
            return false;
        }

    }

    public function resetPasswordPage($email, $token)
    {
        try {
            $user = User::query()->firstWhere('email', $email);

            if (!$user) {
                return 'invalid_email';
            }

            if ($user->token != $token) {
                return 'invalid_token';
            }

            return 'success';

        } catch (\Throwable$th) {
            return false;
        }

    }
    public function resetPassword($request)
    {
        try {
            $user = User::query()->firstWhere('email', $request->email);

            if (!$user) {
                return 'invalid_email';
            }

            if ($user->token != $request->token) {
                return 'invalid_token';
            }

            $user->password = Hash::make($request->password);
            $user->token = null;
            $user->save();

            return 'success';

        } catch (\Throwable$th) {
            return false;
        }

    }

}
