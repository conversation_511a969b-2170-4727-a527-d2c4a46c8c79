<?php

namespace App\Http\Controllers;

use App\Models\Property\PropertyFacility;
use Illuminate\Http\Request;

class PropertyFacilityController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Property\PropertyFacility  $propertyFacility
     * @return \Illuminate\Http\Response
     */
    public function show(PropertyFacility $propertyFacility)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Property\PropertyFacility  $propertyFacility
     * @return \Illuminate\Http\Response
     */
    public function edit(PropertyFacility $propertyFacility)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Property\PropertyFacility  $propertyFacility
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, PropertyFacility $propertyFacility)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Property\PropertyFacility  $propertyFacility
     * @return \Illuminate\Http\Response
     */
    public function destroy(PropertyFacility $propertyFacility)
    {
        //
    }
}
