<?php

namespace Database\Seeders;

use App\Models\BankAccount;
use Illuminate\Database\Seeder;
use App\Models\Property\Property;
use Illuminate\Support\Facades\DB;
use App\Models\Property\Transaction;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class AccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {


        // $transactions = Transaction::all();
        // foreach ($transactions  as $transaction) {

        //     $bankAccount = BankAccount::where('user_id', $transaction->tenant->user_id)->first();
        //     if($bankAccount  == null) {
        //        $bank_account_id= DB::table('bank_accounts')->insert([
        //             'user_id' =>$transaction->tenant->user_id,
        //             'name' => 'Bank Account ' . ($transaction->tenant->user_id),
        //             'branch' => 'Branch ' . ($transaction->tenant->user_id),
        //             'account_number' => '**********',
        //             'account_name' => '<PERSON>',
        //             'route_number' => '*********',
        //             'branch_address' => '123 Main St, Anytown USA',
        //             'created_at' => now(),
        //             'updated_at' => now()
        //         ]);
        //     }
        //     DB::table('accounts')->insert(
        //         [
        //             'transaction_id' => $transaction->id,
        //             'property_id' => $transaction->property_id,
        //             'property_tenant_id' => $transaction->property_tenant_id,
        //             'rental_id' => $transaction->rental_id,
        //             'bank_account_id' => $transaction->tenant->user_id??$bank_account_id,
        //             'amount' => 1000 + rand()%1000,
        //             'type' => 'rent',
        //             'date' => '2023-02-20',
        //             'note' => 'Monthly rent payment'
        //         ]
        //     );
        // }
    }
}
