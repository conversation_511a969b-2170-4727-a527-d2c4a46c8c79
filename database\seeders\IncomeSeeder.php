<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Income;
use App\Models\Category;
use Illuminate\Database\Seeder;
use App\Models\Property\PropertyTenant;
use App\Models\Property\Transaction;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class IncomeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = Category::where('type', 'income')->get();
        $transactions = Transaction::all();
        foreach ($transactions as $transaction){
            foreach ( $categories as  $category){
                Income::create(
                    [
                        'title' => $category->title,
                        'amount' => 1000 + rand() % 125120,
                        'date' => '2021-08-03',
                        'category_id' => $category->id,
                        'transaction_id' => $transaction->id,
                        'property_id' => $transaction->property_id,
                        'property_tenant_id' => $transaction->property_tenant_id,
                        'user_id' => $transaction->property->user_id,
                    ]
                );

            }

        }
    }
}
