<?php

namespace Database\Seeders;

use App\Models\Notification;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Notification::create([

            'title' => 'Item delivered',
            'description' => 'This notification is sent to confirm that the user\'s item has been delivered to their address.',

        ]);
        Notification::create([
            'title' => 'Successful login',
            'description' => 'This notification is sent to confirm that the user has successfully logged into their account.',

        ]);
        Notification::create([
            'title' => 'Password reset link sent',
            'description' => 'This notification is sent to confirm that the user\'s password reset link has been sent to their email.',

        ]);
    }
}
