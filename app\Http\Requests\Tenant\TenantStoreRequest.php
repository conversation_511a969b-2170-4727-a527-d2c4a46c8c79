<?php

namespace App\Http\Requests\Tenant;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class TenantStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(Request $r)
    {
        return [
            'name' => 'required',
            'email' => 'required',
            'email' => 'required',
            'phone' => 'required',
            'permanent_address' => 'required',
            'city' => 'required',
            'state' => 'required',
            'zip_code' => 'required',
            'occupation' => 'required',
            'designation' => 'required',
            'nid' => 'required',
            'image' => 'image|mimes:jpg,png,jpeg,gif,svg|max:2048',
            'status' => 'required',
        ];
    }
}
