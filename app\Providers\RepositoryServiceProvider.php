<?php

namespace App\Providers;

use App\Interfaces\RoleInterface;
use App\Interfaces\UserInterface;
use App\Interfaces\AboutInterface;
use App\Interfaces\BlogsInterface;
use App\Interfaces\RentalInterface;
use App\Interfaces\TenantInterface;
use App\Interfaces\CountryInterface;
use App\Interfaces\FeatureInterface;
use App\Interfaces\SettingInterface;
use App\Repositories\RoleRepository;
use App\Repositories\UserRepository;
use App\Interfaces\CategoryInterface;
use App\Interfaces\FlagIconInterface;
use App\Interfaces\HomePageInterface;
use App\Interfaces\LanguageInterface;
use App\Interfaces\PartnersInterface;
use App\Interfaces\PropertyInterface;
use App\Repositories\AboutRepository;
use App\Repositories\BlogsRepository;
use App\Interfaces\HowItWorkInterface;
use App\Repositories\RentalRepository;
use App\Repositories\TenantRepository;
use App\Interfaces\PermissionInterface;
use App\Repositories\CountryRepository;
use App\Repositories\FeatureRepository;
use App\Repositories\SettingRepository;
use Illuminate\Support\ServiceProvider;
use App\Interfaces\HeroSectionInterface;
use App\Interfaces\TestimonialInterface;
use App\Repositories\CategoryRepository;
use App\Repositories\FlagIconRepository;
use App\Repositories\HomePageRepository;
use App\Repositories\LanguageRepository;
use App\Repositories\PartnersRepository;
use App\Repositories\PropertyRepository;
use App\Repositories\HowItWorkRepository;
use App\Interfaces\AdvertisementInterface;
use App\Interfaces\BusinessModelInterface;
use App\Repositories\PermissionRepository;
use App\Interfaces\BlogCategoriesInterface;
use App\Interfaces\GeneralSettingInterface;
use App\Repositories\HeroSectionRepository;
use App\Repositories\TestimonialRepository;
use App\Interfaces\PropertyCategoryInterface;
use App\Repositories\AdvertisementRepository;
use App\Repositories\BusinessModelRepository;
use App\Repositories\AuthenticationRepository;
use App\Repositories\BlogCategoriesRepository;
use App\Repositories\GeneralSettingRepository;
use App\Repositories\PropertyCategoryRepository;
use App\Interfaces\PropertyFacilityTypeInterface;
use App\Repositories\PropertyFacilityTypeRepository;
use App\Interfaces\AuthenticationRepositoryInterface;



class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(AuthenticationRepositoryInterface::class, AuthenticationRepository::class);
        $this->app->bind(RoleInterface::class,                     RoleRepository::class);
        $this->app->bind(PermissionInterface::class,               PermissionRepository::class);
        $this->app->bind(UserInterface::class,                     UserRepository::class);
        $this->app->bind(BlogsInterface::class,                    BlogsRepository::class);
        $this->app->bind(BlogCategoriesInterface::class,           BlogCategoriesRepository::class);
        $this->app->bind(GeneralSettingInterface::class,           GeneralSettingRepository::class);
        $this->app->bind(SettingInterface::class,                  SettingRepository::class);
        $this->app->bind(LanguageInterface::class,                 LanguageRepository::class);
        $this->app->bind(FlagIconInterface::class,                 FlagIconRepository::class);
        $this->app->bind(TestimonialInterface::class,              TestimonialRepository::class);
        $this->app->bind(HowItWorkInterface::class,                HowItWorkRepository::class);
        $this->app->bind(CategoryInterface::class,                 CategoryRepository::class);
        $this->app->bind(CountryInterface::class,                  CountryRepository::class);
        $this->app->bind(HomePageInterface::class,                 HomePageRepository::class);
        $this->app->bind(AboutInterface::class,                    AboutRepository::class);
        $this->app->bind(PartnersInterface::class,                 PartnersRepository::class);
        $this->app->bind(BusinessModelInterface::class,            BusinessModelRepository::class);
        $this->app->bind(FeatureInterface::class,                  FeatureRepository::class);
        $this->app->bind(HeroSectionInterface::class,              HeroSectionRepository::class);
        $this->app->bind(TenantInterface::class,                   TenantRepository::class);
        $this->app->bind(PropertyInterface::class,                 PropertyRepository::class);
        $this->app->bind(RentalInterface::class,                   RentalRepository::class);
        $this->app->bind(PropertyCategoryInterface::class,         PropertyCategoryRepository::class);
        $this->app->bind(PropertyFacilityTypeInterface::class,     PropertyFacilityTypeRepository::class);
        $this->app->bind(AdvertisementInterface::class,            AdvertisementRepository::class);
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
