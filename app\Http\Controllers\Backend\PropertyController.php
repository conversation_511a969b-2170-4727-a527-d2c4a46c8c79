<?php

namespace App\Http\Controllers\Backend;

use App\Models\City;
use App\Models\State;
use Illuminate\Http\Request;
use App\Models\Locations\Country;
use App\Models\Locations\Upazila;
use App\Models\Property\Property;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Interfaces\PropertyInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Schema;
use App\Interfaces\PermissionInterface;
use App\Models\Property\PropertyCategory;
use App\Models\Property\PropertyFacility;
use App\Models\Property\PropertyLocation;
use App\Models\Property\PropertyFacilityType;
use App\Http\Requests\Property\PropertyStoreRequest;
use App\Http\Requests\Property\PropertyUpdateRequest;

class PropertyController extends Controller
{
    private $property;
    private $permission;

    public function __construct(PropertyInterface $propertyInterface, PermissionInterface $permissionInterface)
    {

        if (!Schema::hasTable('settings') && !Schema::hasTable('users')) {
            abort(400);
        }
        $this->property = $propertyInterface;
        $this->permission = $permissionInterface;
    }

    public function index()
    {
        $data['title'] = ___('common.property');
        $data['property'] = (Auth::user()->role_id == 1) ? $this->property->getAll() : $this->property->getCreatedBy();
        return view('backend.property.index', compact('data'));
    }


    public function create()
    {


        $cacheKey = 'property_data'; // Define a cache key

        // Check if data exists in cache
        if (Cache::has($cacheKey)) {
            // Retrieve data from cache
            $data = Cache::get($cacheKey);
        } else {
            // Fetch data from the database or perform expensive operation
            $data['title'] = __('common.create_property');
            $data['property_categories'] = PropertyCategory::all();
            $data['property_facilities'] = PropertyFacilityType::all();
            $data['property_categories'] = PropertyCategory::all();
            $data['countries'] = Country::all();
            $data['permissions'] = $this->permission->all();

            // Store the data in the cache
            Cache::put($cacheKey, $data, 60); // You can set an optional expiration time if needed
        }

        return view('backend.property.create', compact('data'));
    }


    public function store(PropertyStoreRequest $request)
    {


        $result = $this->property->store($request);
        if ($result) {
            return redirect()->route('properties.index')->with('success', ___('alert.property_created_successfully'));
        }
        return redirect()->route('properties.index')->with('danger', ___('alert.something_went_wrong_please_try_again'));
    }

    public function edit($id)
    {
        $data['property_categories'] = PropertyCategory::all();
        $data['property'] = $this->property->show($id);
        $data['title'] = ___('common.properties');
        $data['permissions'] = $this->permission->all();
        return view('backend.property.edit', compact('data'));
    }

    public function update(Request $request, $id, $type)
    {
        $result = $this->property->update($request, $id, $type);
        if ($result) {
            return redirect()->back()->with('success', ___('alert.Updated_successfully'));
        }
        return redirect()->back()->with('danger', ___('alert.something_went_wrong_please_try_again'));
    }

    public function delete($id)
    {
        if ($this->property->destroy($id)) :
            $success[0] = "Deleted Successfully";
            $success[1] = "Success";
            $success[2] = "Deleted";
        else :
            $success[0] = "Something went wrong, please try again.";
            $success[1] = 'error';
            $success[2] = "oops";
        endif;
        return response()->json($success);
    }
    public function details($id)
    {
        $data['title'] = "Details";
        $data['property'] = Property::find($id);

        return view('backend.property.details', compact('data'));
    }
    public function detailsType($id, $type)
    {

        $data['title'] = "Details";
        $data['property'] = Property::find($id);
        $data['property_categories'] = PropertyCategory::all();
        $data['property_location'] = PropertyLocation::where('property_id', $id)->first();
        $data['state'] = State::where('country_id', $data['property']->location->country_id)->get();
        $data['city'] = City::where('state_id', $data['property']->location->state_id)->get();
        $data['countries'] = Country::all();
        switch ($type) {
            case 'basicInfo':
                $data['title'] = 'Basic Info';
                return view('backend.property.basicInfo', compact('data'));
                break;

            case 'gallery':
                $data['title'] = 'Gallery';
                $data['galleries'] = $data['property']->galleries->where('type', 'gallery');
                return view('backend.property.Gallery', compact('data'));
                break;
            case 'tenant':
                $data['title'] = 'Tenant';
                return view('backend.property.Tenant', compact('data'));
                break;

            case 'facility':
                $data['title'] = 'Facility';
                $data['property'] = Property::find($id);
                $data['facilities'] = PropertyFacility::where('property_id', $id)->get();
                $data['type'] = PropertyFacilityType::all();
                return view('backend.property.Facility', compact('data'));
                break;


            case 'floorPlan':
                $data['title'] = 'FloorPlan';
                $data['floorPlans'] = $data['property']->floorPlans->where('type', 'floor_plan');
                return view('backend.property.FloorPlan', compact('data'));
                break;
        }
    }


    public function deleteImage($id)
    {
        $result = $this->property->deleteImage($id);
        if ($result) {
            return redirect()->back()->with('success', ___('alert.image_deleted_successfully'));
        }
        return redirect()->back()->with('danger', ___('alert.something_went_wrong_please_try_again'));
    }
    public function facilityDelete($id)
    {
        $result = $this->property->facilityDestroy($id);
        if ($result) {
            return redirect()->back()->with('success', ___('alert.Facility Delete Successfully'));
        }
        return redirect()->back()->with('danger', ___('alert.something_went_wrong_please_try_again'));
    }
    public function getStates($countryId)
    {

        $states = State::where('country_id', $countryId)->get();

        return response()->json(['states' => $states]);
    }

    public function getCities($stateId)
    {
        $cities = City::where('state_id', $stateId)->get();

        return response()->json(['cities' => $cities]);
    }
}
